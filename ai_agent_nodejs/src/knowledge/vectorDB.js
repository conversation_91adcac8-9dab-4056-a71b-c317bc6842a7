/**
 * Vector Database Manager for TessellCS AI Agent (Node.js)
 * Handles vector embeddings and similarity search
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const logger = require('../utils/logger');

class VectorDBManager {
    constructor(dbPath = './vectordb') {
        this.dbPath = dbPath;
        this.documents = new Map(); // In-memory storage for simplicity
        this.embeddings = new Map(); // Store embeddings
        this.metadata = new Map(); // Store metadata
        this.isInitialized = false;
    }

    /**
     * Initialize the vector database
     */
    async initialize() {
        try {
            await fs.mkdir(this.dbPath, { recursive: true });
            await this.loadDatabase();
            this.isInitialized = true;
            logger.info('Vector database initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize vector database:', error);
            throw error;
        }
    }

    /**
     * Generate simple text embeddings (TF-IDF style)
     * In production, you'd use a proper embedding service
     */
    generateEmbedding(text) {
        const words = text.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 2);
        
        // Create a simple word frequency vector
        const wordFreq = {};
        words.forEach(word => {
            wordFreq[word] = (wordFreq[word] || 0) + 1;
        });
        
        // Convert to normalized vector (simplified)
        const totalWords = words.length;
        const embedding = {};
        
        Object.keys(wordFreq).forEach(word => {
            embedding[word] = wordFreq[word] / totalWords;
        });
        
        return embedding;
    }

    /**
     * Calculate cosine similarity between two embeddings
     */
    cosineSimilarity(embedding1, embedding2) {
        const keys1 = new Set(Object.keys(embedding1));
        const keys2 = new Set(Object.keys(embedding2));
        const allKeys = new Set([...keys1, ...keys2]);
        
        let dotProduct = 0;
        let norm1 = 0;
        let norm2 = 0;
        
        for (const key of allKeys) {
            const val1 = embedding1[key] || 0;
            const val2 = embedding2[key] || 0;
            
            dotProduct += val1 * val2;
            norm1 += val1 * val1;
            norm2 += val2 * val2;
        }
        
        if (norm1 === 0 || norm2 === 0) {
            return 0;
        }
        
        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * Add documents to the database
     */
    async addDocuments(documents) {
        try {
            for (const doc of documents) {
                const id = this.generateDocId(doc);
                const embedding = this.generateEmbedding(doc.content);
                
                this.documents.set(id, doc.content);
                this.embeddings.set(id, embedding);
                this.metadata.set(id, doc.metadata);
            }
            
            await this.saveDatabase();
            logger.debug(`Added ${documents.length} documents to vector database`);
            
        } catch (error) {
            logger.error('Error adding documents to vector database:', error);
            throw error;
        }
    }

    /**
     * Generate unique document ID
     */
    generateDocId(doc) {
        const content = JSON.stringify({
            content: doc.content.substring(0, 100),
            file_path: doc.metadata.file_path,
            chunk_id: doc.metadata.chunk_id || 0
        });
        
        return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
    }

    /**
     * Search for similar documents
     */
    async search(query, options = {}) {
        const {
            limit = 5,
            filters = {},
            minScore = 0.0
        } = options;
        
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }
            
            const queryEmbedding = this.generateEmbedding(query);
            const results = [];
            
            for (const [id, embedding] of this.embeddings.entries()) {
                const metadata = this.metadata.get(id);
                
                // Apply filters
                let matchesFilters = true;
                for (const [key, value] of Object.entries(filters)) {
                    if (metadata[key] !== value) {
                        matchesFilters = false;
                        break;
                    }
                }
                
                if (!matchesFilters) continue;
                
                const score = this.cosineSimilarity(queryEmbedding, embedding);
                
                if (score >= minScore) {
                    results.push({
                        id: id,
                        content: this.documents.get(id),
                        metadata: metadata,
                        score: score
                    });
                }
            }
            
            // Sort by score (descending) and limit results
            results.sort((a, b) => b.score - a.score);
            return results.slice(0, limit);
            
        } catch (error) {
            logger.error('Vector search failed:', error);
            return [];
        }
    }

    /**
     * Get documents by metadata filters
     */
    async getByMetadata(filters) {
        try {
            const results = [];
            
            for (const [id, metadata] of this.metadata.entries()) {
                let matches = true;
                
                for (const [key, value] of Object.entries(filters)) {
                    if (metadata[key] !== value) {
                        matches = false;
                        break;
                    }
                }
                
                if (matches) {
                    results.push({
                        id: id,
                        content: this.documents.get(id),
                        metadata: metadata
                    });
                }
            }
            
            return results;
            
        } catch (error) {
            logger.error('Error getting documents by metadata:', error);
            return [];
        }
    }

    /**
     * Delete documents by metadata filters
     */
    async deleteByMetadata(filters) {
        try {
            const toDelete = [];
            
            for (const [id, metadata] of this.metadata.entries()) {
                let matches = true;
                
                for (const [key, value] of Object.entries(filters)) {
                    if (metadata[key] !== value) {
                        matches = false;
                        break;
                    }
                }
                
                if (matches) {
                    toDelete.push(id);
                }
            }
            
            for (const id of toDelete) {
                this.documents.delete(id);
                this.embeddings.delete(id);
                this.metadata.delete(id);
            }
            
            if (toDelete.length > 0) {
                await this.saveDatabase();
                logger.debug(`Deleted ${toDelete.length} documents from vector database`);
            }
            
            return toDelete.length;
            
        } catch (error) {
            logger.error('Error deleting documents:', error);
            return 0;
        }
    }

    /**
     * Get total document count
     */
    async count() {
        return this.documents.size;
    }

    /**
     * Get unique values for a metadata field
     */
    async getUniqueValues(field) {
        const values = new Set();
        
        for (const metadata of this.metadata.values()) {
            if (metadata[field]) {
                values.add(metadata[field]);
            }
        }
        
        return Array.from(values);
    }

    /**
     * Save database to disk
     */
    async saveDatabase() {
        try {
            const data = {
                documents: Object.fromEntries(this.documents),
                embeddings: Object.fromEntries(this.embeddings),
                metadata: Object.fromEntries(this.metadata),
                version: '1.0.0',
                timestamp: new Date().toISOString()
            };
            
            const dbFile = path.join(this.dbPath, 'database.json');
            await fs.writeFile(dbFile, JSON.stringify(data, null, 2));
            
        } catch (error) {
            logger.error('Error saving vector database:', error);
            throw error;
        }
    }

    /**
     * Load database from disk
     */
    async loadDatabase() {
        try {
            const dbFile = path.join(this.dbPath, 'database.json');
            
            try {
                const data = JSON.parse(await fs.readFile(dbFile, 'utf8'));
                
                this.documents = new Map(Object.entries(data.documents || {}));
                this.embeddings = new Map(Object.entries(data.embeddings || {}));
                this.metadata = new Map(Object.entries(data.metadata || {}));
                
                logger.info(`Loaded ${this.documents.size} documents from vector database`);
                
            } catch (readError) {
                // Database file doesn't exist, start fresh
                logger.info('No existing vector database found, starting fresh');
            }
            
        } catch (error) {
            logger.error('Error loading vector database:', error);
            throw error;
        }
    }

    /**
     * Clear all data
     */
    async clear() {
        try {
            this.documents.clear();
            this.embeddings.clear();
            this.metadata.clear();
            
            await this.saveDatabase();
            logger.info('Vector database cleared');
            
        } catch (error) {
            logger.error('Error clearing vector database:', error);
            throw error;
        }
    }

    /**
     * Get database statistics
     */
    async getStats() {
        return {
            total_documents: this.documents.size,
            total_embeddings: this.embeddings.size,
            storage_path: this.dbPath,
            is_initialized: this.isInitialized
        };
    }
}

module.exports = { VectorDBManager };