/**
 * Repository Indexer for TessellCS AI Agent (Node.js)
 * Handles repository content indexing and semantic search
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const simpleGit = require('simple-git');
const { VectorDBManager } = require('./vectorDB');
const logger = require('../utils/logger');

class RepositoryIndexer {
    constructor(repoPath, dbPath = './vectordb') {
        this.repoPath = path.resolve(repoPath);
        this.dbPath = dbPath;
        this.vectorDB = new VectorDBManager(dbPath);
        this.git = simpleGit(repoPath);
        
        // File extensions to index
        this.indexableExtensions = new Set([
            '.sh', '.py', '.sql', '.md', '.txt', '.conf', 
            '.json', '.ini', '.yaml', '.yml', '.js', '.ts'
        ]);
        
        // Categories for file classification
        this.categories = {
            'database_patching': ['database_patching', 'patch', 'patching'],
            'migration': ['migration', 'migrate', 'migration_flows'],
            'mysql': ['mysql', 'mariadb'],
            'oracle': ['oracle', 'ora', 'rman'],
            'postgres': ['postgres', 'postgresql', 'pg'],
            'mssql': ['mssql', 'sqlserver', 'sql server'],
            'cloud_tasks': ['cloud_tasks', 'aws', 'azure', 'gcp'],
            'monitoring': ['metrics', 'monitoring', 'observability'],
            'backup': ['backup', 'restore', 'dump'],
            'ssl': ['ssl', 'tls', 'cert', 'certificate'],
            'documentation': ['readme', 'doc', '.md'],
            'configuration': ['.conf', '.json', '.ini', 'config'],
            'script': ['.sh', '.py', '.sql']
        };
    }

    /**
     * Initialize the vector database
     */
    async initialize() {
        try {
            await this.vectorDB.initialize();
            logger.info('Repository indexer initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize repository indexer:', error);
            throw error;
        }
    }

    /**
     * Generate hash for file content
     */
    async getFileHash(filePath) {
        try {
            const content = await fs.readFile(filePath);
            return crypto.createHash('md5').update(content).digest('hex');
        } catch (error) {
            logger.error(`Error generating hash for ${filePath}:`, error);
            return null;
        }
    }

    /**
     * Extract metadata from file
     */
    async extractMetadata(filePath) {
        const relativePath = path.relative(this.repoPath, filePath);
        const stats = await fs.stat(filePath);
        
        const metadata = {
            file_path: relativePath,
            file_name: path.basename(filePath),
            file_type: path.extname(filePath).toLowerCase(),
            category: this.categorizeFile(relativePath),
            size: stats.size,
            modified: stats.mtime.getTime()
        };

        // Add git information if available
        try {
            const gitLog = await this.git.log({ file: relativePath, maxCount: 1 });
            if (gitLog.latest) {
                const commit = gitLog.latest;
                metadata.last_commit = commit.hash;
                metadata.last_author = commit.author_name;
                metadata.commit_date = new Date(commit.date).getTime();
            }
        } catch (error) {
            // Git info not available, continue without it
            logger.debug(`No git info for ${relativePath}:`, error.message);
        }

        return metadata;
    }

    /**
     * Categorize file based on path and content
     */
    categorizeFile(relativePath) {
        const pathLower = relativePath.toLowerCase();
        
        for (const [category, keywords] of Object.entries(this.categories)) {
            if (keywords.some(keyword => pathLower.includes(keyword))) {
                return category;
            }
        }
        
        return 'general';
    }

    /**
     * Check if file should be indexed
     */
    shouldIndexFile(filePath) {
        const fileName = path.basename(filePath);
        
        // Skip hidden files
        if (fileName.startsWith('.')) {
            return false;
        }
        
        // Skip binary files
        const binaryExtensions = new Set([
            '.pdf', '.png', '.jpg', '.jpeg', '.gif', 
            '.zip', '.tar', '.gz', '.exe', '.bin'
        ]);
        
        const ext = path.extname(filePath).toLowerCase();
        if (binaryExtensions.has(ext)) {
            return false;
        }
        
        // Include indexable extensions
        if (this.indexableExtensions.has(ext)) {
            return true;
        }
        
        // Include files without extensions that might be scripts
        if (!ext) {
            return this.isLikelyScript(filePath);
        }
        
        return false;
    }

    /**
     * Check if file without extension is likely a script
     */
    async isLikelyScript(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            const firstLine = content.split('\n')[0];
            return firstLine.startsWith('#!');
        } catch (error) {
            return false;
        }
    }

    /**
     * Split content into chunks for indexing
     */
    splitContent(content, chunkSize = 1000, overlap = 200) {
        if (content.length <= chunkSize) {
            return [content];
        }

        const chunks = [];
        let start = 0;
        
        while (start < content.length) {
            const end = Math.min(start + chunkSize, content.length);
            const chunk = content.slice(start, end);
            chunks.push(chunk);
            
            if (end === content.length) break;
            start = end - overlap;
        }
        
        return chunks;
    }

    /**
     * Process a single file into documents
     */
    async processFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            const metadata = await this.extractMetadata(filePath);
            
            // For small files, keep as single document
            if (content.length <= 1000) {
                return [{
                    content: content,
                    metadata: metadata
                }];
            }
            
            // Split larger files into chunks
            const chunks = this.splitContent(content);
            return chunks.map((chunk, index) => ({
                content: chunk,
                metadata: {
                    ...metadata,
                    chunk_id: index,
                    total_chunks: chunks.length
                }
            }));
            
        } catch (error) {
            logger.error(`Error processing file ${filePath}:`, error);
            return [];
        }
    }

    /**
     * Get all files recursively from directory
     */
    async getAllFiles(dirPath, files = []) {
        const entries = await fs.readdir(dirPath, { withFileTypes: true });
        
        for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);
            
            if (entry.isDirectory()) {
                // Skip certain directories
                if (!entry.name.startsWith('.') && 
                    !['node_modules', '__pycache__', 'venv', '.git'].includes(entry.name)) {
                    await this.getAllFiles(fullPath, files);
                }
            } else if (entry.isFile()) {
                files.push(fullPath);
            }
        }
        
        return files;
    }

    /**
     * Index entire repository
     */
    async indexRepository(forceReindex = false) {
        const stats = { processed: 0, indexed: 0, errors: 0, skipped: 0 };
        
        logger.info(`Starting repository indexing: ${this.repoPath}`);
        
        try {
            const allFiles = await this.getAllFiles(this.repoPath);
            const filesToIndex = allFiles.filter(file => this.shouldIndexFile(file));
            
            logger.info(`Found ${filesToIndex.length} files to process`);
            
            for (const filePath of filesToIndex) {
                stats.processed++;
                
                try {
                    const relativePath = path.relative(this.repoPath, filePath);
                    
                    // Check if file needs reindexing
                    if (!forceReindex) {
                        const fileHash = await this.getFileHash(filePath);
                        const existing = await this.vectorDB.getByMetadata({ file_path: relativePath });
                        
                        if (existing.length > 0 && existing[0].metadata.file_hash === fileHash) {
                            stats.skipped++;
                            continue;
                        }
                    }
                    
                    // Process file
                    const documents = await this.processFile(filePath);
                    if (documents.length === 0) {
                        continue;
                    }
                    
                    // Remove existing entries for this file
                    await this.vectorDB.deleteByMetadata({ file_path: relativePath });
                    
                    // Add file hash to metadata
                    const fileHash = await this.getFileHash(filePath);
                    documents.forEach(doc => {
                        doc.metadata.file_hash = fileHash;
                    });
                    
                    // Add to vector database
                    await this.vectorDB.addDocuments(documents);
                    
                    stats.indexed++;
                    
                    if (stats.processed % 10 === 0) {
                        logger.info(`Processed ${stats.processed}/${filesToIndex.length} files`);
                    }
                    
                } catch (error) {
                    logger.error(`Error processing ${filePath}:`, error);
                    stats.errors++;
                }
            }
            
            logger.info(`Indexing complete:`, stats);
            return stats;
            
        } catch (error) {
            logger.error('Repository indexing failed:', error);
            throw error;
        }
    }

    /**
     * Search indexed content
     */
    async search(query, options = {}) {
        const {
            nResults = 5,
            category = null,
            minScore = 0.0
        } = options;
        
        try {
            const filters = {};
            if (category) {
                filters.category = category;
            }
            
            const results = await this.vectorDB.search(query, {
                limit: nResults,
                filters: filters,
                minScore: minScore
            });
            
            return results.map(result => ({
                content: result.content,
                metadata: result.metadata,
                score: result.score
            }));
            
        } catch (error) {
            logger.error('Search failed:', error);
            return [];
        }
    }

    /**
     * Get full content of a specific file
     */
    async getFileContent(relativePath) {
        try {
            const fullPath = path.join(this.repoPath, relativePath);
            const content = await fs.readFile(fullPath, 'utf8');
            return content;
        } catch (error) {
            logger.error(`Error reading file ${relativePath}:`, error);
            return null;
        }
    }

    /**
     * Get all scripts in a specific category
     */
    async getScriptsByCategory(category) {
        try {
            const results = await this.vectorDB.getByMetadata({
                category: category,
                file_type: '.sh'
            });
            
            return results.map(result => ({
                path: result.metadata.file_path,
                name: result.metadata.file_name,
                content: result.content
            }));
            
        } catch (error) {
            logger.error(`Error getting scripts for category ${category}:`, error);
            return [];
        }
    }

    /**
     * Get repository statistics
     */
    async getStats() {
        try {
            const totalDocs = await this.vectorDB.count();
            const categories = await this.vectorDB.getUniqueValues('category');
            const fileTypes = await this.vectorDB.getUniqueValues('file_type');
            
            return {
                total_documents: totalDocs,
                categories: categories,
                file_types: fileTypes,
                last_updated: new Date().toISOString()
            };
            
        } catch (error) {
            logger.error('Error getting repository stats:', error);
            return null;
        }
    }
}

module.exports = { RepositoryIndexer };