/**
 * Express.js Server for TessellCS AI Agent (Node.js)
 * REST API server with authentication and security
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

const { TessellAgent } = require('./agent/tessellAgent');
const { SecurityManager, InputSanitizer, SecurityLevel } = require('./security/securityManager');
const logger = require('./utils/logger');

class TessellAgentServer {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.host = process.env.HOST || '0.0.0.0';
        
        // Initialize components
        this.securityManager = new SecurityManager();
        this.inputSanitizer = new InputSanitizer();
        
        // Initialize agent with LLM configuration
        const repoPath = process.env.TESSELL_REPO_PATH || path.join(__dirname, '../../..');
        const llmConfig = {
            engine: process.env.LLM_ENGINE || 'template',
            apiKey: this._getLLMApiKey(),
            model: this._getLLMModel(),
            ollamaUrl: process.env.OLLAMA_URL || 'http://localhost:11434',
            maxTokens: parseInt(process.env.LLM_MAX_TOKENS) || 500,
            temperature: parseFloat(process.env.LLM_TEMPERATURE) || 0.7
        };
        this.agent = new TessellAgent(repoPath, llmConfig);
        
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
    }

    /**
     * Get API key based on LLM engine
     */
    _getLLMApiKey() {
        const engine = process.env.LLM_ENGINE || 'template';
        switch (engine) {
            case 'openai':
                return process.env.OPENAI_API_KEY;
            case 'anthropic':
                return process.env.ANTHROPIC_API_KEY;
            case 'ollama':
            case 'template':
            default:
                return null;
        }
    }

    /**
     * Get model based on LLM engine
     */
    _getLLMModel() {
        const engine = process.env.LLM_ENGINE || 'template';
        switch (engine) {
            case 'openai':
                return process.env.OPENAI_MODEL || 'gpt-3.5-turbo';
            case 'anthropic':
                return process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229';
            case 'ollama':
                return process.env.OLLAMA_MODEL || 'llama2:7b';
            case 'template':
            default:
                return null;
        }
    }

    /**
     * Setup Express middleware
     */
    setupMiddleware() {
        // Security headers
        this.app.use(helmet());
        
        // CORS
        this.app.use(cors({
            origin: process.env.CORS_ORIGIN || '*',
            credentials: true
        }));
        
        // Request logging
        this.app.use(morgan('combined', {
            stream: { write: message => logger.info(message.trim()) }
        }));
        
        // Rate limiting
        const limiter = rateLimit({
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 100, // limit each IP to 100 requests per windowMs
            message: 'Too many requests from this IP, please try again later.',
            standardHeaders: true,
            legacyHeaders: false
        });
        this.app.use('/api/', limiter);
        
        // Body parsing
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true }));
        
        // Static files (for frontend)
        this.app.use(express.static(path.join(__dirname, '../client/build')));
    }

    /**
     * Authentication middleware
     */
    authenticateToken(req, res, next) {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return res.status(401).json({ error: 'Access token required' });
        }

        const session = this.securityManager.validateSession(token);
        if (!session) {
            return res.status(401).json({ error: 'Invalid or expired token' });
        }

        req.session = session;
        req.user = { id: session.userId, securityLevel: session.securityLevel };
        next();
    }

    /**
     * Setup API routes
     */
    setupRoutes() {
        // Health check
        this.app.get('/api/health', (req, res) => {
            res.json({
                status: 'healthy',
                version: '1.0.0',
                timestamp: new Date().toISOString()
            });
        });

        // Authentication routes
        this.app.post('/api/login', async (req, res) => {
            try {
                const { username, password } = req.body;

                if (!username || !password) {
                    return res.status(400).json({ error: 'Username and password required' });
                }

                // Demo authentication (replace with real auth)
                const demoUsers = {
                    admin: { password: 'admin123', level: SecurityLevel.ADMIN },
                    user: { password: 'user123', level: SecurityLevel.STANDARD },
                    guest: { password: 'guest123', level: SecurityLevel.RESTRICTED }
                };

                const user = demoUsers[username];
                if (!user || user.password !== password) {
                    this.securityManager.logSecurityEvent(
                        'auth', username, 'login_failed', 'authentication',
                        'medium', false, { reason: 'invalid_credentials' }, req.ip
                    );
                    return res.status(401).json({ error: 'Invalid credentials' });
                }

                const token = await this.securityManager.createSession(username, user.level);

                this.securityManager.logSecurityEvent(
                    'auth', username, 'login_success', 'authentication',
                    'low', true, { level: user.level }, req.ip
                );

                res.json({
                    token: token,
                    user_id: username,
                    security_level: user.level
                });

            } catch (error) {
                logger.error('Login error:', error);
                res.status(500).json({ error: 'Login failed' });
            }
        });

        // Query processing
        this.app.post('/api/query', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const { query, execute_if_safe = false, context = {} } = req.body;

                if (!query) {
                    return res.status(400).json({ error: 'Query is required' });
                }

                // Validate input
                const validation = this.inputSanitizer.validateQuery(query);
                if (!validation.valid) {
                    this.securityManager.logSecurityEvent(
                        'query', req.user.id, 'invalid_query', 'input_validation',
                        'medium', false, { query: query.substring(0, 100), reason: validation.reason }, req.ip
                    );
                    return res.status(400).json({ error: `Invalid query: ${validation.reason}` });
                }

                // Check rate limits
                if (!this.securityManager.checkRateLimit(req.session)) {
                    return res.status(429).json({ error: 'Rate limit exceeded' });
                }

                // Process query
                const response = await this.agent.processQuery(query, execute_if_safe);

                // Log successful query
                this.securityManager.logSecurityEvent(
                    'query', req.user.id, 'query_processed', 'agent_interaction',
                    'low', true, { query: query.substring(0, 100), confidence: response.confidence }, req.ip
                );

                res.json({
                    message: response.message,
                    suggested_scripts: response.suggestedScripts,
                    execution_results: response.executionResults,
                    requires_approval: response.requiresApproval,
                    confidence: response.confidence
                });

            } catch (error) {
                logger.error('Query processing error:', error);
                this.securityManager.logSecurityEvent(
                    'query', req.user.id, 'query_error', 'agent_interaction',
                    'high', false, { error: error.message }, req.ip
                );
                res.status(500).json({ error: 'Query processing failed' });
            }
        });

        // Script execution
        this.app.post('/api/execute-script', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const { script_path, args = [], force_approval = false } = req.body;

                if (!script_path) {
                    return res.status(400).json({ error: 'Script path is required' });
                }

                // Sanitize script path
                const cleanPath = this.inputSanitizer.sanitizeFilename(script_path);

                // Get script info for risk assessment
                const scriptInfo = await this.agent.executor.getScriptInfo(cleanPath);
                if (scriptInfo.error) {
                    return res.status(404).json({ error: scriptInfo.error });
                }

                const riskLevel = scriptInfo.risk_level;

                // Validate execution permissions
                const validation = this.securityManager.validateScriptExecution(
                    req.session, cleanPath, riskLevel
                );

                if (!validation.allowed) {
                    this.securityManager.logSecurityEvent(
                        'execution', req.user.id, 'script_execution_denied', cleanPath,
                        'medium', false, { reason: validation.reason, risk_level: riskLevel }, req.ip
                    );
                    return res.status(403).json({ error: validation.reason });
                }

                // Check if approval is required
                if (this.securityManager.requireApproval(req.session, 'script_execution', riskLevel) && !force_approval) {
                    this.securityManager.logSecurityEvent(
                        'execution', req.user.id, 'approval_required', cleanPath,
                        'medium', true, { risk_level: riskLevel }, req.ip
                    );
                    return res.json({
                        requires_approval: true,
                        message: `Script execution requires approval due to ${riskLevel} risk level`,
                        script_info: scriptInfo
                    });
                }

                // Execute script
                const result = await this.agent.executeScriptWithApproval(cleanPath, args);

                this.securityManager.logSecurityEvent(
                    'execution', req.user.id, 'script_executed', cleanPath,
                    riskLevel, result.success, { exit_code: result.exit_code }, req.ip
                );

                res.json(result);

            } catch (error) {
                logger.error('Script execution error:', error);
                this.securityManager.logSecurityEvent(
                    'execution', req.user.id, 'script_execution_error', req.body.script_path || 'unknown',
                    'high', false, { error: error.message }, req.ip
                );
                res.status(500).json({ error: 'Script execution failed' });
            }
        });

        // List scripts
        this.app.get('/api/scripts', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const { category } = req.query;

                let scripts;
                if (category) {
                    scripts = await this.agent.indexer.getScriptsByCategory(category);
                } else {
                    // Get all scripts
                    const results = await this.agent.indexer.search('', { nResults: 100 });
                    scripts = results
                        .filter(item => item.metadata.file_type === '.sh')
                        .map(item => ({
                            path: item.metadata.file_path,
                            name: item.metadata.file_name,
                            category: item.metadata.category || 'general'
                        }));
                }

                // Add script info
                for (const script of scripts) {
                    const info = await this.agent.executor.getScriptInfo(script.path);
                    Object.assign(script, info);
                }

                res.json({ scripts });

            } catch (error) {
                logger.error('List scripts error:', error);
                res.status(500).json({ error: 'Failed to list scripts' });
            }
        });

        // Get script info
        this.app.get('/api/script/:scriptPath(*)/info', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const scriptPath = req.params.scriptPath;
                const cleanPath = this.inputSanitizer.sanitizeFilename(scriptPath);

                const info = await this.agent.getScriptDocumentation(cleanPath);
                res.json(info);

            } catch (error) {
                logger.error('Get script info error:', error);
                res.status(500).json({ error: 'Failed to get script info' });
            }
        });

        // Audit log (admin only)
        this.app.get('/api/audit-log', this.authenticateToken.bind(this), (req, res) => {
            try {
                if (req.user.securityLevel !== SecurityLevel.ADMIN) {
                    return res.status(403).json({ error: 'Admin access required' });
                }

                const hours = parseInt(req.query.hours) || 24;
                const logs = this.securityManager.getAuditLog(null, hours);

                res.json({ audit_log: logs });

            } catch (error) {
                logger.error('Audit log error:', error);
                res.status(500).json({ error: 'Failed to get audit log' });
            }
        });

        // Initialize knowledge base
        this.app.post('/api/initialize', this.authenticateToken.bind(this), async (req, res) => {
            try {
                if (![SecurityLevel.ADMIN, SecurityLevel.ELEVATED].includes(req.user.securityLevel)) {
                    return res.status(403).json({ error: 'Elevated access required' });
                }

                const { force_reindex = false } = req.body;
                const stats = await this.agent.initializeKnowledgeBase(force_reindex);

                this.securityManager.logSecurityEvent(
                    'admin', req.user.id, 'knowledge_base_updated', 'system',
                    'low', true, stats, req.ip
                );

                res.json({ message: 'Knowledge base updated', stats });

            } catch (error) {
                logger.error('Initialize knowledge base error:', error);
                res.status(500).json({ error: 'Initialization failed' });
            }
        });

        // Get agent statistics
        this.app.get('/api/stats', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const stats = await this.agent.getStats();
                const securityStats = {
                    active_sessions: this.securityManager.getActiveSessionsCount(),
                    user_sessions: this.securityManager.getUserSessions(req.user.id).length
                };

                res.json({
                    agent: stats,
                    security: securityStats
                });

            } catch (error) {
                logger.error('Get stats error:', error);
                res.status(500).json({ error: 'Failed to get statistics' });
            }
        });

        // Serve React app for all other routes
        this.app.get('*', (req, res) => {
            res.sendFile(path.join(__dirname, '../client/build/index.html'));
        });
    }

    /**
     * Setup error handling
     */
    setupErrorHandling() {
        // 404 handler
        this.app.use((req, res) => {
            res.status(404).json({ error: 'Not found' });
        });

        // Global error handler
        this.app.use((error, req, res, next) => {
            logger.error('Unhandled error:', error);
            res.status(500).json({ error: 'Internal server error' });
        });
    }

    /**
     * Start the server
     */
    async start() {
        try {
            // Initialize components
            await this.securityManager.initialize();
            await this.agent.initialize();

            // Check if knowledge base exists and initialize if needed
            const stats = await this.agent.indexer.getStats();
            if (!stats || stats.total_documents === 0) {
                logger.info('Initializing knowledge base...');
                const indexStats = await this.agent.initializeKnowledgeBase();
                logger.info('Knowledge base initialized:', indexStats);
            } else {
                logger.info('Knowledge base already exists with', stats.total_documents, 'documents');
            }

            // Start server
            this.server = this.app.listen(this.port, this.host, () => {
                logger.info(`TessellCS AI Agent server running on http://${this.host}:${this.port}`);
                logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
                logger.info(`LLM Engine: ${process.env.LLM_ENGINE || 'template'}`);
                if (process.env.LLM_ENGINE !== 'template') {
                    logger.info(`LLM Model: ${this._getLLMModel()}`);
                }
            });

        } catch (error) {
            logger.error('Failed to start server:', error);
            process.exit(1);
        }
    }

    /**
     * Stop the server
     */
    async stop() {
        if (this.server) {
            return new Promise((resolve) => {
                this.server.close(() => {
                    logger.info('Server stopped');
                    resolve();
                });
            });
        }
    }
}

// Start server if this file is run directly
if (require.main === module) {
    const server = new TessellAgentServer();
    
    // Graceful shutdown
    process.on('SIGTERM', async () => {
        logger.info('SIGTERM received, shutting down gracefully');
        await server.stop();
        process.exit(0);
    });

    process.on('SIGINT', async () => {
        logger.info('SIGINT received, shutting down gracefully');
        await server.stop();
        process.exit(0);
    });

    server.start().catch(error => {
        logger.error('Failed to start server:', error);
        process.exit(1);
    });
}

module.exports = { TessellAgentServer };