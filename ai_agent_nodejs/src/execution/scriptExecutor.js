/**
 * Script Execution Engine for TessellCS AI Agent (Node.js)
 * Provides safe, controlled execution of repository scripts
 */

const { spawn, exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { promisify } = require('util');
const execAsync = promisify(exec);
const logger = require('../utils/logger');

const RiskLevel = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical'
};

class ExecutionResult {
    constructor(success, exitCode, stdout, stderr, executionTime, riskAssessment, warnings = []) {
        this.success = success;
        this.exitCode = exitCode;
        this.stdout = stdout;
        this.stderr = stderr;
        this.executionTime = executionTime;
        this.riskAssessment = riskAssessment;
        this.warnings = warnings;
    }
}

class ScriptValidator {
    constructor() {
        // Dangerous commands that should be blocked or require approval
        this.dangerousPatterns = {
            [RiskLevel.CRITICAL]: [
                'rm -rf /',
                'format',
                'mkfs',
                '> /dev/sd',
                'dd if=',
                'chmod 777',
                'chown root',
                'sudo su',
                'curl | sh',
                'wget | sh'
            ],
            [RiskLevel.HIGH]: [
                'rm -rf',
                'drop database',
                'truncate table',
                'delete from',
                'sudo',
                'su -',
                'passwd',
                'useradd',
                'userdel',
                'groupadd',
                'systemctl stop',
                'service stop',
                'kill -9',
                'pkill',
                'reboot',
                'shutdown'
            ],
            [RiskLevel.MEDIUM]: [
                'rm ',
                'mv ',
                'cp ',
                'chmod',
                'chown',
                'mount',
                'umount',
                'crontab',
                'systemctl',
                'service',
                'iptables'
            ]
        };

        // Safe patterns that are generally OK
        this.safePatterns = [
            'echo', 'cat', 'ls', 'grep', 'awk', 'sed', 'sort', 'uniq',
            'head', 'tail', 'wc', 'find', 'which', 'whereis', 'ps',
            'top', 'df', 'du', 'free', 'uptime', 'date', 'whoami', 'pwd', 'uname'
        ];
    }

    /**
     * Assess risk level of script content
     */
    assessRisk(scriptContent) {
        const warnings = [];
        let maxRisk = RiskLevel.LOW;

        const lines = scriptContent.toLowerCase().split('\n');

        for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine || trimmedLine.startsWith('#')) {
                continue;
            }

            // Check for dangerous patterns
            for (const [riskLevel, patterns] of Object.entries(this.dangerousPatterns)) {
                for (const pattern of patterns) {
                    if (trimmedLine.includes(pattern)) {
                        warnings.push(`Detected ${riskLevel} risk pattern: ${pattern}`);
                        
                        if (riskLevel === RiskLevel.CRITICAL || 
                            (riskLevel === RiskLevel.HIGH && maxRisk !== RiskLevel.CRITICAL)) {
                            maxRisk = riskLevel;
                        } else if (riskLevel === RiskLevel.MEDIUM && maxRisk === RiskLevel.LOW) {
                            maxRisk = riskLevel;
                        }
                    }
                }
            }
        }

        // Additional checks
        if (scriptContent.includes('eval') || scriptContent.includes('$((')) {
            warnings.push('Script contains dynamic evaluation - potential security risk');
            if (maxRisk === RiskLevel.LOW) {
                maxRisk = RiskLevel.MEDIUM;
            }
        }

        const sudoCount = (scriptContent.match(/sudo/g) || []).length;
        if (sudoCount > 3) {
            warnings.push('Multiple sudo commands detected');
            if (maxRisk === RiskLevel.LOW) {
                maxRisk = RiskLevel.MEDIUM;
            }
        }

        return { riskLevel: maxRisk, warnings };
    }

    /**
     * Validate that script is within repository bounds
     */
    validateScriptPath(scriptPath, repoPath) {
        try {
            const scriptFull = path.resolve(scriptPath);
            const repoFull = path.resolve(repoPath);
            return scriptFull.startsWith(repoFull);
        } catch (error) {
            return false;
        }
    }
}

class ScriptExecutor {
    constructor(repoPath, useDocker = false) {
        this.repoPath = path.resolve(repoPath);
        this.useDocker = useDocker;
        this.validator = new ScriptValidator();
        
        // Approved scripts cache
        this.approvedScripts = new Set();
        this.loadApprovedScripts();
    }

    /**
     * Load list of pre-approved scripts
     */
    async loadApprovedScripts() {
        try {
            const approvedFile = path.join(this.repoPath, 'ai_agent_nodejs', 'approved_scripts.json');
            const data = await fs.readFile(approvedFile, 'utf8');
            const parsed = JSON.parse(data);
            this.approvedScripts = new Set(parsed.approved || []);
        } catch (error) {
            // File doesn't exist or error reading, start with empty set
            logger.debug('No approved scripts file found, starting fresh');
        }
    }

    /**
     * Save approved scripts list
     */
    async saveApprovedScripts() {
        try {
            const approvedFile = path.join(this.repoPath, 'ai_agent_nodejs', 'approved_scripts.json');
            const dir = path.dirname(approvedFile);
            
            await fs.mkdir(dir, { recursive: true });
            await fs.writeFile(approvedFile, JSON.stringify({ 
                approved: Array.from(this.approvedScripts) 
            }, null, 2));
        } catch (error) {
            logger.error('Error saving approved scripts:', error);
        }
    }

    /**
     * Execute script in Docker container (if available)
     */
    async executeInDocker(scriptPath, args = []) {
        return new Promise((resolve, reject) => {
            const relativePath = path.relative(this.repoPath, scriptPath);
            const containerCmd = `bash /repo/${relativePath}`;
            const fullCmd = args.length > 0 ? `${containerCmd} ${args.join(' ')}` : containerCmd;

            const dockerArgs = [
                'run', '--rm',
                '--memory=256m',
                '--cpus=0.5',
                '--network=none',
                '--user=nobody',
                '-v', `${this.repoPath}:/repo:ro`,
                'ubuntu:20.04',
                'sh', '-c', fullCmd
            ];

            const startTime = Date.now();
            const dockerProcess = spawn('docker', dockerArgs);
            
            let stdout = '';
            let stderr = '';

            dockerProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            dockerProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            dockerProcess.on('close', (code) => {
                const executionTime = Date.now() - startTime;
                
                resolve(new ExecutionResult(
                    code === 0,
                    code,
                    stdout,
                    stderr,
                    executionTime,
                    RiskLevel.LOW,
                    []
                ));
            });

            dockerProcess.on('error', (error) => {
                const executionTime = Date.now() - startTime;
                
                resolve(new ExecutionResult(
                    false,
                    -1,
                    '',
                    error.message,
                    executionTime,
                    RiskLevel.HIGH,
                    [`Docker execution failed: ${error.message}`]
                ));
            });

            // Timeout after 5 minutes
            setTimeout(() => {
                dockerProcess.kill('SIGTERM');
                resolve(new ExecutionResult(
                    false,
                    -1,
                    stdout,
                    'Process timed out',
                    300000,
                    RiskLevel.HIGH,
                    ['Process timed out after 5 minutes']
                ));
            }, 300000);
        });
    }

    /**
     * Execute script natively with subprocess
     */
    async executeNative(scriptPath, args = [], timeout = 300000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const cmd = [scriptPath, ...args];
            
            const process = spawn(cmd[0], cmd.slice(1), {
                cwd: path.dirname(scriptPath),
                timeout: timeout
            });

            let stdout = '';
            let stderr = '';

            process.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            process.on('close', (code) => {
                const executionTime = Date.now() - startTime;
                
                resolve(new ExecutionResult(
                    code === 0,
                    code,
                    stdout,
                    stderr,
                    executionTime,
                    RiskLevel.MEDIUM,
                    []
                ));
            });

            process.on('error', (error) => {
                const executionTime = Date.now() - startTime;
                
                resolve(new ExecutionResult(
                    false,
                    -1,
                    '',
                    error.message,
                    executionTime,
                    RiskLevel.HIGH,
                    [`Execution failed: ${error.message}`]
                ));
            });
        });
    }

    /**
     * Main script execution method
     */
    async executeScript(scriptPath, args = [], forceApproval = false) {
        // Validate script path
        if (!this.validator.validateScriptPath(scriptPath, this.repoPath)) {
            return new ExecutionResult(
                false, -1, '', 'Script path not within repository bounds',
                0, RiskLevel.CRITICAL, ['Invalid script path']
            );
        }

        let scriptFullPath = path.resolve(scriptPath);
        
        // Try relative to repo if not absolute
        if (!await this.fileExists(scriptFullPath)) {
            scriptFullPath = path.resolve(this.repoPath, scriptPath);
        }

        if (!await this.fileExists(scriptFullPath)) {
            return new ExecutionResult(
                false, -1, '', 'Script not found', 0, RiskLevel.LOW, ['Script file not found']
            );
        }

        // Read and assess script
        let scriptContent;
        try {
            scriptContent = await fs.readFile(scriptFullPath, 'utf8');
        } catch (error) {
            return new ExecutionResult(
                false, -1, '', `Cannot read script: ${error.message}`,
                0, RiskLevel.MEDIUM, ['Script read error']
            );
        }

        // Risk assessment
        const { riskLevel, warnings } = this.validator.assessRisk(scriptContent);

        // Check if script is pre-approved or requires approval
        const scriptRelPath = path.relative(this.repoPath, scriptFullPath);

        if (riskLevel === RiskLevel.CRITICAL) {
            return new ExecutionResult(
                false, -1, '', 'Script contains critical risk patterns and cannot be executed',
                0, riskLevel, warnings
            );
        }

        if (riskLevel === RiskLevel.HIGH && 
            !this.approvedScripts.has(scriptRelPath) && 
            !forceApproval) {
            return new ExecutionResult(
                false, -1, '', 'High-risk script requires explicit approval',
                0, riskLevel, [...warnings, 'Script requires approval']
            );
        }

        // Execute script
        if (forceApproval && !this.approvedScripts.has(scriptRelPath)) {
            this.approvedScripts.add(scriptRelPath);
            await this.saveApprovedScripts();
        }

        // Choose execution method
        let result;
        if (this.useDocker && [RiskLevel.MEDIUM, RiskLevel.HIGH].includes(riskLevel)) {
            result = await this.executeInDocker(scriptFullPath, args);
        } else {
            result = await this.executeNative(scriptFullPath, args);
        }

        result.riskAssessment = riskLevel;
        result.warnings = [...result.warnings, ...warnings];

        return result;
    }

    /**
     * Approve a script for future execution
     */
    async approveScript(scriptPath) {
        let scriptFullPath = path.resolve(scriptPath);
        
        if (!await this.fileExists(scriptFullPath)) {
            scriptFullPath = path.resolve(this.repoPath, scriptPath);
        }

        if (await this.fileExists(scriptFullPath)) {
            const scriptRelPath = path.relative(this.repoPath, scriptFullPath);
            this.approvedScripts.add(scriptRelPath);
            await this.saveApprovedScripts();
            return true;
        }
        return false;
    }

    /**
     * Get information about a script without executing it
     */
    async getScriptInfo(scriptPath) {
        let scriptFullPath = path.resolve(scriptPath);
        
        if (!await this.fileExists(scriptFullPath)) {
            scriptFullPath = path.resolve(this.repoPath, scriptPath);
        }

        if (!await this.fileExists(scriptFullPath)) {
            return { error: 'Script not found' };
        }

        try {
            const content = await fs.readFile(scriptFullPath, 'utf8');
            const stats = await fs.stat(scriptFullPath);
            const { riskLevel, warnings } = this.validator.assessRisk(content);
            const scriptRelPath = path.relative(this.repoPath, scriptFullPath);

            return {
                path: scriptRelPath,
                size: stats.size,
                risk_level: riskLevel,
                warnings: warnings,
                approved: this.approvedScripts.has(scriptRelPath),
                executable: this.isExecutable(stats.mode)
            };

        } catch (error) {
            return { error: `Cannot read script: ${error.message}` };
        }
    }

    /**
     * Check if file exists
     */
    async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Check if file is executable
     */
    isExecutable(mode) {
        // Check if owner, group, or others have execute permission
        return (mode & parseInt('111', 8)) !== 0;
    }

    /**
     * Get list of approved scripts
     */
    getApprovedScripts() {
        return Array.from(this.approvedScripts);
    }

    /**
     * Remove script from approved list
     */
    async revokeApproval(scriptPath) {
        const scriptRelPath = path.relative(this.repoPath, path.resolve(scriptPath));
        const removed = this.approvedScripts.delete(scriptRelPath);
        
        if (removed) {
            await this.saveApprovedScripts();
        }
        
        return removed;
    }
}

module.exports = { 
    ScriptExecutor, 
    ScriptValidator, 
    ExecutionResult, 
    RiskLevel 
};