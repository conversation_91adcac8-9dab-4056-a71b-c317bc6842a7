/**
 * Main AI Agent for TessellCS Operations (Node.js)
 * Provides intelligent assistance for tessellcs-ops repository
 */

const axios = require('axios');
const { RepositoryIndexer } = require('../knowledge/repositoryIndexer');
const { ScriptExecutor, RiskLevel } = require('../execution/scriptExecutor');
const logger = require('../utils/logger');

// LLM Engine imports
const Anthropic = require('@anthropic-ai/sdk');
const OpenAI = require('openai');

class AgentResponse {
    constructor(message, suggestedScripts = [], executionResults = null, requiresApproval = false, confidence = 0.0) {
        this.message = message;
        this.suggestedScripts = suggestedScripts;
        this.executionResults = executionResults;
        this.requiresApproval = requiresApproval;
        this.confidence = confidence;
    }
}

class TessellAgent {
    constructor(repoPath, llmConfig = {}) {
        this.repoPath = repoPath;
        this.indexer = new RepositoryIndexer(repoPath);
        this.executor = new ScriptExecutor(repoPath);
        
        // LLM Configuration
        this.llmConfig = {
            engine: llmConfig.engine || 'template', // 'openai', 'anthropic', 'ollama', 'template'
            apiKey: llmConfig.apiKey || null,
            model: llmConfig.model || this._getDefaultModel(llmConfig.engine),
            ollamaUrl: llmConfig.ollamaUrl || 'http://localhost:11434',
            maxTokens: llmConfig.maxTokens || 500,
            temperature: llmConfig.temperature || 0.7
        };
        
        // Initialize LLM clients
        this.llmClient = null;
        this._initializeLLMClient();
        
        // Agent personality and context
        this.systemPrompt = `You are TessellCS AI Agent, an expert assistant for database operations and infrastructure management.

You have access to a comprehensive repository of scripts and documentation for:
- Database patching (Oracle, MySQL, PostgreSQL, MSSQL)
- Database migrations and replication
- Cloud infrastructure tasks
- SSL certificate management
- Monitoring and metrics collection
- Backup and restore operations

Your capabilities:
1. Answer questions about database operations and best practices
2. Recommend appropriate scripts for specific tasks
3. Execute safe scripts with proper validation
4. Provide step-by-step guidance for complex operations

Always prioritize safety and ask for confirmation before executing high-risk operations.
When suggesting scripts, explain what they do and any prerequisites or risks.`;

        // Intent classification patterns
        this.intentPatterns = {
            script_execution: ['run', 'execute', 'start', 'launch', 'perform'],
            information_query: ['what', 'how', 'why', 'when', 'where', 'explain', 'describe', 'show'],
            script_search: ['find', 'search', 'locate', 'available', 'scripts for'],
            guidance: ['help', 'guide', 'steps', 'process', 'procedure', 'how to']
        };
    }

    /**
     * Get default model for each LLM engine
     */
    _getDefaultModel(engine) {
        const defaults = {
            'openai': 'gpt-3.5-turbo',
            'anthropic': 'claude-3-sonnet-20240229', 
            'ollama': 'llama2:7b',
            'template': null
        };
        return defaults[engine] || null;
    }

    /**
     * Initialize LLM client based on configuration
     */
    _initializeLLMClient() {
        try {
            switch (this.llmConfig.engine) {
                case 'openai':
                    if (this.llmConfig.apiKey) {
                        this.llmClient = new OpenAI({ apiKey: this.llmConfig.apiKey });
                        logger.info('OpenAI client initialized');
                    } else {
                        logger.warn('OpenAI API key not provided, falling back to template responses');
                        this.llmConfig.engine = 'template';
                    }
                    break;
                    
                case 'anthropic':
                    if (this.llmConfig.apiKey) {
                        this.llmClient = new Anthropic({ apiKey: this.llmConfig.apiKey });
                        logger.info('Anthropic client initialized');
                    } else {
                        logger.warn('Anthropic API key not provided, falling back to template responses');
                        this.llmConfig.engine = 'template';
                    }
                    break;
                    
                case 'ollama':
                    // Test Ollama connection
                    this._testOllamaConnection();
                    logger.info(`Ollama client initialized (${this.llmConfig.ollamaUrl})`);
                    break;
                    
                case 'template':
                default:
                    logger.info('Using template-based responses');
                    break;
            }
        } catch (error) {
            logger.error('LLM client initialization failed:', error);
            this.llmConfig.engine = 'template';
        }
    }

    /**
     * Test Ollama connection
     */
    async _testOllamaConnection() {
        try {
            const response = await axios.get(`${this.llmConfig.ollamaUrl}/api/tags`, {
                timeout: 5000
            });
            
            const models = response.data.models || [];
            const hasModel = models.some(model => model.name === this.llmConfig.model);
            
            if (!hasModel) {
                logger.warn(`Model ${this.llmConfig.model} not found in Ollama. Available models: ${models.map(m => m.name).join(', ')}`);
                logger.warn(`Run: ollama pull ${this.llmConfig.model}`);
            }
        } catch (error) {
            logger.warn('Cannot connect to Ollama:', error.message);
            logger.warn('Make sure Ollama is running: ollama serve');
        }
    }

    /**
     * Initialize the agent
     */
    async initialize() {
        try {
            await this.indexer.initialize();
            logger.info('TessellCS AI Agent initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize TessellCS AI Agent:', error);
            throw error;
        }
    }

    /**
     * Classify user intent from query
     */
    classifyIntent(query) {
        const queryLower = query.toLowerCase();
        
        for (const [intent, patterns] of Object.entries(this.intentPatterns)) {
            if (patterns.some(pattern => queryLower.includes(pattern))) {
                return intent;
            }
        }
        
        return 'information_query'; // default
    }

    /**
     * Extract context information from query
     */
    extractContext(query) {
        const context = {
            databaseType: null,
            operationType: null,
            urgency: 'normal',
            environment: null
        };

        const queryLower = query.toLowerCase();

        // Database type detection
        const dbTypes = {
            oracle: ['oracle', 'ora', 'rman'],
            mysql: ['mysql', 'mariadb'],
            postgresql: ['postgres', 'postgresql', 'pg'],
            mssql: ['mssql', 'sqlserver', 'sql server']
        };

        for (const [db, keywords] of Object.entries(dbTypes)) {
            if (keywords.some(keyword => queryLower.includes(keyword))) {
                context.databaseType = db;
                break;
            }
        }

        // Operation type detection
        const operations = {
            backup: ['backup', 'dump', 'export'],
            restore: ['restore', 'import', 'recover'],
            migration: ['migrate', 'migration', 'move', 'transfer'],
            patching: ['patch', 'update', 'upgrade'],
            monitoring: ['monitor', 'metrics', 'check', 'health'],
            ssl: ['ssl', 'tls', 'certificate', 'cert'],
            replication: ['replication', 'replica', 'sync']
        };

        for (const [op, keywords] of Object.entries(operations)) {
            if (keywords.some(keyword => queryLower.includes(keyword))) {
                context.operationType = op;
                break;
            }
        }

        // Environment detection
        const environments = ['prod', 'production', 'dev', 'development', 'test', 'staging'];
        for (const env of environments) {
            if (queryLower.includes(env)) {
                context.environment = env;
                break;
            }
        }

        // Urgency detection
        const urgentKeywords = ['urgent', 'emergency', 'critical', 'asap', 'immediately'];
        if (urgentKeywords.some(keyword => queryLower.includes(keyword))) {
            context.urgency = 'high';
        }

        return context;
    }

    /**
     * Search for relevant scripts and documentation
     */
    async searchRelevantContent(query, context) {
        // Build search query with context
        const searchTerms = [query];
        
        if (context.databaseType) {
            searchTerms.push(context.databaseType);
        }
        
        if (context.operationType) {
            searchTerms.push(context.operationType);
        }
        
        const searchQuery = searchTerms.join(' ');
        
        // Search in knowledge base
        const results = await this.indexer.search(searchQuery, { nResults: 10 });
        
        // Filter and rank results
        const filteredResults = results.map(result => {
            // Prioritize scripts over documentation for execution intents
            if (result.metadata.file_type === '.sh') {
                result.relevanceBoost = 1.2;
            } else if (result.metadata.file_type === '.md') {
                result.relevanceBoost = 1.0;
            } else {
                result.relevanceBoost = 0.8;
            }
            
            // Boost based on category match
            if (context.operationType === result.metadata.category) {
                result.relevanceBoost *= 1.5;
            }
            
            return result;
        });

        // Sort by boosted relevance
        filteredResults.sort((a, b) => {
            const scoreA = (a.relevanceBoost || 1.0) * (1 - (a.score || 0.5));
            const scoreB = (b.relevanceBoost || 1.0) * (1 - (b.score || 0.5));
            return scoreB - scoreA;
        });

        return filteredResults.slice(0, 5);
    }

    /**
     * Generate response using configured LLM or template-based approach
     */
    async generateResponse(query, context, relevantContent) {
        switch (this.llmConfig.engine) {
            case 'openai':
                return await this._generateOpenAIResponse(query, context, relevantContent);
            case 'anthropic':
                return await this._generateAnthropicResponse(query, context, relevantContent);
            case 'ollama':
                return await this._generateOllamaResponse(query, context, relevantContent);
            case 'template':
            default:
                return this._generateTemplateResponse(query, context, relevantContent);
        }
    }

    /**
     * Generate response using OpenAI
     */
    async _generateOpenAIResponse(query, context, relevantContent) {
        const contentSummary = relevantContent.slice(0, 3).map(item => 
            `File: ${item.metadata.file_path}\nContent: ${item.content.substring(0, 200)}...`
        ).join('\n\n');

        const userMessage = `${query}

Context Information:
${JSON.stringify(context, null, 2)}

Relevant Repository Content:
${contentSummary}

Please provide a helpful response addressing the user's query about the TessellCS operations repository. Focus on practical guidance and suggest appropriate scripts when relevant.`;

        try {
            const response = await this.llmClient.chat.completions.create({
                model: this.llmConfig.model,
                max_tokens: this.llmConfig.maxTokens,
                temperature: this.llmConfig.temperature,
                messages: [
                    { role: 'system', content: this.systemPrompt },
                    { role: 'user', content: userMessage }
                ]
            });

            return response.choices[0].message.content;
        } catch (error) {
            logger.error('OpenAI API error:', error);
            return this._generateTemplateResponse(query, context, relevantContent);
        }
    }

    /**
     * Generate response using Anthropic Claude
     */
    async _generateAnthropicResponse(query, context, relevantContent) {
        // Prepare context for Claude
        const contentSummary = relevantContent.slice(0, 3).map(item => 
            `File: ${item.metadata.file_path}\nContent: ${item.content.substring(0, 200)}...`
        ).join('\n\n');

        const userMessage = `${query}

Context Information:
${JSON.stringify(context, null, 2)}

Relevant Repository Content:
${contentSummary}

Please provide a helpful response addressing the user's query about the TessellCS operations repository. Focus on practical guidance and suggest appropriate scripts when relevant.`;

        try {
            const response = await this.llmClient.messages.create({
                model: this.llmConfig.model,
                max_tokens: this.llmConfig.maxTokens,
                temperature: this.llmConfig.temperature,
                system: this.systemPrompt,
                messages: [
                    { role: 'user', content: userMessage }
                ]
            });

            return response.content[0].text;
        } catch (error) {
            logger.error('Anthropic API error:', error);
            return this._generateTemplateResponse(query, context, relevantContent);
        }
    }

    /**
     * Generate response using Ollama
     */
    async _generateOllamaResponse(query, context, relevantContent) {
        const contentSummary = relevantContent.slice(0, 3).map(item => 
            `File: ${item.metadata.file_path}\nContent: ${item.content.substring(0, 200)}...`
        ).join('\n\n');

        const prompt = `${this.systemPrompt}

User Query: ${query}

Context Information:
${JSON.stringify(context, null, 2)}

Relevant Repository Content:
${contentSummary}

Please provide a helpful response addressing the user's query about the TessellCS operations repository. Focus on practical guidance and suggest appropriate scripts when relevant.`;

        try {
            const response = await axios.post(`${this.llmConfig.ollamaUrl}/api/generate`, {
                model: this.llmConfig.model,
                prompt: prompt,
                stream: false,
                options: {
                    temperature: this.llmConfig.temperature,
                    num_predict: this.llmConfig.maxTokens
                }
            }, {
                timeout: 30000 // 30 seconds timeout
            });

            return response.data.response;
        } catch (error) {
            logger.error('Ollama API error:', error);
            if (error.code === 'ECONNREFUSED') {
                logger.error('Cannot connect to Ollama. Make sure it\'s running: ollama serve');
            }
            return this._generateTemplateResponse(query, context, relevantContent);
        }
    }

    /**
     * Generate response using template-based approach
     */
    _generateTemplateResponse(query, context, relevantContent) {
        const responseParts = [];

        // Add contextual greeting
        if (context.urgency === 'high') {
            responseParts.push('I understand this is urgent. Let me help you quickly.');
        } else {
            responseParts.push('I can help you with that.');
        }

        // Add specific guidance based on context
        if (context.databaseType && context.operationType) {
            responseParts.push(`For ${context.databaseType} ${context.operationType} operations, here's what I found:`);
        }

        // Add relevant scripts/documentation
        if (relevantContent.length > 0) {
            responseParts.push('\nRelevant resources:');
            relevantContent.slice(0, 3).forEach((item, index) => {
                const filePath = item.metadata.file_path;
                const fileType = item.metadata.file_type || '';
                const category = item.metadata.category || 'general';

                if (fileType === '.sh') {
                    responseParts.push(`${index + 1}. Script: ${filePath} (Category: ${category})`);
                } else if (fileType === '.md') {
                    responseParts.push(`${index + 1}. Documentation: ${filePath}`);
                } else {
                    responseParts.push(`${index + 1}. File: ${filePath}`);
                }
            });
        }

        // Add safety note for high-risk operations
        const highRiskOperations = ['patching', 'migration', 'restore'];
        if (highRiskOperations.includes(context.operationType)) {
            responseParts.push('\n⚠️  Important: This is a high-risk operation. Please ensure you have proper backups and test in non-production first.');
        }

        return responseParts.join('\n');
    }

    /**
     * Suggest appropriate scripts based on query and context
     */
    async suggestScripts(query, context, relevantContent) {
        const suggested = [];

        for (const item of relevantContent) {
            if (item.metadata.file_type === '.sh') {
                const scriptPath = item.metadata.file_path;
                const scriptInfo = await this.executor.getScriptInfo(scriptPath);

                if (!scriptInfo.error) {
                    suggested.push({
                        path: scriptPath,
                        description: item.content.substring(0, 200) + '...',
                        risk_level: scriptInfo.risk_level,
                        approved: scriptInfo.approved,
                        category: item.metadata.category || 'general'
                    });
                }
            }
        }

        return suggested.slice(0, 3); // Top 3 suggestions
    }

    /**
     * Main query processing method
     */
    async processQuery(query, executeIfSafe = false) {
        try {
            // Classify intent and extract context
            const intent = this.classifyIntent(query);
            const context = this.extractContext(query);

            // Search for relevant content
            const relevantContent = await this.searchRelevantContent(query, context);

            // Generate response
            const message = await this.generateResponse(query, context, relevantContent);

            // Suggest scripts
            const suggestedScripts = await this.suggestScripts(query, context, relevantContent);

            // Auto-execute if requested and safe
            let executionResults = null;
            let requiresApproval = false;

            if (executeIfSafe && intent === 'script_execution' && suggestedScripts.length > 0) {
                const bestScript = suggestedScripts[0];
                if (bestScript.risk_level === RiskLevel.LOW) {
                    const result = await this.executor.executeScript(bestScript.path);
                    executionResults = {
                        script: bestScript.path,
                        success: result.success,
                        output: result.stdout,
                        errors: result.stderr
                    };
                } else if ([RiskLevel.MEDIUM, RiskLevel.HIGH].includes(bestScript.risk_level)) {
                    requiresApproval = true;
                }
            }

            return new AgentResponse(
                message,
                suggestedScripts,
                executionResults,
                requiresApproval,
                relevantContent.length > 0 ? 0.8 : 0.3
            );

        } catch (error) {
            logger.error('Query processing failed:', error);
            return new AgentResponse(
                'I encountered an error while processing your query. Please try again.',
                [],
                null,
                false,
                0.0
            );
        }
    }

    /**
     * Execute script after explicit approval
     */
    async executeScriptWithApproval(scriptPath, args = []) {
        try {
            const result = await this.executor.executeScript(scriptPath, args, true);

            return {
                script: scriptPath,
                success: result.success,
                exit_code: result.exitCode,
                output: result.stdout,
                errors: result.stderr,
                execution_time: result.executionTime,
                warnings: result.warnings
            };

        } catch (error) {
            logger.error('Script execution failed:', error);
            return {
                script: scriptPath,
                success: false,
                exit_code: -1,
                output: '',
                errors: error.message,
                execution_time: 0,
                warnings: ['Execution failed']
            };
        }
    }

    /**
     * Get documentation and usage info for a script
     */
    async getScriptDocumentation(scriptPath) {
        try {
            // Get script info
            const scriptInfo = await this.executor.getScriptInfo(scriptPath);
            if (scriptInfo.error) {
                return scriptInfo;
            }

            // Get full content
            const content = await this.indexer.getFileContent(scriptPath);
            if (!content) {
                return { error: 'Cannot read script content' };
            }

            // Extract documentation from comments
            const lines = content.split('\n');
            const docLines = [];
            const usageLines = [];

            let inDocBlock = false;
            for (const line of lines.slice(0, 50)) { // Check first 50 lines
                const trimmedLine = line.trim();
                if (trimmedLine.startsWith('#')) {
                    const docLine = trimmedLine.substring(1).trim();
                    if (docLine.toLowerCase().includes('usage:') || docLine.toLowerCase().includes('example:')) {
                        inDocBlock = true;
                    }

                    if (inDocBlock) {
                        usageLines.push(docLine);
                    } else {
                        docLines.push(docLine);
                    }
                }
            }

            return {
                path: scriptPath,
                description: docLines.slice(0, 5).join('\n'), // First 5 comment lines
                usage: usageLines.join('\n'),
                risk_level: scriptInfo.risk_level,
                warnings: scriptInfo.warnings,
                size: scriptInfo.size,
                category: scriptInfo.category || 'general'
            };

        } catch (error) {
            logger.error('Error getting script documentation:', error);
            return { error: error.message };
        }
    }

    /**
     * Initialize or update the knowledge base
     */
    async initializeKnowledgeBase(forceReindex = false) {
        try {
            return await this.indexer.indexRepository(forceReindex);
        } catch (error) {
            logger.error('Knowledge base initialization failed:', error);
            throw error;
        }
    }

    /**
     * Get repository and agent statistics
     */
    async getStats() {
        try {
            const repoStats = await this.indexer.getStats();
            const approvedScripts = this.executor.getApprovedScripts();

            return {
                repository: repoStats,
                approved_scripts: approvedScripts.length,
                llm_engine: this.llmConfig.engine,
                llm_model: this.llmConfig.model,
                agent_version: '1.0.0'
            };

        } catch (error) {
            logger.error('Error getting agent stats:', error);
            return null;
        }
    }
}

module.exports = { TessellAgent, AgentResponse };