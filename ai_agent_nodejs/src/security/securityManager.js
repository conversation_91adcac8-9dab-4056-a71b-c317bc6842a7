/**
 * Security Manager for TessellCS AI Agent (Node.js)
 * Handles authentication, authorization, and audit logging
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

const SecurityLevel = {
    RESTRICTED: 'restricted',
    STANDARD: 'standard',
    ELEVATED: 'elevated',
    ADMIN: 'admin'
};

class SecurityEvent {
    constructor(eventType, userId, action, resource, riskLevel, success, details = {}, ipAddress = null) {
        this.timestamp = new Date().toISOString();
        this.eventType = eventType;
        this.userId = userId;
        this.action = action;
        this.resource = resource;
        this.riskLevel = riskLevel;
        this.success = success;
        this.details = details;
        this.ipAddress = ipAddress;
    }
}

class UserSession {
    constructor(userId, sessionId, securityLevel, permissions = new Set()) {
        this.userId = userId;
        this.sessionId = sessionId;
        this.securityLevel = securityLevel;
        this.createdAt = new Date();
        this.lastActivity = new Date();
        this.permissions = permissions;
        this.rateLimitTokens = 100;
    }
}

class SecurityManager {
    constructor(configPath = null) {
        this.configPath = configPath || 'ai_agent_nodejs/security_config.json';
        this.config = {};
        this.activeSessions = new Map();
        this.auditLog = [];
        
        // Rate limiting configuration
        this.rateLimits = {
            [SecurityLevel.RESTRICTED]: { requests: 10, window: 300000 },  // 10 req/5min
            [SecurityLevel.STANDARD]: { requests: 50, window: 300000 },    // 50 req/5min
            [SecurityLevel.ELEVATED]: { requests: 200, window: 300000 },   // 200 req/5min
            [SecurityLevel.ADMIN]: { requests: 1000, window: 300000 }      // 1000 req/5min
        };
        
        // Blocked operations by security level
        this.blockedOperations = {
            [SecurityLevel.RESTRICTED]: new Set([
                'script_execution', 'file_modification', 'system_commands'
            ]),
            [SecurityLevel.STANDARD]: new Set([
                'high_risk_scripts', 'system_modification'
            ]),
            [SecurityLevel.ELEVATED]: new Set([
                'critical_operations'
            ]),
            [SecurityLevel.ADMIN]: new Set() // Admin can do everything
        };
        
        this.initialize();
    }

    /**
     * Initialize security manager
     */
    async initialize() {
        try {
            await this.loadSecurityConfig();
            this.setupAuditLogCleanup();
            logger.info('Security manager initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize security manager:', error);
            throw error;
        }
    }

    /**
     * Load security configuration
     */
    async loadSecurityConfig() {
        const defaultConfig = {
            jwtSecret: crypto.randomBytes(32).toString('hex'),
            sessionTimeout: 3600000, // 1 hour in milliseconds
            maxSessionsPerUser: 3,
            auditRetentionDays: 90,
            requireApprovalFor: ['high', 'critical'],
            blockedPatterns: [
                'rm -rf /',
                'format c:',
                'DROP DATABASE',
                'TRUNCATE TABLE',
                'DELETE FROM.*WHERE.*1=1'
            ],
            allowedScripts: [],
            restrictedPaths: [
                '/etc/passwd',
                '/etc/shadow', 
                '/root',
                '/boot'
            ]
        };

        try {
            const configData = await fs.readFile(this.configPath, 'utf8');
            const userConfig = JSON.parse(configData);
            this.config = { ...defaultConfig, ...userConfig };
        } catch (error) {
            // Config file doesn't exist, create with defaults
            this.config = defaultConfig;
            await this.saveSecurityConfig();
        }
    }

    /**
     * Save security configuration
     */
    async saveSecurityConfig() {
        try {
            const dir = path.dirname(this.configPath);
            await fs.mkdir(dir, { recursive: true });
            await fs.writeFile(this.configPath, JSON.stringify(this.config, null, 2));
        } catch (error) {
            logger.error('Error saving security config:', error);
        }
    }

    /**
     * Generate JWT session token
     */
    generateSessionToken(userId, securityLevel) {
        const payload = {
            userId: userId,
            securityLevel: securityLevel,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + (this.config.sessionTimeout / 1000)
        };

        return jwt.sign(payload, this.config.jwtSecret);
    }

    /**
     * Validate JWT session token
     */
    validateSessionToken(token) {
        try {
            const payload = jwt.verify(token, this.config.jwtSecret);
            return payload;
        } catch (error) {
            if (error.name === 'TokenExpiredError') {
                this.logSecurityEvent('auth', 'unknown', 'session_expired', 'token_validation', 'low', false);
            } else {
                this.logSecurityEvent('auth', 'unknown', 'invalid_token', 'token_validation', 'medium', false);
            }
            return null;
        }
    }

    /**
     * Create new user session
     */
    async createSession(userId, securityLevel, permissions = new Set()) {
        // Check max sessions per user
        const userSessions = Array.from(this.activeSessions.values())
            .filter(session => session.userId === userId);
        
        if (userSessions.length >= this.config.maxSessionsPerUser) {
            // Remove oldest session
            const oldest = userSessions.reduce((prev, current) => 
                prev.lastActivity < current.lastActivity ? prev : current
            );
            this.activeSessions.delete(oldest.sessionId);
        }

        const sessionId = crypto.randomBytes(32).toString('hex');
        const session = new UserSession(userId, sessionId, securityLevel, permissions);
        
        this.activeSessions.set(sessionId, session);
        
        this.logSecurityEvent('auth', userId, 'session_created', 'authentication', 'low', true, { sessionId });
        
        return this.generateSessionToken(userId, securityLevel);
    }

    /**
     * Validate session and return session info
     */
    validateSession(token) {
        const payload = this.validateSessionToken(token);
        if (!payload) {
            return null;
        }

        // Find active session
        const userSessions = Array.from(this.activeSessions.values())
            .filter(session => session.userId === payload.userId);
        
        if (userSessions.length === 0) {
            return null;
        }

        const session = userSessions[0]; // Get most recent

        // Check session timeout
        const now = new Date();
        if (now - session.lastActivity > this.config.sessionTimeout) {
            this.activeSessions.delete(session.sessionId);
            this.logSecurityEvent('auth', session.userId, 'session_timeout', 'authentication', 'low', false);
            return null;
        }

        // Update last activity
        session.lastActivity = now;
        
        return session;
    }

    /**
     * Check if user is within rate limits
     */
    checkRateLimit(session) {
        const limitConfig = this.rateLimits[session.securityLevel];
        
        if (session.rateLimitTokens <= 0) {
            // Check if window has passed
            const windowStart = new Date(session.lastActivity.getTime() - limitConfig.window);
            if (new Date() > windowStart) {
                session.rateLimitTokens = limitConfig.requests;
            } else {
                return false;
            }
        }

        session.rateLimitTokens--;
        return true;
    }

    /**
     * Check if operation is allowed for user's security level
     */
    isOperationAllowed(session, operation) {
        const blocked = this.blockedOperations[session.securityLevel] || new Set();
        return !blocked.has(operation);
    }

    /**
     * Validate script execution request
     */
    validateScriptExecution(session, scriptPath, riskLevel) {
        // Check basic permissions
        if (!this.isOperationAllowed(session, 'script_execution')) {
            return { allowed: false, reason: 'Script execution not allowed for your security level' };
        }

        // Check rate limits
        if (!this.checkRateLimit(session)) {
            return { allowed: false, reason: 'Rate limit exceeded' };
        }

        // Check risk level permissions
        if (['high', 'critical'].includes(riskLevel) && 
            [SecurityLevel.RESTRICTED, SecurityLevel.STANDARD].includes(session.securityLevel)) {
            return { allowed: false, reason: 'High-risk script execution requires elevated permissions' };
        }

        // Check if script is in allowed list
        if (this.config.allowedScripts.length > 0 && 
            !this.config.allowedScripts.includes(scriptPath) &&
            session.securityLevel !== SecurityLevel.ADMIN) {
            return { allowed: false, reason: 'Script not in approved list' };
        }

        // Check for blocked patterns in script path
        for (const pattern of this.config.blockedPatterns) {
            if (scriptPath.toLowerCase().includes(pattern.toLowerCase())) {
                return { allowed: false, reason: `Script contains blocked pattern: ${pattern}` };
            }
        }

        return { allowed: true, reason: 'Approved' };
    }

    /**
     * Validate file access request
     */
    validateFileAccess(session, filePath, accessType = 'read') {
        // Check restricted paths
        for (const restricted of this.config.restrictedPaths) {
            if (filePath.startsWith(restricted)) {
                if (session.securityLevel !== SecurityLevel.ADMIN) {
                    return { allowed: false, reason: `Access to ${restricted} requires admin privileges` };
                }
            }
        }

        // Check write access
        if (['write', 'modify', 'delete'].includes(accessType)) {
            if (!this.isOperationAllowed(session, 'file_modification')) {
                return { allowed: false, reason: 'File modification not allowed for your security level' };
            }
        }

        return { allowed: true, reason: 'Approved' };
    }

    /**
     * Log security event
     */
    logSecurityEvent(eventType, userId, action, resource, riskLevel, success, details = {}, ipAddress = null) {
        const event = new SecurityEvent(
            eventType, userId, action, resource, riskLevel, success, details, ipAddress
        );
        
        this.auditLog.push(event);
        
        // Log to file
        logger.info(`Security Event: ${eventType} - ${userId} - ${action} - ${success ? 'SUCCESS' : 'FAILURE'}`);
        
        // Alert on high-risk events
        if (['high', 'critical'].includes(riskLevel) && !success) {
            logger.warn(`HIGH RISK SECURITY EVENT: ${action} by ${userId} failed`);
        }
    }

    /**
     * Get audit log entries
     */
    getAuditLog(userId = null, hours = 24) {
        const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
        
        return this.auditLog
            .filter(event => {
                const eventTime = new Date(event.timestamp);
                return eventTime > cutoff && (!userId || event.userId === userId);
            })
            .map(event => ({
                timestamp: event.timestamp,
                eventType: event.eventType,
                userId: event.userId,
                action: event.action,
                resource: event.resource,
                riskLevel: event.riskLevel,
                success: event.success,
                details: event.details,
                ipAddress: event.ipAddress
            }));
    }

    /**
     * Setup automatic audit log cleanup
     */
    setupAuditLogCleanup() {
        setInterval(() => {
            this.cleanupOldLogs();
        }, 24 * 60 * 60 * 1000); // Run daily
    }

    /**
     * Clean up old audit logs
     */
    cleanupOldLogs() {
        const retentionMs = this.config.auditRetentionDays * 24 * 60 * 60 * 1000;
        const cutoff = new Date(Date.now() - retentionMs);
        
        const originalLength = this.auditLog.length;
        this.auditLog = this.auditLog.filter(event => {
            const eventTime = new Date(event.timestamp);
            return eventTime > cutoff;
        });
        
        const removed = originalLength - this.auditLog.length;
        if (removed > 0) {
            logger.info(`Cleaned up ${removed} old audit log entries`);
        }
    }

    /**
     * Check if operation requires additional approval
     */
    requireApproval(session, operation, riskLevel) {
        // Admin bypass
        if (session.securityLevel === SecurityLevel.ADMIN) {
            return false;
        }

        // Check config requirements
        if (this.config.requireApprovalFor.includes(riskLevel)) {
            return true;
        }

        // Critical operations always require approval for non-admin
        if (riskLevel === 'critical') {
            return true;
        }

        return false;
    }

    /**
     * Terminate session
     */
    terminateSession(sessionId) {
        const session = this.activeSessions.get(sessionId);
        if (session) {
            this.activeSessions.delete(sessionId);
            this.logSecurityEvent('auth', session.userId, 'session_terminated', 'authentication', 'low', true);
            return true;
        }
        return false;
    }

    /**
     * Get active sessions count
     */
    getActiveSessionsCount() {
        return this.activeSessions.size;
    }

    /**
     * Get sessions for a specific user
     */
    getUserSessions(userId) {
        return Array.from(this.activeSessions.values())
            .filter(session => session.userId === userId)
            .map(session => ({
                sessionId: session.sessionId,
                createdAt: session.createdAt,
                lastActivity: session.lastActivity,
                securityLevel: session.securityLevel
            }));
    }
}

class InputSanitizer {
    constructor() {
        this.dangerousPatterns = [
            /[;&|`$(){}[\]<>]/,  // Shell metacharacters
            /\.\.\//,            // Path traversal
            /eval\s*\(/,         // Code evaluation
            /exec\s*\(/,         // Code execution
            /import\s+os/,       // OS module import
            /subprocess/,        // Subprocess calls
            /__import__/,        // Dynamic imports
        ];
    }

    /**
     * Sanitize filename input
     */
    sanitizeFilename(filename) {
        // Remove dangerous characters
        let sanitized = filename.replace(/[^a-zA-Z0-9.\-_/]/g, '');
        
        // Remove path traversal attempts
        sanitized = sanitized.replace(/\.\.\//g, '').replace(/\.\.\\/g, '');
        
        return sanitized;
    }

    /**
     * Sanitize command line arguments
     */
    sanitizeCommandArgs(args) {
        return args.map(arg => {
            // Remove shell metacharacters
            return arg.replace(/[^a-zA-Z0-9.\-_=:/]/g, '');
        }).filter(arg => arg.length > 0);
    }

    /**
     * Validate user query for dangerous patterns
     */
    validateQuery(query) {
        for (const pattern of this.dangerousPatterns) {
            if (pattern.test(query)) {
                return { valid: false, reason: `Query contains potentially dangerous pattern: ${pattern}` };
            }
        }

        // Check length
        if (query.length > 1000) {
            return { valid: false, reason: 'Query too long' };
        }

        return { valid: true, reason: 'Valid' };
    }
}

module.exports = {
    SecurityManager,
    InputSanitizer,
    SecurityLevel,
    SecurityEvent,
    UserSession
};