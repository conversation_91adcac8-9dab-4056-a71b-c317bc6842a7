# TessellCS AI Agent Node.js Configuration

# Repository Path
TESSELL_REPO_PATH=/path/to/tessellcs-ops

# LLM Configuration
# Choose: 'openai', 'anthropic', 'ollama', or 'template'
LLM_ENGINE=template

# API Keys (only needed for cloud providers)
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here

# LLM Model Configuration
OPENAI_MODEL=gpt-3.5-turbo
ANTHROPIC_MODEL=claude-3-sonnet-20240229
OLLAMA_MODEL=llama2:7b

# Ollama Configuration (if using local Ollama)
OLLAMA_URL=http://localhost:11434

# LLM Parameters
LLM_MAX_TOKENS=500
LLM_TEMPERATURE=0.7

# Server Configuration
NODE_ENV=development
HOST=0.0.0.0
PORT=3000

# Logging
LOG_LEVEL=info

# Security Configuration
JWT_SECRET=your-jwt-secret-here

# CORS Configuration
CORS_ORIGIN=*

# Docker Configuration (if using Docker sandboxing)
USE_DOCKER=false