# TessellCS AI Agent - Node.js Version

A Node.js implementation of the TessellCS AI Agent that provides intelligent assistance for the tessellcs-ops repository with natural language querying, safe script execution, and comprehensive security features.

## 🚀 Features

- 🤖 **Natural Language Interface**: Ask questions about database operations, scripts, and procedures
- 🧠 **Multi-LLM Engine Support**: Choose between OpenAI, Anthropic Claude, <PERSON><PERSON><PERSON> (free local), or template responses
- 📚 **Knowledge Base**: Automatically indexes repository content for intelligent search
- 🔒 **Safe Script Execution**: Multi-level security with risk assessment and approval workflows
- 🎯 **Context-Aware Responses**: Understands database types, operations, and environments
- 📊 **Audit Logging**: Comprehensive security and activity logging
- 🌐 **Modern Web Interface**: React-based UI with Material-UI components
- ⚡ **REST API**: Full-featured Express.js API server
- 🐳 **Containerized Deployment**: Docker support for easy deployment
- 🆓 **Free Option Available**: Use Ollama for completely free local LLM processing

## 📁 Architecture

```
ai_agent_nodejs/
├── src/
│   ├── agent/
│   │   └── tessellAgent.js          # Main AI agent logic
│   ├── execution/
│   │   └── scriptExecutor.js        # Safe script execution engine
│   ├── knowledge/
│   │   ├── repositoryIndexer.js     # Repository content indexing
│   │   └── vectorDB.js              # Vector database for search
│   ├── security/
│   │   └── securityManager.js       # Authentication & authorization
│   ├── utils/
│   │   └── logger.js                # Structured logging
│   └── server.js                    # Express.js API server
├── client/                          # React frontend application
├── package.json                     # Node.js dependencies
└── README.md                        # This file
```

## 🛠️ Installation

### Prerequisites

- Node.js 18.0.0 or higher
- npm 8.0.0 or higher
- Docker (optional, for containerized execution)
- **LLM Engine** (choose one):
  - **Ollama** (FREE - runs locally, no API key needed)
  - **OpenAI API key** (for GPT models)
  - **Anthropic API key** (for Claude models)
  - **Template mode** (no external LLM required)

### Setup

1. **Install dependencies**:
   ```bash
   cd ai_agent_nodejs
   npm install
   ```

2. **Configure LLM Engine**:
   
   **Option A: Ollama (FREE - Recommended)**
   ```bash
   # Install Ollama
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # Pull a model
   ollama pull llama2:7b
   # OR for coding tasks
   ollama pull codellama:7b
   
   # Start Ollama
   ollama serve
   
   # Create .env file
   cat > .env << EOF
   TESSELL_REPO_PATH=/path/to/tessellcs-ops
   LLM_ENGINE=ollama
   OLLAMA_MODEL=llama2:7b
   NODE_ENV=development
   PORT=3000
   EOF
   ```
   
   **Option B: OpenAI**
   ```bash
   cat > .env << EOF
   TESSELL_REPO_PATH=/path/to/tessellcs-ops
   LLM_ENGINE=openai
   OPENAI_API_KEY=sk-your-openai-api-key
   OPENAI_MODEL=gpt-3.5-turbo
   NODE_ENV=development
   PORT=3000
   EOF
   ```
   
   **Option C: Anthropic Claude**
   ```bash
   cat > .env << EOF
   TESSELL_REPO_PATH=/path/to/tessellcs-ops
   LLM_ENGINE=anthropic
   ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
   ANTHROPIC_MODEL=claude-3-sonnet-20240229
   NODE_ENV=development
   PORT=3000
   EOF
   ```
   
   **Option D: Template Mode (No LLM)**
   ```bash
   cat > .env << EOF
   TESSELL_REPO_PATH=/path/to/tessellcs-ops
   LLM_ENGINE=template
   NODE_ENV=development
   PORT=3000
   EOF
   ```

3. **Install frontend dependencies**:
   ```bash
   npm run install:client
   ```

4. **Test your LLM setup** (optional):
   ```bash
   # For Ollama - test connection
   curl http://localhost:11434/api/tags
   
   # For OpenAI - test API key (replace with your key)
   curl https://api.openai.com/v1/models \
     -H "Authorization: Bearer $OPENAI_API_KEY"
   
   # For Anthropic - test API key
   curl https://api.anthropic.com/v1/messages \
     -H "x-api-key: $ANTHROPIC_API_KEY"
   ```

5. **Start the application**:
   ```bash
   # Development mode with auto-reload
   npm run dev
   
   # Production mode
   npm start
   ```

5. **Access the application**:
   - Web Interface: http://localhost:3000
   - API Documentation: http://localhost:3000/api/health

## 🔑 Authentication

The system includes demo users for testing:

| Username | Password | Security Level | Capabilities |
|----------|----------|----------------|--------------|
| admin    | admin123 | Admin          | Full access including audit logs |
| user     | user123  | Standard       | Script execution, repository access |
| guest    | guest123 | Restricted     | Read-only access |

## 🔒 Security Features

### Multi-Level Security

- **Restricted**: Read-only access, no script execution
- **Standard**: Can execute low-risk scripts
- **Elevated**: Can execute medium-risk scripts with approval
- **Admin**: Full access with comprehensive audit logging

### Risk Assessment

Scripts are automatically classified:

- **Low**: Safe monitoring and status scripts
- **Medium**: Configuration changes, file operations
- **High**: Database operations, system modifications (requires approval)
- **Critical**: Destructive operations (blocked)

### Safety Mechanisms

- Input sanitization and validation
- Script path validation (repository bounds)
- Rate limiting per user
- Execution timeouts
- Docker sandboxing (optional)
- Comprehensive audit logging

## 🌐 API Endpoints

### Authentication
- `POST /api/login` - User authentication
- `GET /api/stats` - Get system statistics

### Agent Interaction
- `POST /api/query` - Process natural language queries
- `GET /api/scripts` - List available scripts
- `GET /api/script/:path/info` - Get script information
- `POST /api/execute-script` - Execute scripts with safety checks

### Administration
- `POST /api/initialize` - Initialize/update knowledge base
- `GET /api/audit-log` - View audit logs (admin only)
- `GET /api/health` - System health check

## 💬 Usage Examples

### Web Interface

1. Open http://localhost:3000 in your browser
2. Login with demo credentials
3. Use the chat interface to ask questions:
   - "Show me Oracle patching scripts"
   - "How do I check database health?"
   - "Execute the CPU monitoring script"

### API Usage

```bash
# Login
curl -X POST http://localhost:3000/api/login \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\"username\": \"admin\", \"password\": \"admin123\"}'

# Query the agent
curl -X POST http://localhost:3000/api/query \\\n  -H \"Authorization: Bearer <token>\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\"query\": \"Show me MySQL backup scripts\"}'

# Execute a script
curl -X POST http://localhost:3000/api/execute-script \\\n  -H \"Authorization: Bearer <token>\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\"script_path\": \"monitoring/check_db.sh\"}'
```

## 🔧 Configuration

### LLM Engine Configuration

| Engine | Description | Cost | Setup Required |
|--------|-------------|------|----------------|
| `ollama` | **FREE** local LLMs (Llama 2, Code Llama, etc.) | Free | Install Ollama, pull model |
| `openai` | OpenAI GPT models (GPT-3.5, GPT-4) | Pay per token | API key required |
| `anthropic` | Anthropic Claude models | Pay per token | API key required |
| `template` | Rule-based responses (no AI) | Free | No setup needed |

### Environment Variables

#### Core Configuration
- `TESSELL_REPO_PATH`: Path to tessellcs-ops repository
- `NODE_ENV`: Environment (development/production)
- `PORT`: Server port (default: 3000)
- `HOST`: Server host (default: 0.0.0.0)
- `LOG_LEVEL`: Logging level (debug/info/warn/error)
- `CORS_ORIGIN`: CORS allowed origins

#### LLM Configuration
- `LLM_ENGINE`: Choose engine (`ollama`, `openai`, `anthropic`, `template`)
- `LLM_MAX_TOKENS`: Response length limit (default: 500)
- `LLM_TEMPERATURE`: Response creativity 0.0-2.0 (default: 0.7)

#### API Keys (only for cloud providers)
- `OPENAI_API_KEY`: OpenAI API key (starts with `sk-`)
- `ANTHROPIC_API_KEY`: Anthropic API key (starts with `sk-ant-`)

#### Model Selection
- `OPENAI_MODEL`: OpenAI model (`gpt-3.5-turbo`, `gpt-4`, etc.)
- `ANTHROPIC_MODEL`: Anthropic model (`claude-3-sonnet-20240229`, etc.)
- `OLLAMA_MODEL`: Ollama model (`llama2:7b`, `codellama:7b`, etc.)

#### Ollama Configuration
- `OLLAMA_URL`: Ollama server URL (default: `http://localhost:11434`)

### Configuration Files

Security settings are stored in `ai_agent_nodejs/security_config.json`:

```json
{
  "sessionTimeout": 3600000,
  "maxSessionsPerUser": 3,
  "auditRetentionDays": 90,
  "requireApprovalFor": ["high", "critical"],
  "allowedScripts": [],
  "restrictedPaths": ["/etc/passwd", "/etc/shadow"]
}
```

## 🐳 Docker Deployment

1. **Build the application**:
   ```bash
   docker build -t tessellcs-ai-agent-nodejs .
   ```

2. **Run with Docker Compose**:
   ```bash
   docker-compose up -d
   ```

3. **Environment variables**:
   ```bash
   # Create .env file for Docker
   echo "ANTHROPIC_API_KEY=your-key-here" > .env
   echo "TESSELL_REPO_PATH=/app/repository" >> .env
   ```

## 🧪 Development

### Project Structure

- **Agent Core** (`src/agent/`): Main AI logic and query processing
- **Execution Engine** (`src/execution/`): Safe script execution with risk assessment
- **Knowledge Base** (`src/knowledge/`): Repository indexing and search
- **Security** (`src/security/`): Authentication, authorization, audit logging
- **API Server** (`src/server.js`): Express.js REST API
- **Frontend** (`client/`): React web application

### Adding New Features

1. **New Script Categories**: Update categorization in `repositoryIndexer.js`
2. **Security Policies**: Modify risk patterns in `scriptExecutor.js`
3. **API Endpoints**: Add routes in `server.js`
4. **UI Components**: Add React components in `client/src/components/`

### Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test -- --coverage

# Test individual components
node src/agent/tessellAgent.js
node src/execution/scriptExecutor.js
```

## 📊 Monitoring

### Logging

- **Application Logs**: `logs/combined.log`
- **Error Logs**: `logs/error.log`
- **Security Logs**: `logs/security.log`

### Health Monitoring

```bash
# Check system health
curl http://localhost:3000/api/health

# Get statistics
curl -H "Authorization: Bearer <token>" http://localhost:3000/api/stats
```

## 🔄 Comparison with Python Version

| Feature | Node.js Version | Python Version |
|---------|----------------|----------------|
| **Backend** | Express.js | FastAPI |
| **Frontend** | React + Material-UI | Streamlit |
| **Vector DB** | Custom implementation | ChromaDB |
| **Authentication** | JWT + Custom | JWT + Custom |
| **LLM Support** | OpenAI + Anthropic + **Ollama** | Anthropic only |
| **Free Option** | ✅ **Ollama (fully free)** | ❌ Requires API key |
| **Local Processing** | ✅ **Available with Ollama** | ❌ Cloud-dependent |
| **Execution** | Node.js child_process | Python subprocess |
| **Deployment** | Docker + Node.js | Docker + Python |

## 🚨 Troubleshooting

### Common Issues

1. **Repository not found**: Check `TESSELL_REPO_PATH` environment variable
2. **LLM Connection Issues**:
   - **Ollama**: Make sure `ollama serve` is running on `http://localhost:11434`
   - **OpenAI**: Verify API key format (starts with `sk-`) and has sufficient credits
   - **Anthropic**: Verify API key format (starts with `sk-ant-`) and is valid
3. **Model Not Found**: 
   - **Ollama**: Run `ollama pull llama2:7b` to download the model
   - **OpenAI/Anthropic**: Check model name is correct and available
4. **Permission errors**: Ensure read access to repository directory
5. **Port conflicts**: Change `PORT` environment variable
6. **Frontend build errors**: Run `npm run install:client` and `npm run build:client`

### LLM-Specific Troubleshooting

#### Ollama Issues
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# If not running, start it
ollama serve

# List available models
ollama list

# Pull missing model
ollama pull llama2:7b
```

#### OpenAI Issues
```bash
# Test API key
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer $OPENAI_API_KEY"

# Check available models
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer $OPENAI_API_KEY" | jq '.data[].id'
```

#### Anthropic Issues
```bash
# Test API key (replace with your key)
curl https://api.anthropic.com/v1/messages \
  -H "x-api-key: $ANTHROPIC_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model":"claude-3-sonnet-20240229","max_tokens":10,"messages":[{"role":"user","content":"Hi"}]}'
```

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=debug
npm run dev

# Check logs
tail -f logs/combined.log
```

## 🤝 Contributing

1. Follow existing code patterns and naming conventions
2. Add tests for new functionality
3. Update documentation for API changes
4. Ensure security validation for new features
5. Test in both development and production modes

## 📄 License

This project is part of the TessellCS operations repository and follows the same licensing terms.

## 🆚 Python vs Node.js Versions

You now have both Python and Node.js implementations:

- **Python Version** (`ai_agent/`): Uses FastAPI, Streamlit, ChromaDB
- **Node.js Version** (`ai_agent_nodejs/`): Uses Express.js, React, custom vector DB

Both versions provide the same core functionality with platform-specific optimizations and UI frameworks. Choose based on your team's preferences and existing technology stack.