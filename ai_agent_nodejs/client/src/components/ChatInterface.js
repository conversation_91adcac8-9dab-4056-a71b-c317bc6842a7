import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  List,
  ListItem,
  ListItemText,
  Chip,
  Alert,
  CircularProgress,
  IconButton,
  Card,
  CardContent,
  CardActions,
  Divider,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Send as SendIcon,
  Person as PersonIcon,
  SmartToy as BotIcon,
  PlayArrow as PlayIcon,
  Info as InfoIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import axios from 'axios';
import ReactMarkdown from 'react-markdown';

function ChatInterface() {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [autoExecute, setAutoExecute] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async () => {
    if (!inputMessage.trim() || loading) return;

    const userMessage = {
      role: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setLoading(true);

    try {
      const response = await axios.post('/api/query', {
        query: inputMessage,
        execute_if_safe: autoExecute
      });

      const assistantMessage = {
        role: 'assistant',
        content: response.data.message,
        timestamp: new Date().toISOString(),
        metadata: {
          suggested_scripts: response.data.suggested_scripts || [],
          execution_results: response.data.execution_results,
          requires_approval: response.data.requires_approval || false,
          confidence: response.data.confidence || 0
        }
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Query failed:', error);
      
      const errorMessage = {
        role: 'assistant',
        content: `Sorry, I encountered an error: ${error.response?.data?.error || error.message}`,
        timestamp: new Date().toISOString(),
        isError: true
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const executeScript = async (scriptPath, forceApproval = false) => {
    try {
      const response = await axios.post('/api/execute-script', {
        script_path: scriptPath,
        args: [],
        force_approval: forceApproval
      });

      if (response.data.requires_approval) {
        const approvalMessage = {
          role: 'system',
          content: response.data.message,
          timestamp: new Date().toISOString(),
          requiresApproval: {
            scriptPath: scriptPath,
            scriptInfo: response.data.script_info
          }
        };
        setMessages(prev => [...prev, approvalMessage]);
      } else {
        const executionMessage = {
          role: 'system',
          content: response.data.success ? 'Script executed successfully!' : 'Script execution failed!',
          timestamp: new Date().toISOString(),
          executionResult: response.data
        };
        setMessages(prev => [...prev, executionMessage]);
      }
    } catch (error) {
      console.error('Script execution failed:', error);
      const errorMessage = {
        role: 'system',
        content: `Script execution failed: ${error.response?.data?.error || error.message}`,
        timestamp: new Date().toISOString(),
        isError: true
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const getScriptInfo = async (scriptPath) => {
    try {
      const response = await axios.get(`/api/script/${encodeURIComponent(scriptPath)}/info`);
      
      const infoMessage = {
        role: 'system',
        content: 'Script Information',
        timestamp: new Date().toISOString(),
        scriptInfo: response.data
      };
      setMessages(prev => [...prev, infoMessage]);
    } catch (error) {
      console.error('Failed to get script info:', error);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  };

  const renderMessage = (message, index) => {
    const isUser = message.role === 'user';
    const isSystem = message.role === 'system';
    
    return (
      <ListItem key={index} sx={{ flexDirection: 'column', alignItems: 'flex-start' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          {isUser ? <PersonIcon /> : isSystem ? <WarningIcon /> : <BotIcon />}
          <Typography variant="subtitle2" sx={{ ml: 1, fontWeight: 'bold' }}>
            {isUser ? 'You' : isSystem ? 'System' : 'TessellCS AI'}
          </Typography>
          <Typography variant="caption" sx={{ ml: 2, color: 'text.secondary' }}>
            {new Date(message.timestamp).toLocaleTimeString()}
          </Typography>
        </Box>
        
        <Paper 
          elevation={1} 
          sx={{ 
            p: 2, 
            width: '100%', 
            bgcolor: isUser ? 'primary.light' : message.isError ? 'error.light' : 'background.paper',
            color: isUser ? 'primary.contrastText' : 'text.primary'
          }}
        >
          <ReactMarkdown>{message.content}</ReactMarkdown>
          
          {/* Suggested Scripts */}
          {message.metadata?.suggested_scripts?.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Suggested Scripts:</Typography>
              {message.metadata.suggested_scripts.map((script, idx) => (
                <Card key={idx} sx={{ mb: 1 }}>
                  <CardContent sx={{ pb: 1 }}>
                    <Typography variant="body2" fontWeight="bold">
                      {script.path}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Risk: {script.risk_level} | Category: {script.category}
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {script.description}
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button 
                      size="small" 
                      startIcon={<InfoIcon />}
                      onClick={() => getScriptInfo(script.path)}
                    >
                      Info
                    </Button>
                    <Button 
                      size="small" 
                      startIcon={<PlayIcon />}
                      onClick={() => executeScript(script.path)}
                      color={script.risk_level === 'low' ? 'primary' : 'warning'}
                    >
                      Execute
                    </Button>
                  </CardActions>
                </Card>
              ))}
            </Box>
          )}
          
          {/* Execution Results */}
          {message.metadata?.execution_results && (
            <Box sx={{ mt: 2 }}>
              <Alert severity={message.metadata.execution_results.success ? 'success' : 'error'}>
                <Typography variant="subtitle2">Execution Result</Typography>
                <Typography variant="body2">Script: {message.metadata.execution_results.script}</Typography>
                {message.metadata.execution_results.output && (
                  <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1 }}>
                    <Typography variant="caption">Output:</Typography>
                    <pre style={{ margin: 0, fontSize: '0.8rem' }}>
                      {message.metadata.execution_results.output}
                    </pre>
                  </Box>
                )}
                {message.metadata.execution_results.errors && (
                  <Box sx={{ mt: 1, p: 1, bgcolor: 'error.light', borderRadius: 1 }}>
                    <Typography variant="caption">Errors:</Typography>
                    <pre style={{ margin: 0, fontSize: '0.8rem' }}>
                      {message.metadata.execution_results.errors}
                    </pre>
                  </Box>
                )}
              </Alert>
            </Box>
          )}
          
          {/* Script Info */}
          {message.scriptInfo && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Script Information</Typography>
              <Typography variant="body2"><strong>Path:</strong> {message.scriptInfo.path}</Typography>
              <Typography variant="body2"><strong>Risk Level:</strong> {message.scriptInfo.risk_level}</Typography>
              <Typography variant="body2"><strong>Size:</strong> {message.scriptInfo.size} bytes</Typography>
              {message.scriptInfo.description && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Description:</strong> {message.scriptInfo.description}
                </Typography>
              )}
              {message.scriptInfo.warnings?.length > 0 && (
                <Alert severity="warning" sx={{ mt: 1 }}>
                  <Typography variant="body2">Warnings:</Typography>
                  <ul>
                    {message.scriptInfo.warnings.map((warning, idx) => (
                      <li key={idx}>{warning}</li>
                    ))}
                  </ul>
                </Alert>
              )}
            </Box>
          )}
          
          {/* Execution Result */}
          {message.executionResult && (
            <Box sx={{ mt: 2 }}>
              <Alert severity={message.executionResult.success ? 'success' : 'error'}>
                <Typography variant="subtitle2">Execution Complete</Typography>
                <Typography variant="body2">Exit Code: {message.executionResult.exit_code}</Typography>
                <Typography variant="body2">Time: {message.executionResult.execution_time}ms</Typography>
                
                {message.executionResult.output && (
                  <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1 }}>
                    <Typography variant="caption">Output:</Typography>
                    <pre style={{ margin: 0, fontSize: '0.8rem', maxHeight: '200px', overflow: 'auto' }}>
                      {message.executionResult.output}
                    </pre>
                  </Box>
                )}
                
                {message.executionResult.errors && (
                  <Box sx={{ mt: 1, p: 1, bgcolor: 'error.light', borderRadius: 1 }}>
                    <Typography variant="caption">Errors:</Typography>
                    <pre style={{ margin: 0, fontSize: '0.8rem', maxHeight: '200px', overflow: 'auto' }}>
                      {message.executionResult.errors}
                    </pre>
                  </Box>
                )}
              </Alert>
            </Box>
          )}
          
          {/* Approval Required */}
          {message.requiresApproval && (
            <Box sx={{ mt: 2 }}>
              <Alert severity="warning">
                <Typography variant="subtitle2">Approval Required</Typography>
                <Typography variant="body2">This script requires explicit approval due to high risk level.</Typography>
                <Button 
                  variant="contained" 
                  color="warning" 
                  sx={{ mt: 1 }}
                  onClick={() => executeScript(message.requiresApproval.scriptPath, true)}
                >
                  Approve and Execute
                </Button>
              </Alert>
            </Box>
          )}
          
          {/* Debug Info */}
          {showDebug && message.metadata && (
            <Box sx={{ mt: 2, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="caption">Debug Info:</Typography>
              <pre style={{ margin: 0, fontSize: '0.7rem' }}>
                {JSON.stringify(message.metadata, null, 2)}
              </pre>
            </Box>
          )}
        </Paper>
      </ListItem>
    );
  };

  return (
    <Box sx={{ height: '80vh', display: 'flex', flexDirection: 'column' }}>
      {/* Settings */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch 
                checked={autoExecute} 
                onChange={(e) => setAutoExecute(e.target.checked)}
              />
            }
            label="Auto-execute safe scripts"
          />
          <FormControlLabel
            control={
              <Switch 
                checked={showDebug} 
                onChange={(e) => setShowDebug(e.target.checked)}
              />
            }
            label="Show debug info"
          />
        </Box>
      </Paper>
      
      {/* Messages */}
      <Paper sx={{ flexGrow: 1, overflow: 'auto', mb: 2 }}>
        {messages.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center', color: 'text.secondary' }}>
            <BotIcon sx={{ fontSize: 48, mb: 2 }} />
            <Typography variant="h6">Welcome to TessellCS AI Agent</Typography>
            <Typography variant="body2">
              Ask me about database operations, scripts, or get help with your infrastructure tasks.
            </Typography>
          </Box>
        ) : (
          <List>
            {messages.map(renderMessage)}
            <div ref={messagesEndRef} />
          </List>
        )}
      </Paper>
      
      {/* Input */}
      <Paper sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Ask me about database operations, scripts, or infrastructure tasks..."
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={loading}
          />
          <Button
            variant="contained"
            onClick={sendMessage}
            disabled={loading || !inputMessage.trim()}
            sx={{ minWidth: 64 }}
          >
            {loading ? <CircularProgress size={24} /> : <SendIcon />}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
}

export default ChatInterface;