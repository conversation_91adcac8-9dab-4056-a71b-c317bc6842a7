import React from 'react';
import {
  AppBar,
  Too<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Box,
  Chip
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Chat as ChatIcon,
  Code as CodeIcon, 
  Security as SecurityIcon,
  ExitToApp as LogoutIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

function Navbar() {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  const menuItems = [
    { label: 'Dashboard', path: '/dashboard', icon: <DashboardIcon /> },
    { label: 'Chat', path: '/chat', icon: <ChatIcon /> },
    { label: 'Scripts', path: '/scripts', icon: <CodeIcon /> },
  ];

  // Add audit log for admin users
  if (user?.securityLevel === 'admin') {
    menuItems.push({ label: 'Audit Log', path: '/audit', icon: <SecurityIcon /> });
  }

  return (
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          🤖 TessellCS AI Agent
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mr: 2 }}>
          {menuItems.map((item) => (
            <Button
              key={item.path}
              color="inherit"
              startIcon={item.icon}
              onClick={() => navigate(item.path)}
              variant={location.pathname === item.path ? 'outlined' : 'text'}
              sx={{ 
                color: 'white',
                borderColor: location.pathname === item.path ? 'white' : 'transparent'
              }}
            >
              {item.label}
            </Button>
          ))}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Chip 
            label={user?.id || 'Unknown'} 
            size="small" 
            variant="outlined"
            sx={{ color: 'white', borderColor: 'white' }}
          />
          <Chip 
            label={user?.securityLevel || 'Unknown'} 
            size="small" 
            color={
              user?.securityLevel === 'admin' ? 'error' :
              user?.securityLevel === 'elevated' ? 'warning' :
              user?.securityLevel === 'standard' ? 'success' : 'default'
            }
          />
          <Button 
            color="inherit" 
            startIcon={<LogoutIcon />}
            onClick={handleLogout}
          >
            Logout
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
}

export default Navbar;