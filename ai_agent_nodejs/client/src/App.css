.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Custom styles for TessellCS AI Agent */
.chat-message {
  margin-bottom: 16px;
}

.script-card {
  transition: transform 0.2s ease-in-out;
}

.script-card:hover {
  transform: translateY(-2px);
}

.execution-output {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
}

.risk-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .App-header {
    padding: 10px;
  }
  
  .mobile-hidden {
    display: none;
  }
}