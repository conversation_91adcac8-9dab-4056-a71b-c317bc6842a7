#!/usr/bin/env node
/**
 * Manual re-indexing script for TessellCS AI Agent
 * Usage: node reindex.js [--force]
 */

const path = require('path');
const { RepositoryIndexer } = require('./src/knowledge/repositoryIndexer');

async function reindex() {
    const forceReindex = process.argv.includes('--force');
    const repoPath = path.join(__dirname, '../..');
    const dbPath = path.join(__dirname, 'vectordb');
    
    console.log('Starting repository re-indexing...');
    console.log(`Repository: ${repoPath}`);
    console.log(`Database: ${dbPath}`);
    console.log(`Force reindex: ${forceReindex}`);
    console.log('----------------------------------------');
    
    try {
        const indexer = new RepositoryIndexer(repoPath, dbPath);
        await indexer.initialize();
        
        const stats = await indexer.indexRepository(forceReindex);
        
        console.log('Re-indexing completed successfully!');
        console.log('Statistics:', stats);
        
        // Show final stats
        const finalStats = await indexer.getStats();
        console.log('Final database stats:', finalStats);
        
    } catch (error) {
        console.error('Re-indexing failed:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    reindex();
}