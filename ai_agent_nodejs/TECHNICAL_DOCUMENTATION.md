# TessellCS AI Agent - Node.js Technical Documentation

This document provides detailed technical information about each component in the Node.js implementation of the TessellCS AI Agent, their architecture, functionality, and integration patterns.

## 📁 Project Structure Overview

```
ai_agent_nodejs/
├── src/
│   ├── agent/
│   │   └── tessellAgent.js           # Main AI agent logic
│   ├── execution/
│   │   └── scriptExecutor.js         # Safe script execution engine
│   ├── knowledge/
│   │   ├── repositoryIndexer.js      # Repository content indexing
│   │   └── vectorDB.js               # Custom vector database
│   ├── security/
│   │   └── securityManager.js        # Authentication & security
│   ├── utils/
│   │   └── logger.js                 # Structured logging
│   └── server.js                     # Express.js API server
├── client/                           # React frontend application
│   ├── src/
│   │   ├── components/               # React components
│   │   ├── contexts/                 # React contexts
│   │   └── App.js                    # Main React app
│   └── package.json                  # Frontend dependencies
├── package.json                      # Backend dependencies
└── README.md                         # Project documentation
```

---

## 🤖 src/agent/tessellAgent.js

### Purpose
Main orchestrator that coordinates all AI agent operations including query processing, Claude integration, and response generation.

### Key Classes

#### `AgentResponse`
**Description**: Data structure for agent responses
```javascript
class AgentResponse {
  constructor(message, suggestedScripts = [], executionResults = null, requiresApproval = false, confidence = 0.0)
}
```

**Fields**:
- `message`: AI-generated response text
- `suggestedScripts`: Array of relevant scripts with metadata
- `executionResults`: Results from script execution (if performed)
- `requiresApproval`: Whether operation needs explicit approval
- `confidence`: Confidence score (0.0-1.0) of the response

#### `TessellAgent`
**Description**: Main AI agent class orchestrating all operations

**Constructor**:
```javascript
constructor(repoPath, anthropicApiKey = null)
```

**Key Methods**:

##### `initialize()`
- Initializes repository indexer and vector database
- Sets up Claude API client if key provided
- Prepares system for query processing

##### `processQuery(query, executeIfSafe = false)`
**Main query processing pipeline**:
1. **Intent Classification**: Determines user intent (execution, information, search, guidance)
2. **Context Extraction**: Extracts database type, operation type, urgency, environment
3. **Content Search**: Finds relevant scripts and documentation using vector search
4. **Response Generation**: Uses Claude API or template-based approach
5. **Script Suggestion**: Recommends appropriate scripts based on context
6. **Auto-execution**: Executes low-risk scripts if requested

**Usage Example**:
```javascript
const agent = new TessellAgent('/path/to/repo', 'claude-api-key');
await agent.initialize();

const response = await agent.processQuery('How do I check Oracle health?');
console.log(response.message);
response.suggestedScripts.forEach(script => {
    console.log(`Script: ${script.path} (Risk: ${script.risk_level})`);
});
```

##### `classifyIntent(query)`
**Intent Recognition System**:
- **script_execution**: Keywords like "run", "execute", "start"
- **information_query**: Keywords like "what", "how", "explain"
- **script_search**: Keywords like "find", "search", "available"
- **guidance**: Keywords like "help", "guide", "steps"

##### `extractContext(query)`
**Context Extraction Engine**:
```javascript
// Detected context structure
{
    databaseType: 'oracle|mysql|postgresql|mssql',
    operationType: 'backup|restore|migration|patching|monitoring|ssl|replication',
    urgency: 'normal|high',
    environment: 'prod|dev|test|staging'
}
```

##### `searchRelevantContent(query, context)`
**Semantic Search with Relevance Boosting**:
- Combines query terms with extracted context
- Boosts script files (1.2x) over documentation (1.0x)
- Applies category matching bonuses (1.5x)
- Returns top 5 most relevant results

##### `generateResponse(query, context, relevantContent)`
**Dual Response Generation**:
- **Claude Mode**: Uses Anthropic API for intelligent responses
- **Template Mode**: Fallback with structured response templates

**Claude Integration**:
```javascript
const response = await this.anthropic.messages.create({
    model: 'claude-3-sonnet-20240229',
    max_tokens: 500,
    temperature: 0.7,
    system: this.systemPrompt,
    messages: [{ role: 'user', content: userMessage }]
});
```

### AI Integration Features

#### **System Prompt Engineering**
```javascript
this.systemPrompt = `You are TessellCS AI Agent, an expert assistant for database operations...

Your capabilities:
1. Answer questions about database operations and best practices
2. Recommend appropriate scripts for specific tasks
3. Execute safe scripts with proper validation
4. Provide step-by-step guidance for complex operations

Always prioritize safety and ask for confirmation before executing high-risk operations.`;
```

#### **Context-Aware Processing**
- Database type detection (Oracle, MySQL, PostgreSQL, MSSQL)
- Operation classification (backup, restore, migration, etc.)
- Environment awareness (production, development, test)
- Urgency assessment for response prioritization

#### **Response Quality Metrics**
- Confidence scoring based on content relevance
- Safety assessment for suggested operations
- Template fallback for API failures

---

## ⚡ src/execution/scriptExecutor.js

### Purpose
Provides safe, controlled execution of repository scripts with comprehensive risk assessment and security validation.

### Key Enums and Classes

#### `RiskLevel`
```javascript
const RiskLevel = {
    LOW: 'low',        // Safe monitoring commands
    MEDIUM: 'medium',  // File operations, configuration changes
    HIGH: 'high',      // Database operations, system modifications
    CRITICAL: 'critical' // Destructive operations (blocked)
};
```

#### `ExecutionResult`
**Description**: Comprehensive execution result with metadata
```javascript
class ExecutionResult {
    constructor(success, exitCode, stdout, stderr, executionTime, riskAssessment, warnings = [])
}
```

**Fields**:
- `success`: Boolean execution status
- `exitCode`: Process exit code
- `stdout/stderr`: Output and error streams
- `executionTime`: Execution duration in milliseconds
- `riskAssessment`: Assessed risk level
- `warnings`: Array of security warnings

#### `ScriptValidator`
**Description**: Advanced script content analysis and risk assessment

**Risk Classification Patterns**:
```javascript
this.dangerousPatterns = {
    [RiskLevel.CRITICAL]: [
        'rm -rf /', 'format', 'mkfs', '> /dev/sd', 'dd if=',
        'chmod 777', 'chown root', 'sudo su', 'curl | sh'
    ],
    [RiskLevel.HIGH]: [
        'rm -rf', 'drop database', 'truncate table', 'delete from',
        'sudo', 'passwd', 'useradd', 'systemctl stop', 'kill -9'
    ],
    [RiskLevel.MEDIUM]: [
        'rm ', 'mv ', 'cp ', 'chmod', 'chown', 'mount', 'systemctl'
    ]
};
```

**Key Methods**:

##### `assessRisk(scriptContent)`
**Multi-layered Risk Analysis**:
1. **Pattern Matching**: Scans for dangerous command patterns
2. **Dynamic Evaluation Detection**: Identifies `eval`, `$((` constructs
3. **Privilege Escalation**: Counts `sudo` usage frequency
4. **Context Analysis**: Considers command combinations

**Return Structure**:
```javascript
{
    riskLevel: 'low|medium|high|critical',
    warnings: ['Array of specific warnings']
}
```

##### `validateScriptPath(scriptPath, repoPath)`
**Path Traversal Protection**:
- Resolves absolute paths using Node.js `path.resolve()`
- Ensures scripts remain within repository boundaries
- Prevents `../` and similar traversal attacks

#### `ScriptExecutor`
**Description**: Main execution engine with multiple safety layers

**Constructor Options**:
```javascript
constructor(repoPath, useDocker = false)
```

**Key Methods**:

##### `executeScript(scriptPath, args = [], forceApproval = false)`
**Comprehensive Execution Pipeline**:

1. **Path Validation**: Ensures script is within repository bounds
2. **File Existence**: Verifies script exists and is readable
3. **Content Analysis**: Performs risk assessment on script content
4. **Approval Check**: Validates against approved scripts list
5. **Execution Method Selection**: Docker vs native execution
6. **Result Processing**: Formats and returns execution results

**Security Workflow**:
```javascript
// Risk-based execution flow
if (riskLevel === RiskLevel.CRITICAL) {
    return blocked_result;
}
if (riskLevel === RiskLevel.HIGH && !approved && !forceApproval) {
    return approval_required_result;
}
// Proceed with execution
```

##### `executeInDocker(scriptPath, args = [])`
**Containerized Execution for Enhanced Security**:
```javascript
const dockerArgs = [
    'run', '--rm',
    '--memory=256m',        // Memory limit
    '--cpus=0.5',          // CPU limit
    '--network=none',      // No network access
    '--user=nobody',       // Non-root user
    '-v', `${repoPath}:/repo:ro`, // Read-only mount
    'ubuntu:20.04'
];
```

**Features**:
- **Resource Limits**: Memory and CPU constraints
- **Network Isolation**: No external network access
- **User Restrictions**: Runs as unprivileged user
- **Read-only Filesystem**: Repository mounted read-only
- **Timeout Protection**: 5-minute execution limit

##### `executeNative(scriptPath, args = [], timeout = 300000)`
**Direct Host Execution with Monitoring**:
- Uses Node.js `spawn` for process control
- Implements timeout protection
- Captures real-time stdout/stderr
- Measures execution time
- Handles process errors gracefully

**Implementation**:
```javascript
const process = spawn(cmd[0], cmd.slice(1), {
    cwd: path.dirname(scriptPath),
    timeout: timeout
});

// Stream handling
process.stdout.on('data', (data) => stdout += data.toString());
process.stderr.on('data', (data) => stderr += data.toString());
```

##### `getScriptInfo(scriptPath)`
**Comprehensive Script Analysis**:
```javascript
{
    path: 'relative/path/to/script.sh',
    size: 1234,
    risk_level: 'medium',
    warnings: ['List of warnings'],
    approved: true,
    executable: true
}
```

### Approved Scripts Management

#### **Persistent Approval Storage**
```javascript
// Storage format: ai_agent_nodejs/approved_scripts.json
{
    "approved": [
        "monitoring/check_cpu.sh",
        "backup/mysql_backup.sh"
    ]
}
```

#### **Approval Workflow**
1. **Initial Assessment**: All scripts undergo risk analysis
2. **Approval Requirement**: High-risk scripts need explicit approval
3. **Persistent Storage**: Approvals saved to JSON file
4. **Session Persistence**: Approvals survive application restarts

---

## 🧠 src/knowledge/repositoryIndexer.js

### Purpose
Comprehensive repository content indexing system with semantic search capabilities and intelligent categorization.

### Key Classes

#### `RepositoryIndexer`
**Description**: Main indexing engine for repository content

**Constructor**:
```javascript
constructor(repoPath, dbPath = './vectordb')
```

**Configuration**:
```javascript
// Indexable file extensions
this.indexableExtensions = new Set([
    '.sh', '.py', '.sql', '.md', '.txt', '.conf', 
    '.json', '.ini', '.yaml', '.yml', '.js', '.ts'
]);

// Category classification rules
this.categories = {
    'database_patching': ['database_patching', 'patch', 'patching'],
    'migration': ['migration', 'migrate', 'migration_flows'],
    'mysql': ['mysql', 'mariadb'],
    'oracle': ['oracle', 'ora', 'rman'],
    // ... more categories
};
```

**Key Methods**:

##### `initialize()`
**System Initialization**:
- Creates vector database instance
- Sets up Git repository access
- Initializes indexing infrastructure

##### `indexRepository(forceReindex = false)`
**Comprehensive Repository Scanning**:

**Process Flow**:
1. **File Discovery**: Recursively scans repository directory
2. **Filtering**: Applies indexable file criteria
3. **Change Detection**: Uses file hashes for incremental updates
4. **Content Processing**: Extracts and chunks file content
5. **Metadata Extraction**: Generates rich metadata including Git info
6. **Vector Storage**: Stores documents in vector database

**Performance Features**:
- **Incremental Indexing**: Only processes changed files
- **Hash-based Change Detection**: MD5 checksums for content comparison
- **Batch Processing**: Efficient bulk operations
- **Progress Reporting**: Real-time indexing statistics

**Statistics Tracking**:
```javascript
{
    processed: 150,    // Total files processed
    indexed: 45,       // Files actually indexed
    errors: 2,         // Processing errors
    skipped: 103       // Unchanged files skipped
}
```

##### `extractMetadata(filePath)`
**Rich Metadata Generation**:
```javascript
{
    file_path: 'relative/path/to/file.sh',
    file_name: 'file.sh',
    file_type: '.sh',
    category: 'monitoring',
    size: 1234,
    modified: 1640995200000,
    last_commit: 'abc123...',
    last_author: 'John Doe',
    commit_date: 1640995200000
}
```

**Git Integration**:
- Uses `simple-git` library for repository access
- Extracts commit history for files
- Provides authorship and change tracking
- Handles repositories without Git gracefully

##### `categorizeFile(relativePath)`
**Intelligent File Categorization**:

**Classification Logic**:
1. **Path-based Classification**: Analyzes directory structure
2. **Keyword Matching**: Searches for category-specific terms
3. **File Extension Analysis**: Considers file types
4. **Content Hints**: Uses filename patterns

**Categories**:
- `database_patching`: Patching and update scripts
- `migration`: Database migration utilities
- `mysql/oracle/postgres/mssql`: Database-specific scripts
- `monitoring`: Health checks and metrics
- `backup/ssl/cloud_tasks`: Operational categories

##### `processFile(filePath)`
**Content Processing Pipeline**:

**For Small Files (≤1000 chars)**:
- Single document with complete content
- Full metadata preservation

**For Large Files (>1000 chars)**:
- **Chunking Strategy**: 1000 chars with 200 char overlap
- **Chunk Metadata**: Includes chunk_id and total_chunks
- **Context Preservation**: Overlapping ensures continuity

**Implementation**:
```javascript
// Chunking algorithm
splitContent(content, chunkSize = 1000, overlap = 200) {
    const chunks = [];
    let start = 0;
    
    while (start < content.length) {
        const end = Math.min(start + chunkSize, content.length);
        const chunk = content.slice(start, end);
        chunks.push(chunk);
        
        if (end === content.length) break;
        start = end - overlap;  // Ensure context continuity
    }
    
    return chunks;
}
```

##### `search(query, options = {})`
**Advanced Search Interface**:
```javascript
const options = {
    nResults: 5,           // Maximum results
    category: 'mysql',     // Category filter
    minScore: 0.0          // Minimum similarity score
};
```

**Search Features**:
- **Semantic Search**: Vector similarity using custom embeddings
- **Category Filtering**: Restrict results to specific categories
- **Score Thresholding**: Filter low-relevance results
- **Ranked Results**: Sorted by relevance score

**Result Format**:
```javascript
[{
    content: 'File content chunk...',
    metadata: { /* Rich metadata */ },
    score: 0.87  // Similarity score
}]
```

### File Processing Intelligence

#### **Should Index File Logic**
```javascript
shouldIndexFile(filePath) {
    // Skip hidden files and directories
    if (fileName.startsWith('.')) return false;
    
    // Skip binary files
    const binaryExtensions = new Set(['.pdf', '.png', '.jpg', '.zip']);
    if (binaryExtensions.has(ext)) return false;
    
    // Include known text formats
    if (this.indexableExtensions.has(ext)) return true;
    
    // Check shebang for scripts without extensions
    return this.isLikelyScript(filePath);
}
```

#### **Script Detection**
- Checks for shebang (`#!/bin/bash`) in files without extensions
- Handles executable scripts with various interpreters
- Graceful error handling for unreadable files

---

## 🗄️ src/knowledge/vectorDB.js

### Purpose
Custom vector database implementation providing semantic search capabilities using TF-IDF-style embeddings with cosine similarity.

### Key Classes

#### `VectorDBManager`
**Description**: In-memory vector database with persistence

**Data Structures**:
```javascript
this.documents = new Map();     // docId -> content
this.embeddings = new Map();    // docId -> embedding vector
this.metadata = new Map();      // docId -> metadata object
```

**Key Methods**:

##### `generateEmbedding(text)`
**Custom Text Embedding Generation**:

**Algorithm**:
1. **Tokenization**: Split text into words, remove punctuation
2. **Filtering**: Remove words shorter than 3 characters
3. **Frequency Calculation**: Count word occurrences
4. **Normalization**: Convert to relative frequencies (TF)

**Implementation**:
```javascript
generateEmbedding(text) {
    const words = text.toLowerCase()
        .replace(/[^\w\s]/g, ' ')  // Remove punctuation
        .split(/\s+/)              // Split on whitespace
        .filter(word => word.length > 2);  // Filter short words
    
    const wordFreq = {};
    words.forEach(word => {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
    });
    
    const totalWords = words.length;
    const embedding = {};
    
    Object.keys(wordFreq).forEach(word => {
        embedding[word] = wordFreq[word] / totalWords;  // TF normalization
    });
    
    return embedding;
}
```

##### `cosineSimilarity(embedding1, embedding2)`
**Vector Similarity Calculation**:

**Mathematical Implementation**:
```javascript
cosineSimilarity(emb1, emb2) {
    const allKeys = new Set([...Object.keys(emb1), ...Object.keys(emb2)]);
    
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;
    
    for (const key of allKeys) {
        const val1 = emb1[key] || 0;
        const val2 = emb2[key] || 0;
        
        dotProduct += val1 * val2;
        norm1 += val1 * val1;
        norm2 += val2 * val2;
    }
    
    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
}
```

**Features**:
- **Sparse Vector Support**: Handles missing dimensions gracefully
- **Normalized Scoring**: Returns values between 0 and 1
- **Performance Optimized**: Efficient computation for large vocabularies

##### `search(query, options = {})`
**Semantic Search Engine**:

**Search Process**:
1. **Query Embedding**: Generate embedding for search query
2. **Similarity Calculation**: Compare against all stored embeddings
3. **Filtering**: Apply metadata filters (category, file type, etc.)
4. **Ranking**: Sort by similarity score
5. **Limiting**: Return top N results

**Advanced Filtering**:
```javascript
const filters = {
    category: 'mysql',
    file_type: '.sh',
    risk_level: 'low'
};
```

##### `addDocuments(documents)`
**Bulk Document Storage**:
- Generates unique document IDs using content hashing
- Creates embeddings for all documents
- Stores metadata with full indexing
- Persists changes to disk

##### `deleteByMetadata(filters)`
**Conditional Document Removal**:
- Supports complex metadata filtering
- Bulk deletion operations
- Maintains index consistency
- Updates persistent storage

### Database Persistence

#### **Storage Format**
```javascript
// Database file: vectordb/database.json
{
    documents: { docId: content, ... },
    embeddings: { docId: embedding, ... },
    metadata: { docId: metadata, ... },
    version: '1.0.0',
    timestamp: '2024-01-01T00:00:00.000Z'
}
```

#### **Load/Save Operations**
- **Atomic Writes**: Ensures data consistency
- **Version Management**: Tracks database schema versions
- **Error Recovery**: Graceful handling of corrupted data
- **Performance**: Efficient JSON serialization

---

## 🔐 src/security/securityManager.js

### Purpose
Comprehensive security framework providing authentication, authorization, rate limiting, audit logging, and input validation.

### Key Enums and Classes

#### `SecurityLevel`
```javascript
const SecurityLevel = {
    RESTRICTED: 'restricted',  // Read-only access
    STANDARD: 'standard',      // Basic script execution
    ELEVATED: 'elevated',      // High-privilege operations
    ADMIN: 'admin'             // Full system access
};
```

#### `SecurityEvent`
**Description**: Structured audit log entry
```javascript
class SecurityEvent {
    constructor(eventType, userId, action, resource, riskLevel, success, details = {}, ipAddress = null)
}
```

**Event Categories**:
- `auth`: Authentication events (login, logout, token validation)
- `query`: AI agent query processing
- `execution`: Script execution attempts
- `admin`: Administrative operations

#### `UserSession`
**Description**: Active user session with permissions and rate limiting
```javascript
class UserSession {
    constructor(userId, sessionId, securityLevel, permissions = new Set()) {
        this.userId = userId;
        this.sessionId = sessionId;
        this.securityLevel = securityLevel;
        this.createdAt = new Date();
        this.lastActivity = new Date();
        this.permissions = permissions;
        this.rateLimitTokens = 100;
    }
}
```

#### `SecurityManager`
**Description**: Main security orchestrator

**Configuration Management**:
```javascript
const defaultConfig = {
    jwtSecret: crypto.randomBytes(32).toString('hex'),
    sessionTimeout: 3600000,      // 1 hour
    maxSessionsPerUser: 3,
    auditRetentionDays: 90,
    requireApprovalFor: ['high', 'critical'],
    blockedPatterns: [
        'rm -rf /',
        'DROP DATABASE',
        'TRUNCATE TABLE'
    ],
    restrictedPaths: [
        '/etc/passwd',
        '/etc/shadow',
        '/root'
    ]
};
```

**Key Methods**:

##### `generateSessionToken(userId, securityLevel)`
**JWT Token Generation**:
```javascript
const payload = {
    userId: userId,
    securityLevel: securityLevel,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (sessionTimeout / 1000)
};

return jwt.sign(payload, this.config.jwtSecret);
```

**Features**:
- **Cryptographic Security**: Uses HMAC-SHA256 signing
- **Expiration Management**: Configurable token lifetime
- **Payload Optimization**: Minimal claims for performance

##### `createSession(userId, securityLevel, permissions = new Set())`
**Session Management**:

**Session Lifecycle**:
1. **Limit Enforcement**: Removes oldest session if limit exceeded
2. **Unique ID Generation**: SHA-256 hash of user+timestamp
3. **Token Creation**: Generates JWT with session claims
4. **Audit Logging**: Records session creation event

**Concurrent Session Handling**:
```javascript
// Remove oldest session if limit exceeded
if (userSessions.length >= this.config.maxSessionsPerUser) {
    const oldest = userSessions.reduce((prev, current) => 
        prev.lastActivity < current.lastActivity ? prev : current
    );
    this.activeSessions.delete(oldest.sessionId);
}
```

##### `validateScriptExecution(session, scriptPath, riskLevel)`
**Multi-layer Execution Validation**:

**Validation Pipeline**:
1. **Permission Check**: Verify script execution permission
2. **Rate Limiting**: Enforce per-user request limits
3. **Risk Assessment**: Validate against security level
4. **Whitelist Verification**: Check approved scripts list
5. **Pattern Scanning**: Detect blocked command patterns

**Permission Matrix**:
```javascript
this.blockedOperations = {
    [SecurityLevel.RESTRICTED]: new Set([
        'script_execution', 'file_modification', 'system_commands'
    ]),
    [SecurityLevel.STANDARD]: new Set([
        'high_risk_scripts', 'system_modification'
    ]),
    [SecurityLevel.ELEVATED]: new Set([
        'critical_operations'
    ]),
    [SecurityLevel.ADMIN]: new Set() // No restrictions
};
```

##### `checkRateLimit(session)`
**Token Bucket Rate Limiting**:

**Rate Limit Configuration**:
```javascript
this.rateLimits = {
    [SecurityLevel.RESTRICTED]: { requests: 10, window: 300000 },   // 10/5min
    [SecurityLevel.STANDARD]: { requests: 50, window: 300000 },     // 50/5min
    [SecurityLevel.ELEVATED]: { requests: 200, window: 300000 },    // 200/5min
    [SecurityLevel.ADMIN]: { requests: 1000, window: 300000 }       // 1000/5min
};
```

**Algorithm**:
```javascript
checkRateLimit(session) {
    if (session.rateLimitTokens <= 0) {
        const windowStart = new Date(session.lastActivity.getTime() - limitConfig.window);
        if (new Date() > windowStart) {
            session.rateLimitTokens = limitConfig.requests;  // Refill bucket
        } else {
            return false;  // Rate limited
        }
    }
    
    session.rateLimitTokens--;  // Consume token
    return true;
}
```

##### `logSecurityEvent(eventType, userId, action, resource, riskLevel, success, details, ipAddress)`
**Comprehensive Audit Logging**:

**Event Processing**:
1. **Event Creation**: Structured SecurityEvent object
2. **Memory Storage**: Add to in-memory audit log
3. **File Logging**: Write to Winston logger
4. **Alert Generation**: High-risk event notifications

**Risk-based Alerting**:
```javascript
if (['high', 'critical'].includes(riskLevel) && !success) {
    logger.warn(`HIGH RISK SECURITY EVENT: ${action} by ${userId} failed`);
}
```

#### `InputSanitizer`
**Description**: Input validation and sanitization utilities

**Key Methods**:

##### `sanitizeFilename(filename)`
**Path Traversal Protection**:
```javascript
sanitizeFilename(filename) {
    // Remove dangerous characters
    let sanitized = filename.replace(/[^a-zA-Z0-9.\-_/]/g, '');
    
    // Remove path traversal attempts
    sanitized = sanitized.replace(/\.\.\//g, '').replace(/\.\.\\/g, '');
    
    return sanitized;
}
```

##### `validateQuery(query)`
**Query Safety Validation**:

**Dangerous Pattern Detection**:
```javascript
this.dangerousPatterns = [
    /[;&|`$(){}[\]<>]/,  // Shell metacharacters
    /\.\.\//,            // Path traversal
    /eval\s*\(/,         // Code evaluation
    /exec\s*\(/,         // Code execution
    /__import__/,        // Dynamic imports
];
```

**Validation Process**:
1. **Pattern Matching**: Regex-based dangerous pattern detection
2. **Length Validation**: Prevent excessively long queries
3. **Character Analysis**: Detect shell injection attempts

---

## 🌐 src/server.js

### Purpose
Express.js REST API server providing comprehensive endpoints for AI agent functionality with integrated security and middleware.

### Key Components

#### `TessellAgentServer`
**Description**: Main server class orchestrating all HTTP operations

**Initialization**:
```javascript
constructor() {
    this.app = express();
    this.port = process.env.PORT || 3000;
    this.host = process.env.HOST || '0.0.0.0';
    
    // Initialize core components
    this.securityManager = new SecurityManager();
    this.inputSanitizer = new InputSanitizer();
    this.agent = new TessellAgent(repoPath, anthropicKey);
}
```

#### **Middleware Stack**

##### Security Middleware
```javascript
// Security headers
this.app.use(helmet());

// CORS configuration
this.app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true
}));
```

##### Request Processing
```javascript
// Request logging with Winston integration
this.app.use(morgan('combined', {
    stream: { write: message => logger.info(message.trim()) }
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000,  // 15 minutes
    max: 100,                   // 100 requests per window
    message: 'Too many requests from this IP',
    standardHeaders: true
});
```

##### Body Processing
```javascript
// JSON parsing with size limits
this.app.use(express.json({ limit: '10mb' }));
this.app.use(express.urlencoded({ extended: true }));
```

#### **Authentication Middleware**

##### `authenticateToken(req, res, next)`
**JWT Token Validation**:
```javascript
authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    const session = this.securityManager.validateSession(token);
    if (!session) {
        return res.status(401).json({ error: 'Invalid or expired token' });
    }

    req.session = session;
    req.user = { id: session.userId, securityLevel: session.securityLevel };
    next();
}
```

### API Endpoints

#### **Authentication Endpoints**

##### `POST /api/login`
**User Authentication**:
```javascript
// Demo user database
const demoUsers = {
    admin: { password: 'admin123', level: SecurityLevel.ADMIN },
    user: { password: 'user123', level: SecurityLevel.STANDARD },
    guest: { password: 'guest123', level: SecurityLevel.RESTRICTED }
};
```

**Process Flow**:
1. **Credential Validation**: Check username/password against demo users
2. **Session Creation**: Generate JWT token and session
3. **Audit Logging**: Record authentication attempt
4. **Response**: Return token and user information

##### `GET /api/health`
**System Health Check**:
```javascript
{
    status: 'healthy',
    version: '1.0.0',
    timestamp: '2024-01-01T00:00:00.000Z'
}
```

#### **Agent Interaction Endpoints**

##### `POST /api/query`
**Natural Language Query Processing**:

**Request Format**:
```javascript
{
    query: "Show me Oracle patching scripts",
    execute_if_safe: false,
    context: {} // Optional additional context
}
```

**Processing Pipeline**:
1. **Input Validation**: Sanitize and validate query content
2. **Rate Limiting**: Check user request limits
3. **Agent Processing**: Route to TessellAgent.processQuery()
4. **Response Formatting**: Structure response with metadata
5. **Audit Logging**: Record query and response

**Response Format**:
```javascript
{
    message: "I found several Oracle patching scripts...",
    suggested_scripts: [
        {
            path: "database_patching/oracle_patch.sh",
            risk_level: "medium",
            category: "database_patching",
            description: "Oracle database patching script..."
        }
    ],
    execution_results: null,
    requires_approval: false,
    confidence: 0.87
}
```

##### `POST /api/execute-script`
**Script Execution with Safety Checks**:

**Request Format**:
```javascript
{
    script_path: "monitoring/check_db.sh",
    args: ["--verbose"],
    force_approval: false
}
```

**Execution Pipeline**:
1. **Path Sanitization**: Clean and validate script path
2. **Script Analysis**: Get risk assessment and metadata
3. **Permission Validation**: Check execution permissions
4. **Approval Workflow**: Handle high-risk script approval
5. **Execution**: Route to ScriptExecutor with monitoring
6. **Result Processing**: Format and return execution results

**Response Formats**:

*Successful Execution*:
```javascript
{
    script: "monitoring/check_db.sh",
    success: true,
    exit_code: 0,
    output: "Database health check passed\nAll services running",
    errors: "",
    execution_time: 1234,
    warnings: []
}
```

*Approval Required*:
```javascript
{
    requires_approval: true,
    message: "Script execution requires approval due to high risk level",
    script_info: {
        risk_level: "high",
        warnings: ["Contains sudo commands"]
    }
}
```

#### **Script Management Endpoints**

##### `GET /api/scripts`
**Script Catalog with Filtering**:

**Query Parameters**:
- `category`: Filter by script category
- `risk_level`: Filter by risk level
- `approved`: Filter by approval status

**Response**:
```javascript
{
    scripts: [
        {
            path: "backup/mysql_backup.sh",
            name: "mysql_backup.sh", 
            category: "backup",
            risk_level: "medium",
            approved: true,
            size: 2048,
            warnings: []
        }
    ]
}
```

##### `GET /api/script/:scriptPath(*)/info`
**Detailed Script Information**:

**Response**:
```javascript
{
    path: "database_patching/oracle_patch.sh",
    description: "Comprehensive Oracle database patching utility...",
    usage: "Usage: ./oracle_patch.sh [options]\n  -p PATCH_ID  Patch identifier\n  -b           Create backup before patching",
    risk_level: "high",
    warnings: ["Requires database downtime", "Creates system backups"],
    size: 4096,
    category: "database_patching"
}
```

#### **Administrative Endpoints**

##### `GET /api/audit-log` (Admin Only)
**Security Event Monitoring**:

**Query Parameters**:
- `hours`: Time range for events (default: 24)
- `user_id`: Filter by specific user
- `event_type`: Filter by event category

**Response**:
```javascript
{
    audit_log: [
        {
            timestamp: "2024-01-01T12:00:00.000Z",
            eventType: "execution",
            userId: "admin",
            action: "script_executed",
            resource: "backup/mysql_backup.sh",
            riskLevel: "medium",
            success: true,
            details: { exit_code: 0 },
            ipAddress: "*************"
        }
    ]
}
```

##### `POST /api/initialize`
**Knowledge Base Management**:

**Request**:
```javascript
{
    force_reindex: true  // Force complete reindexing
}
```

**Response**:
```javascript
{
    message: "Knowledge base updated",
    stats: {
        processed: 150,
        indexed: 45,
        errors: 0,
        skipped: 105
    }
}
```

##### `GET /api/stats`
**System Statistics**:
```javascript
{
    agent: {
        repository: {
            total_documents: 450,
            categories: ["mysql", "oracle", "backup"],
            file_types: [".sh", ".py", ".sql"]
        },
        approved_scripts: 25,
        claude_enabled: true,
        agent_version: "1.0.0"
    },
    security: {
        active_sessions: 3,
        user_sessions: 1
    }
}
```

### Error Handling

#### **Global Error Handler**
```javascript
this.app.use((error, req, res, next) => {
    logger.error('Unhandled error:', error);
    res.status(500).json({ error: 'Internal server error' });
});
```

#### **404 Handler**
```javascript
this.app.use((req, res) => {
    res.status(404).json({ error: 'Not found' });
});
```

#### **Security Error Responses**
- **401 Unauthorized**: Invalid or missing authentication
- **403 Forbidden**: Insufficient permissions
- **429 Too Many Requests**: Rate limit exceeded
- **400 Bad Request**: Invalid input or validation failure

---

## 📱 Frontend Architecture (client/)

### Purpose
Modern React application providing intuitive web interface for the AI agent with Material-UI components and responsive design.

### Project Structure
```
client/
├── src/
│   ├── components/
│   │   ├── LoginPage.js           # Authentication interface
│   │   ├── Navbar.js              # Navigation with role-based menus
│   │   ├── Dashboard.js           # System overview and statistics
│   │   ├── ChatInterface.js       # Main AI chat interface
│   │   ├── ScriptsManager.js      # Script browsing and execution
│   │   └── AuditLog.js            # Security event monitoring
│   ├── contexts/
│   │   └── AuthContext.js         # Authentication state management
│   ├── App.js                     # Main application component
│   └── index.js                   # Application entry point
└── package.json                   # Frontend dependencies
```

### Key Components

#### **src/contexts/AuthContext.js**
**Purpose**: Centralized authentication state management using React Context

**Key Features**:
```javascript
const AuthContext = createContext();

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
```

**State Management**:
```javascript
const [user, setUser] = useState(null);
const [token, setToken] = useState(null);
const [loading, setLoading] = useState(true);
```

**Methods**:
- `login(username, password)`: Authenticate user and store session
- `logout()`: Clear session and redirect to login
- `verifyToken(token)`: Validate stored token on app startup

**Persistent Storage**:
```javascript
// Store authentication state
localStorage.setItem('tessell_token', token);
localStorage.setItem('tessell_user', JSON.stringify(userData));

// Set default axios header
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
```

#### **src/components/LoginPage.js**
**Purpose**: User authentication interface with demo credentials

**Key Features**:
- Material-UI form components with validation
- Error handling and loading states
- Demo credentials display for testing
- Responsive design for mobile devices

**Authentication Flow**:
```javascript
const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(username, password);
    
    if (!result.success) {
        setError(result.error);
    }
    
    setLoading(false);
};
```

#### **src/components/Navbar.js**
**Purpose**: Main navigation with role-based menu items

**Dynamic Menu Generation**:
```javascript
const menuItems = [
    { label: 'Dashboard', path: '/dashboard', icon: <DashboardIcon /> },
    { label: 'Chat', path: '/chat', icon: <ChatIcon /> },
    { label: 'Scripts', path: '/scripts', icon: <CodeIcon /> },
];

// Add audit log for admin users
if (user?.securityLevel === 'admin') {
    menuItems.push({ label: 'Audit Log', path: '/audit', icon: <SecurityIcon /> });
}
```

**User Information Display**:
- User ID chip with styling
- Security level indicator with color coding
- Logout functionality

#### **src/components/Dashboard.js**
**Purpose**: System overview with statistics and quick actions

**Data Sources**:
```javascript
const fetchData = async () => {
    const [statsResponse, healthResponse] = await Promise.all([
        axios.get('/api/stats'),
        axios.get('/api/health')
    ]);
    
    setStats(statsResponse.data);
    setHealth(healthResponse.data);
};
```

**Key Metrics Display**:
- System health status
- Repository document count
- Active session monitoring
- Claude API status

**Administrative Functions**:
- Knowledge base reindexing (elevated users)
- System statistics monitoring
- Quick action navigation

#### **src/components/ChatInterface.js**
**Purpose**: Main AI chat interface with rich message rendering

**State Management**:
```javascript
const [messages, setMessages] = useState([]);
const [inputMessage, setInputMessage] = useState('');
const [loading, setLoading] = useState(false);
const [autoExecute, setAutoExecute] = useState(false);
const [showDebug, setShowDebug] = useState(false);
```

**Message Processing**:
```javascript
const sendMessage = async () => {
    const userMessage = {
        role: 'user',
        content: inputMessage,
        timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);

    const response = await axios.post('/api/query', {
        query: inputMessage,
        execute_if_safe: autoExecute
    });

    const assistantMessage = {
        role: 'assistant',
        content: response.data.message,
        timestamp: new Date().toISOString(),
        metadata: response.data
    };

    setMessages(prev => [...prev, assistantMessage]);
};
```

**Rich Message Rendering**:
- **Markdown Support**: ReactMarkdown for formatted responses
- **Script Suggestions**: Interactive cards with execution buttons
- **Execution Results**: Formatted output with syntax highlighting
- **Approval Workflows**: Interactive approval dialogs
- **Debug Information**: Collapsible metadata display

**Interactive Elements**:
```javascript
// Script execution button
<Button 
    size="small" 
    startIcon={<PlayIcon />}
    onClick={() => executeScript(script.path)}
    color={script.risk_level === 'low' ? 'primary' : 'warning'}
>
    Execute
</Button>

// Script information button
<Button 
    size="small" 
    startIcon={<InfoIcon />}
    onClick={() => getScriptInfo(script.path)}
>
    Info
</Button>
```

#### **src/components/ScriptsManager.js**
**Purpose**: Comprehensive script browsing and management interface

**Filtering System**:
```javascript
const filterScripts = () => {
    let filtered = scripts;

    // Search term filtering
    if (searchTerm) {
        filtered = filtered.filter(script => 
            script.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            script.path.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }

    // Category filtering
    if (categoryFilter !== 'all') {
        filtered = filtered.filter(script => script.category === categoryFilter);
    }

    // Risk level filtering
    if (riskFilter !== 'all') {
        filtered = filtered.filter(script => script.risk_level === riskFilter);
    }

    setFilteredScripts(filtered);
};
```

**Script Card Display**:
- **Risk Level Indicators**: Color-coded chips with icons
- **Approval Status**: Visual indicators for approved scripts
- **Warning Alerts**: Security warnings display
- **Interactive Actions**: Info and execute buttons

**Modal Dialogs**:
- **Script Information**: Detailed metadata and usage
- **Execution Interface**: Safe execution with confirmations
- **Result Display**: Real-time execution feedback

#### **src/components/AuditLog.js**
**Purpose**: Security event monitoring interface (Admin only)

**Data Table Features**:
```javascript
// Filterable audit log with pagination
<TablePagination
    rowsPerPageOptions={[10, 25, 50, 100]}
    component="div"
    count={filteredLogs.length}
    rowsPerPage={rowsPerPage}
    page={page}
    onPageChange={(event, newPage) => setPage(newPage)}
/>
```

**Event Visualization**:
- **Status Icons**: Success/failure indicators
- **Risk Level Chips**: Color-coded risk assessment
- **Event Type Badges**: Categorized event types
- **Timestamp Formatting**: Human-readable dates
- **IP Address Tracking**: Source identification

**Filtering Options**:
- Time range selection (1 hour to 1 month)
- Event type filtering (auth, query, execution, admin)
- User-specific filtering
- Real-time refresh capabilities

### UI/UX Design Patterns

#### **Material-UI Integration**
```javascript
import {
    Box, Paper, TextField, Button, Typography,
    Alert, CircularProgress, Card, CardContent
} from '@mui/material';
```

#### **Responsive Design**
- **Grid System**: Material-UI Grid for responsive layouts
- **Breakpoint Management**: Mobile-first responsive design
- **Touch-Friendly**: Appropriate sizing for mobile interactions

#### **Color Coding System**
```javascript
const getRiskColor = (riskLevel) => {
    switch (riskLevel) {
        case 'low': return 'success';
        case 'medium': return 'warning';
        case 'high': return 'error';
        case 'critical': return 'error';
        default: return 'default';
    }
};
```

#### **Loading States**
- **Skeleton Loading**: Placeholder content during data fetching
- **Progress Indicators**: CircularProgress for operations
- **Disabled States**: Prevent interaction during processing

---

## 🛠️ Development and Deployment

### Development Workflow

#### **Local Development Setup**
```bash
# Backend setup
cd ai_agent_nodejs
npm install
cp .env.example .env
# Edit .env with your configuration

# Frontend setup
npm run install:client

# Start development servers
npm run dev  # Starts both backend and frontend
```

#### **Environment Configuration**
```bash
# Essential environment variables
TESSELL_REPO_PATH=/path/to/tessellcs-ops
ANTHROPIC_API_KEY=sk-ant-your-key-here
NODE_ENV=development
PORT=3000
LOG_LEVEL=info
```

### Testing Strategy

#### **Backend Testing**
```javascript
// Component testing examples
const agent = new TessellAgent('/path/to/repo');
await agent.initialize();

// Test query processing
const response = await agent.processQuery('Show me MySQL scripts');
console.assert(response.suggestedScripts.length > 0);

// Test script execution
const executor = new ScriptExecutor('/path/to/repo');
const result = await executor.executeScript('test_script.sh');
console.assert(result.success === true);
```

#### **Frontend Testing**
```bash
# Run React testing suite
cd client
npm test

# Run with coverage
npm test -- --coverage
```

### Production Deployment

#### **Docker Deployment**
```dockerfile
# Multi-stage build
FROM node:18-alpine AS backend
WORKDIR /app
COPY package*.json ./
RUN npm ci --production

FROM node:18-alpine AS frontend
WORKDIR /app/client
COPY client/package*.json ./
RUN npm ci
COPY client/ ./
RUN npm run build

FROM node:18-alpine AS production
WORKDIR /app
COPY --from=backend /app/node_modules ./node_modules
COPY --from=frontend /app/client/build ./client/build
COPY src/ ./src/
COPY package.json ./
EXPOSE 3000
CMD ["npm", "start"]
```

#### **Environment-Specific Configuration**
```yaml
# docker-compose.production.yml
version: '3.8'
services:
  tessell-ai-agent:
    build: .
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=warn
      - USE_DOCKER=true
    volumes:
      - /path/to/tessellcs-ops:/app/repository:ro
      - ./logs:/app/logs
    restart: unless-stopped
```

### Monitoring and Maintenance

#### **Logging Configuration**
```javascript
// Winston logger setup
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    transports: [
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' })
    ]
});
```

#### **Health Monitoring**
```bash
# Health check endpoint
curl http://localhost:3000/api/health

# System statistics
curl -H "Authorization: Bearer <token>" http://localhost:3000/api/stats
```

#### **Performance Optimization**
- **Database Indexing**: Efficient vector search algorithms
- **Caching Strategy**: In-memory caching for frequently accessed data
- **Rate Limiting**: Prevent API abuse and resource exhaustion
- **Resource Limits**: Docker constraints for script execution

### Security Considerations

#### **Production Security Checklist**
- [ ] Change default JWT secret
- [ ] Enable HTTPS with proper certificates
- [ ] Configure CORS for specific origins
- [ ] Set up proper firewall rules
- [ ] Enable audit logging
- [ ] Regular security updates
- [ ] Monitor failed authentication attempts

#### **Data Protection**
- [ ] Encrypt sensitive configuration
- [ ] Secure API key storage
- [ ] Regular backup of audit logs
- [ ] Access control for log files

This comprehensive technical documentation provides detailed insights into every component of the Node.js TessellCS AI Agent implementation, enabling effective development, maintenance, and extension of the system.