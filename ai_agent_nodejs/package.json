{"name": "tessellcs-ai-agent-nodejs", "version": "1.0.0", "description": "Node.js AI Agent for TessellCS Operations Repository", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "build": "npm run build:client", "build:client": "cd client && npm run build", "install:client": "cd client && npm install", "postinstall": "npm run install:client"}, "keywords": ["ai", "agent", "tessell", "database", "operations", "openai", "anthropic", "ollama", "claude"], "author": "TessellCS Team", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.17.1", "@pinecone-database/pinecone": "^1.1.2", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "chroma-js": "^2.4.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "openai": "^4.20.0", "simple-git": "^3.20.0", "uuid": "^9.0.1", "validator": "^13.11.0", "vectordb": "^0.4.0", "winston": "^3.11.0", "yaml": "^2.3.4"}, "devDependencies": {"@types/node": "^20.10.5", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}