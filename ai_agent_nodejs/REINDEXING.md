# Vector Database Re-indexing Guide

This guide explains how to re-index the vector database (`vectordb/database.json`) when new scripts or documentation are added to the repository.

## Overview

The AI agent uses a vector database to provide intelligent script recommendations and search functionality. When you add new scripts to the repository, the vector database needs to be updated to include these new files.

## Re-indexing Methods

### Method 1: Using the Web API (Recommended)

#### Prerequisites
1. Start the AI agent server:
```bash
cd /Users/<USER>/DEV/claude-test/tessellcs-ops/ai_agent_nodejs
npm start
```

2. The server will run on `http://localhost:3000` by default

#### Re-index via API
```bash
# Login first (using demo credentials)
curl -X POST http://localhost:3000/api/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# Save the token from the response, then re-index:
curl -X POST http://localhost:3000/api/initialize \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{"force_reindex": true}'
```

**Options:**
- `"force_reindex": true` - Re-indexes all files (complete rebuild)
- `"force_reindex": false` - Incremental indexing (only new/changed files)

### Method 2: Using the Web Interface

1. Open `http://localhost:3000` in your browser
2. Login with:
   - Username: `admin`
   - Password: `admin123`
3. Navigate to the admin/settings section
4. Look for "Re-index Repository" or "Initialize Knowledge Base" button
5. Click to trigger re-indexing

### Method 3: Direct Node.js Script

Use the provided `reindex.js` script:

```bash
cd /Users/<USER>/DEV/claude-test/tessellcs-ops/ai_agent_nodejs

# Incremental re-index (only new/changed files)
node reindex.js

# Force complete re-index (all files)
node reindex.js --force
```

## Environment Configuration

Ensure you have the correct environment variables set. Create a `.env` file in the `ai_agent_nodejs` directory:

```bash
# Repository path (adjust as needed)
TESSELL_REPO_PATH=/Users/<USER>/DEV/claude-test/tessellcs-ops

# Server configuration
PORT=3000
HOST=0.0.0.0
NODE_ENV=development

# LLM Configuration (optional - defaults to template mode)
LLM_ENGINE=template
```

## What Gets Indexed

The system automatically indexes the following file types:
- **Scripts**: `.sh`, `.py`, `.sql`
- **Documentation**: `.md`, `.txt`
- **Configuration**: `.conf`, `.json`, `.ini`, `.yaml`, `.yml`
- **Code**: `.js`, `.ts`
- **Executable files**: Files without extensions that start with `#!` (shebang)

### Excluded Directories
- `.git`
- `node_modules`
- `__pycache__`
- `venv`
- Hidden directories (starting with `.`)

### File Categorization
Files are automatically categorized based on their path and content:
- `database_patching` - Database patching related files
- `migration` - Database migration scripts
- `mysql`, `oracle`, `postgres`, `mssql` - Database-specific scripts
- `cloud_tasks` - Cloud infrastructure scripts
- `monitoring` - Metrics and monitoring scripts
- `backup` - Backup and restore scripts
- `ssl` - SSL/TLS certificate management
- `documentation` - README and documentation files
- `configuration` - Configuration files
- `script` - General scripts

## Monitoring Progress

During re-indexing, you'll see progress information:
- **Processed**: Total number of files examined
- **Indexed**: Number of files successfully added to the database
- **Skipped**: Number of files that haven't changed (incremental mode only)
- **Errors**: Number of files that failed to process

Example output:
```
Starting repository indexing: /path/to/tessellcs-ops
Found 127 files to process
Processed 10/127 files
Processed 20/127 files
...
Indexing complete: { processed: 127, indexed: 45, errors: 0, skipped: 82 }
```

## When to Re-index

### Automatic Re-indexing
The system will automatically initialize the knowledge base when:
- The server starts and no vector database exists
- The vector database is empty

### Manual Re-indexing Required
You should manually re-index when:
- Adding new scripts to the repository
- Modifying existing scripts significantly
- Adding new documentation
- Changing file locations or directory structure
- After major repository updates

### Incremental vs. Force Re-indexing

**Incremental Re-indexing** (`force_reindex: false`):
- Only processes new or modified files
- Faster execution
- Uses MD5 hash comparison to detect changes
- Recommended for regular updates

**Force Re-indexing** (`force_reindex: true`):
- Processes all files regardless of changes
- Slower but ensures complete consistency
- Recommended after major repository changes
- Use when troubleshooting search issues

## Troubleshooting

### Common Issues

1. **"No documents found" in search results**
   - Run force re-indexing: `node reindex.js --force`

2. **New scripts not appearing in recommendations**
   - Run incremental re-indexing: `node reindex.js`

3. **Permission errors during indexing**
   - Check file permissions in the repository
   - Ensure the `vectordb/` directory is writable

4. **Server fails to start with "Knowledge base initialization failed"**
   - Delete the `vectordb/` directory and restart the server
   - The system will rebuild the database automatically

### Verification

To verify successful re-indexing:

1. **Check database stats via API**:
```bash
curl -X GET http://localhost:3000/api/stats \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

2. **Test search functionality**:
```bash
curl -X POST http://localhost:3000/api/query \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{"query": "mysql migration script"}'
```

3. **Verify file counts**:
   - Check that `total_documents` in stats matches expected file count
   - Verify categories contain appropriate file counts

## Best Practices

1. **Regular Maintenance**
   - Run incremental re-indexing weekly or after significant changes
   - Monitor the database size and performance

2. **Development Workflow**
   - Re-index after adding new scripts before testing AI recommendations
   - Include re-indexing in deployment procedures

3. **Performance Optimization**
   - Use incremental re-indexing for routine updates
   - Reserve force re-indexing for major changes or troubleshooting

4. **Backup Considerations**
   - The vector database can be regenerated from source files
   - Consider excluding `vectordb/` from backups to save space
   - Document the re-indexing process for team members

## Security Notes

- Re-indexing requires admin or elevated privileges
- All indexing operations are logged in the security audit log
- Sensitive files (containing credentials) should be excluded from indexing
- Review the file categorization to ensure proper access controls