# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is the **tessellcs-ops** repository containing scripts and automation tools for the Customer Success Team at Tessell. The repository is organized into functional areas for database operations, cloud infrastructure, and operational tasks.

## Repository Structure

The codebase is organized into the following main categories:

- **`cloud_tasks/`** - Cloud infrastructure validation scripts (network, VM, storage health checks)
- **`data_collection/`** - Database and system metrics collection scripts
- **`db_tasks/`** - Database administration tasks (health checks, cleanup, monitoring)
- **`database_patching/`** - Oracle database patching automation framework with backup and rollback capabilities
- **`migration_flows/`** - Database migration automation with validation and rollback capabilities
- **`os_tasks/`** - Operating system level tasks and operations
- **`product_tasks/`** - Internal product validation (metadata drift, K8s health, Terraform state)
- **`old_scripts/`** - Legacy scripts organized by database engine (MySQL, Oracle, MSSQL, Postgres)
- **`sizing_engine/`** - Database sizing and capacity planning tools
- **`shared_documentations/`** - Common documentation and guidelines

## Key Scripts and Tools

### MySQL Migration Automation
- **Location**: `old_scripts/mysql_migration_automation/`
- **Main Script**: `mysql_migrator.sh`
- **Purpose**: Comprehensive MySQL database migration with pre-checks, validation, and CDC replication
- **Features**: Schema compatibility checks, selective migration, user migration, post-migration validation
- **Requirements**: MySQL 8.0.23+ for CDC replication support

### Oracle Database Patching Automation
- **Location**: `database_patching/oracledb_patching/`
- **Main Script**: `oracle_patch_automation.sh`
- **Purpose**: Comprehensive Oracle database patching with validation, backup, and rollback
- **Key Features**: Pre/post-patch validation, RMAN/snapshot backups, automated rollback, RAC support
- **Usage**: `./oracle_patch_automation.sh apply-patch -p 12345678 -s /patches/p12345678.zip -t OPATCH`
- **Configuration**: Edit `patch_config.conf` for environment-specific settings

### SSL Certificate Management
- **Location**: `old_scripts/tessell-ops-task/ssl-generation-script/`
- **Main Scripts**: `generate_keys_from_cloud_secrets.py`, `generate_and_verify_cert.py`
- **Purpose**: TLS certificate generation and management using AWS/Azure Key Vault
- **Dependencies**: Poetry for dependency management (`poetry install`, `poetry shell`)

## Bash Scripting Standards

All bash scripts should follow the guidelines in `script-guidelines/bash/README.md`:

### Required Script Header
```bash
#!/usr/bin/env bash
set -euo pipefail
trap 'log_error "Script failed at line $LINENO"; exit 1' ERR
```

### Standard Logging Functions
```bash
log_info()  { echo -e "\033[1;32m[INFO]\033[0m $*"; }
log_warn()  { echo -e "\033[1;33m[WARN]\033[0m $*"; }
log_error() { echo -e "\033[1;31m[ERROR]\033[0m $*"; }
exit_on_error() { log_error "$1"; exit 1; }
```

### Script Structure
- Use `snake_case` for files and functions
- Store scripts in appropriate category directories
- Include usage/help functions for complex scripts
- Use `mktemp` for temporary files
- Always quote variables: `"$var"`
- Validate all inputs and arguments

## Development Guidelines

- **File Organization**: Scripts are categorized by function (db_tasks, cloud_tasks, database_patching, etc.)
- **Naming Convention**: Use descriptive names with appropriate extensions (.sh for bash, .py for Python)
- **Error Handling**: All scripts must include proper error handling and logging
- **Validation**: Include pre-requisite checks and input validation
- **Configuration**: Use .conf files for environment-specific settings (see database_patching examples)
- **Modular Design**: Break complex automation into separate, reusable modules
- **Documentation**: Each major script directory includes its own README.md

## Security Considerations

- Never commit secrets or credentials to the repository
- Use secure credential management (AWS Secrets Manager, Azure Key Vault)
- Validate all file paths before operations (especially deletions)
- Follow principle of least privilege for database user permissions

## Testing and Validation

- Test scripts in non-production environments first
- Use validation reports for migration scripts
- Include rollback procedures for critical operations
- Monitor logs for errors and performance issues