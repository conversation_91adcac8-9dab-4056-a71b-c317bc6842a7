#!/usr/bin/env bash
set -euo pipefail
trap 'log_error "<PERSON><PERSON><PERSON> failed at line $LINENO"; exit 1' ERR

log_info()  { echo -e "\033[1;32m[INFO]\033[0m $*"; }
log_warn()  { echo -e "\033[1;33m[WARN]\033[0m $*"; }
log_error() { echo -e "\033[1;31m[ERROR]\033[0m $*"; }
exit_on_error() { log_error "$1"; exit 1; }

usage() {
  cat <<EOF

Usage: check_cpu_usage.sh [OS] [OPTIONS]

Description:
  This script checks current CPU usage percentage on Linux and macOS systems and displays it on the command prompt.
  If no OS is specified, it will auto-detect your operating system.

Arguments:
  OS                   Target operating system (linux, mac, macos, auto) - OPTIONAL
                       If not provided, OS will be auto-detected

Options:
  -c, --continuous      Run continuously with 5-second intervals (Ctrl+C to stop)
  -i, --interval <sec>  Set custom interval in seconds for continuous mode (default: 5)
  -h, --help           Show this help message and exit

Examples:
  # Auto-detect OS and check CPU usage (simplest)
  ./check_cpu_usage.sh

  # Check CPU usage once on macOS
  ./check_cpu_usage.sh mac

  # Check CPU usage once on Linux
  ./check_cpu_usage.sh linux

  # Auto-detect OS and check CPU usage
  ./check_cpu_usage.sh auto

  # Auto-detect OS and run continuously
  ./check_cpu_usage.sh --continuous

  # Check CPU usage continuously on macOS every 5 seconds
  ./check_cpu_usage.sh mac --continuous

  # Check CPU usage continuously on Linux every 2 seconds
  ./check_cpu_usage.sh linux --continuous --interval 2

EOF
  exit 1
}

detect_os() {
  local detected_os
  case "$(uname -s)" in
    Linux*)     detected_os="linux" ;;
    Darwin*)    detected_os="mac" ;;
    *)          exit_on_error "Unsupported operating system: $(uname -s)" ;;
  esac
  echo "$detected_os"
}

get_cpu_usage_linux() {
  # Method 1: Using /proc/stat (most reliable for Linux)
  if [[ -f /proc/stat ]]; then
    # Read CPU stats twice with 1 second interval for accurate calculation
    cpu_stats1=$(grep '^cpu ' /proc/stat)
    sleep 1
    cpu_stats2=$(grep '^cpu ' /proc/stat)
    
    # Extract values from first reading
    read -r cpu user1 nice1 system1 idle1 iowait1 irq1 softirq1 steal1 guest1 guest_nice1 <<< "$cpu_stats1"
    # Extract values from second reading  
    read -r cpu user2 nice2 system2 idle2 iowait2 irq2 softirq2 steal2 guest2 guest_nice2 <<< "$cpu_stats2"
    
    # Calculate differences
    user_diff=$((user2 - user1))
    nice_diff=$((nice2 - nice1))
    system_diff=$((system2 - system1))
    idle_diff=$((idle2 - idle1))
    iowait_diff=$((iowait2 - iowait1))
    irq_diff=$((irq2 - irq1))
    softirq_diff=$((softirq2 - softirq1))
    steal_diff=$((steal2 - steal1))
    
    # Calculate total and active time
    total_diff=$((user_diff + nice_diff + system_diff + idle_diff + iowait_diff + irq_diff + softirq_diff + steal_diff))
    active_diff=$((total_diff - idle_diff))
    
    # Calculate CPU usage percentage
    if [[ $total_diff -gt 0 ]]; then
      cpu_usage=$(awk "BEGIN {printf \"%.2f\", ($active_diff * 100) / $total_diff}")
    else
      cpu_usage="0.00"
    fi
    
  # Method 2: Fallback using top command for Linux
  elif command -v top &> /dev/null; then
    log_warn "/proc/stat not available, using top command as fallback"
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    
  # Method 3: Fallback using vmstat for Linux
  elif command -v vmstat &> /dev/null; then
    log_warn "/proc/stat not available, using vmstat as fallback" 
    cpu_idle=$(vmstat 1 2 | tail -1 | awk '{print $15}')
    cpu_usage=$(awk "BEGIN {printf \"%.2f\", 100 - $cpu_idle}")
    
  else
    exit_on_error "Unable to determine CPU usage. /proc/stat, top, and vmstat are not available."
  fi
  
  echo "$cpu_usage"
}

get_cpu_usage_mac() {
  # Method 1: Using top command (most reliable for macOS)
  if command -v top &> /dev/null; then
    # Use top to get CPU usage on macOS
    cpu_usage=$(top -l1 -n0 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
    
    # If the above doesn't work, try alternative top format
    if [[ -z "$cpu_usage" || "$cpu_usage" == "" ]]; then
      cpu_usage=$(top -l1 -n0 | awk '/CPU usage/ {print $3}' | sed 's/%//')
    fi
    
    # If still empty, try parsing different top output format
    if [[ -z "$cpu_usage" || "$cpu_usage" == "" ]]; then
      # Get CPU idle percentage and calculate usage
      cpu_idle=$(top -l1 -n0 | awk '/CPU usage/ {print $7}' | sed 's/%//' | sed 's/idle//')
      if [[ -n "$cpu_idle" && "$cpu_idle" != "" ]]; then
        cpu_usage=$(awk "BEGIN {printf \"%.2f\", 100 - $cpu_idle}")
      fi
    fi
    
  # Method 2: Using iostat (if available on macOS)
  elif command -v iostat &> /dev/null; then
    log_warn "top command issues, using iostat as fallback"
    cpu_idle=$(iostat -c 2 | tail -1 | awk '{print $6}')
    cpu_usage=$(awk "BEGIN {printf \"%.2f\", 100 - $cpu_idle}")
    
  # Method 3: Using sar (if available on macOS)
  elif command -v sar &> /dev/null; then
    log_warn "top and iostat not available, using sar as fallback"
    cpu_idle=$(sar -u 1 1 | tail -1 | awk '{print $5}')
    cpu_usage=$(awk "BEGIN {printf \"%.2f\", 100 - $cpu_idle}")
    
  else
    exit_on_error "Unable to determine CPU usage. top, iostat, and sar are not available on macOS."
  fi
  
  # Ensure we have a valid number
  if [[ -z "$cpu_usage" || "$cpu_usage" == "" ]]; then
    exit_on_error "Failed to parse CPU usage from system commands"
  fi
  
  echo "$cpu_usage"
}

get_cpu_usage() {
  local target_os="$1"
  
  case "$target_os" in
    linux)
      get_cpu_usage_linux
      ;;
    mac|macos)
      get_cpu_usage_mac
      ;;
    *)
      exit_on_error "Unsupported OS: $target_os"
      ;;
  esac
}

display_cpu_info() {
  local cpu_usage="$1"
  local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  
  # Color coding based on CPU usage
  if (( $(echo "$cpu_usage >= 80" | bc -l) )); then
    echo -e "[$timestamp] CPU Usage: \033[1;31m${cpu_usage}%\033[0m (HIGH)"
  elif (( $(echo "$cpu_usage >= 50" | bc -l) )); then
    echo -e "[$timestamp] CPU Usage: \033[1;33m${cpu_usage}%\033[0m (MEDIUM)"
  else
    echo -e "[$timestamp] CPU Usage: \033[1;32m${cpu_usage}%\033[0m (LOW)"
  fi
}

main() {
  local continuous=false
  local interval=5
  local target_os=""
  
  # If no arguments provided, auto-detect OS
  if [[ $# -eq 0 ]]; then
    target_os="auto"
  else
    # First argument should be the OS or option
    case "$1" in
      -h|--help)
        usage
        ;;
      linux|mac|macos|auto)
        target_os="$1"
        shift
        ;;
      -c|--continuous|-i|--interval)
        # If first argument is an option, auto-detect OS
        target_os="auto"
        ;;
      *)
        exit_on_error "Invalid OS argument: $1. Valid options: linux, mac, macos, auto. Use -h for help."
        ;;
    esac
  fi
  
  # Handle auto-detection
  if [[ "$target_os" == "auto" ]]; then
    target_os=$(detect_os)
    log_info "Auto-detected OS: $target_os"
  fi
  
  # Parse remaining command line arguments
  while [[ $# -gt 0 ]]; do
    case $1 in
      -c|--continuous)
        continuous=true
        shift
        ;;
      -i|--interval)
        if [[ -n "${2:-}" && "$2" =~ ^[0-9]+$ ]]; then
          interval="$2"
          shift 2
        else
          exit_on_error "Invalid interval value. Please provide a positive integer."
        fi
        ;;
      -h|--help)
        usage
        ;;
      *)
        exit_on_error "Unknown option: $1. Use -h for help."
        ;;
    esac
  done
  
  # Check if bc is available for floating point calculations
  if ! command -v bc &> /dev/null; then
    log_warn "bc command not found. CPU usage calculations may be less precise."
  fi
  
  log_info "Starting CPU usage monitoring for $target_os..."
  
  if [[ "$continuous" == true ]]; then
    log_info "Running in continuous mode (interval: ${interval}s). Press Ctrl+C to stop."
    echo "----------------------------------------"
    
    # Set up trap for graceful exit
    trap 'echo; log_info "Monitoring stopped."; exit 0' INT
    
    while true; do
      cpu_usage=$(get_cpu_usage "$target_os")
      display_cpu_info "$cpu_usage"
      sleep "$interval"
    done
  else
    # Single check mode
    cpu_usage=$(get_cpu_usage "$target_os")
    display_cpu_info "$cpu_usage"
  fi
}

main "$@"