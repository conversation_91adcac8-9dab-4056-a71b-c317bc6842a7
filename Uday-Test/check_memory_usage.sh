#!/usr/bin/env bash
set -euo pipefail
trap 'log_error "<PERSON><PERSON><PERSON> failed at line $LINENO"; exit 1' ERR

log_info()  { echo -e "\033[1;32m[INFO]\033[0m $*"; }
log_warn()  { echo -e "\033[1;33m[WARN]\033[0m $*"; }
log_error() { echo -e "\033[1;31m[ERROR]\033[0m $*"; }
exit_on_error() { log_error "$1"; exit 1; }

usage() {
  cat <<EOF

Usage: check_memory_usage.sh [OS] [OPTIONS]

Description:
  This script checks current memory usage on Linux and macOS systems using simple commands.
  If no OS is specified, it will auto-detect your operating system.

Arguments:
  OS                   Target operating system (linux, mac, macos, auto) - OPTIONAL
                       If not provided, OS will be auto-detected

Options:
  -c, --continuous      Run continuously with 5-second intervals (Ctrl+C to stop)
  -i, --interval <sec>  Set custom interval in seconds for continuous mode (default: 5)
  -h, --help           Show this help message and exit

Examples:
  # Auto-detect OS and check memory usage (simplest)
  ./check_memory_usage.sh

  # Check memory usage once on macOS
  ./check_memory_usage.sh mac

  # Check memory usage once on Linux
  ./check_memory_usage.sh linux

  # Auto-detect OS and check memory usage
  ./check_memory_usage.sh auto

  # Auto-detect OS and run continuously
  ./check_memory_usage.sh --continuous

  # Check memory usage continuously on macOS every 5 seconds
  ./check_memory_usage.sh mac --continuous

EOF
  exit 1
}

detect_os() {
  local detected_os
  case "$(uname -s)" in
    Linux*)     detected_os="linux" ;;
    Darwin*)    detected_os="mac" ;;
    *)          exit_on_error "Unsupported operating system: $(uname -s)" ;;
  esac
  echo "$detected_os"
}

bytes_to_gb() {
  local bytes=$1
  echo "scale=2; $bytes / (1024*1024*1024)" | bc
}

kb_to_gb() {
  local kb=$1
  echo "scale=2; $kb / (1024*1024)" | bc
}

get_memory_usage_linux() {
  if command -v free &> /dev/null; then
    # Use free command - simple and reliable
    local free_output=$(free -k)
    local mem_line=$(echo "$free_output" | grep '^Mem:')
    
    local total_kb=$(echo "$mem_line" | awk '{print $2}')
    local used_kb=$(echo "$mem_line" | awk '{print $3}')
    local available_kb=$(echo "$mem_line" | awk '{print $7}')
    
    # If available column doesn't exist, use free
    if [[ -z "$available_kb" ]]; then
      available_kb=$(echo "$mem_line" | awk '{print $4}')
      used_kb=$((total_kb - available_kb))
    fi
    
    local total_gb=$(kb_to_gb "$total_kb")
    local used_gb=$(kb_to_gb "$used_kb")
    local available_gb=$(kb_to_gb "$available_kb")
    
    local usage_pct=$(echo "scale=2; ($used_kb * 100) / $total_kb" | bc)
    
    echo "$usage_pct|${total_gb}GB|${used_gb}GB|${available_gb}GB"
    
  else
    exit_on_error "free command not available on Linux"
  fi
}

get_memory_usage_mac() {
  # For macOS, use a combination of sysctl and vm_stat
  if command -v vm_stat &> /dev/null && command -v sysctl &> /dev/null; then
    # Get total memory in bytes
    local total_bytes=$(sysctl -n hw.memsize)
    local total_gb=$(bytes_to_gb "$total_bytes")
    
    # Get page size
    local page_size=$(vm_stat | head -1 | awk '{print $8}')
    
    # Get memory pages
    local vm_output=$(vm_stat)
    local pages_free=$(echo "$vm_output" | grep "Pages free:" | awk '{print $3}' | sed 's/\.//')
    local pages_active=$(echo "$vm_output" | grep "Pages active:" | awk '{print $3}' | sed 's/\.//')
    local pages_inactive=$(echo "$vm_output" | grep "Pages inactive:" | awk '{print $3}' | sed 's/\.//')
    local pages_speculative=$(echo "$vm_output" | grep "Pages speculative:" | awk '{print $3}' | sed 's/\.//')
    local pages_wired=$(echo "$vm_output" | grep "Pages wired down:" | awk '{print $4}' | sed 's/\.//')
    local pages_purgeable=$(echo "$vm_output" | grep "Pages purgeable:" | awk '{print $3}' | sed 's/\.//')
    local pages_compressed=$(echo "$vm_output" | grep "Pages stored in compressor:" | awk '{print $5}' | sed 's/\.//')
    
    # Calculate memory in bytes
    local free_bytes=$((pages_free * page_size))
    local active_bytes=$((pages_active * page_size))
    local inactive_bytes=$((pages_inactive * page_size))
    local speculative_bytes=$((pages_speculative * page_size))
    local wired_bytes=$((pages_wired * page_size))
    local purgeable_bytes=$((pages_purgeable * page_size))
    local compressed_bytes=$((pages_compressed * page_size))
    
    # On macOS memory calculation (all categories should add up to total):
    # Used = active + wired (actual physical memory in use)
    # Available = free + inactive + speculative + purgeable (memory that can be reclaimed)
    local used_bytes=$((active_bytes + wired_bytes))
    local available_bytes=$((free_bytes + inactive_bytes + speculative_bytes + purgeable_bytes))
    
    local used_gb=$(bytes_to_gb "$used_bytes")
    local available_gb=$(bytes_to_gb "$available_bytes")
    
    local usage_pct=$(echo "scale=2; ($used_bytes * 100) / $total_bytes" | bc)
    
    echo "$usage_pct|${total_gb}GB|${used_gb}GB|${available_gb}GB"
    
  else
    exit_on_error "vm_stat or sysctl commands not available on macOS"
  fi
}

get_memory_usage() {
  local target_os="$1"
  
  case "$target_os" in
    linux)
      get_memory_usage_linux
      ;;
    mac|macos)
      get_memory_usage_mac
      ;;
    *)
      exit_on_error "Unsupported OS: $target_os"
      ;;
  esac
}

display_memory_info() {
  local memory_data="$1"
  local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  
  IFS='|' read -r usage_pct total_mem used_mem available_mem <<< "$memory_data"
  
  # Color coding based on memory usage
  local color=""
  local status=""
  if (( $(echo "$usage_pct >= 85" | bc -l) )); then
    color="\033[1;31m" # Red
    status="CRITICAL"
  elif (( $(echo "$usage_pct >= 70" | bc -l) )); then
    color="\033[1;33m" # Yellow
    status="HIGH"
  else
    color="\033[1;32m" # Green
    status="NORMAL"
  fi
  
  echo -e "[$timestamp] Memory Usage: ${color}${usage_pct}%\033[0m ($status) | Total: $total_mem | Used: $used_mem | Available: $available_mem"
}

main() {
  local continuous=false
  local interval=5
  local target_os=""
  
  # If no arguments provided, auto-detect OS
  if [[ $# -eq 0 ]]; then
    target_os="auto"
  else
    # First argument should be the OS or option
    case "$1" in
      -h|--help)
        usage
        ;;
      linux|mac|macos|auto)
        target_os="$1"
        shift
        ;;
      -c|--continuous|-i|--interval)
        # If first argument is an option, auto-detect OS
        target_os="auto"
        ;;
      *)
        exit_on_error "Invalid OS argument: $1. Valid options: linux, mac, macos, auto. Use -h for help."
        ;;
    esac
  fi
  
  # Handle auto-detection
  if [[ "$target_os" == "auto" ]]; then
    target_os=$(detect_os)
    log_info "Auto-detected OS: $target_os"
  fi
  
  # Parse remaining command line arguments
  while [[ $# -gt 0 ]]; do
    case $1 in
      -c|--continuous)
        continuous=true
        shift
        ;;
      -i|--interval)
        if [[ -n "${2:-}" && "$2" =~ ^[0-9]+$ ]]; then
          interval="$2"
          shift 2
        else
          exit_on_error "Invalid interval value. Please provide a positive integer."
        fi
        ;;
      -h|--help)
        usage
        ;;
      *)
        exit_on_error "Unknown option: $1. Use -h for help."
        ;;
    esac
  done
  
  # Check if bc is available for calculations
  if ! command -v bc &> /dev/null; then
    exit_on_error "bc command is required for calculations but not found."
  fi
  
  log_info "Starting memory usage monitoring for $target_os..."
  
  if [[ "$continuous" == true ]]; then
    log_info "Running in continuous mode (interval: ${interval}s). Press Ctrl+C to stop."
    echo "========================================"
    
    # Set up trap for graceful exit
    trap 'echo; log_info "Monitoring stopped."; exit 0' INT
    
    while true; do
      memory_data=$(get_memory_usage "$target_os")
      display_memory_info "$memory_data"
      sleep "$interval"
    done
  else
    # Single check mode
    memory_data=$(get_memory_usage "$target_os")
    display_memory_info "$memory_data"
  fi
}

main "$@"