#!/usr/bin/env bash
set -euo pipefail
trap 'log_error "<PERSON><PERSON><PERSON> failed at line $LINENO"; exit 1' ERR

log_info()  { echo -e "\033[1;32m[INFO]\033[0m $*"; }
log_warn()  { echo -e "\033[1;33m[WARN]\033[0m $*"; }
log_error() { echo -e "\033[1;31m[ERROR]\033[0m $*"; }
exit_on_error() { log_error "$1"; exit 1; }

usage() {
  cat <<EOF

Usage: check_disk_usage.sh [OS] [OPTIONS]

Description:
  This script checks disk space usage on Linux and macOS systems for all mounted filesystems.
  If no OS is specified, it will auto-detect your operating system.

Arguments:
  OS                   Target operating system (linux, mac, macos, auto) - OPTIONAL
                       If not provided, OS will be auto-detected

Options:
  -c, --continuous      Run continuously with 5-second intervals (Ctrl+C to stop)
  -i, --interval <sec>  Set custom interval in seconds for continuous mode (default: 5)
  -a, --all            Show all filesystems including system/virtual ones
  -h, --help           Show this help message and exit

Examples:
  # Auto-detect OS and check disk usage (simplest)
  ./check_disk_usage.sh

  # Check disk usage once on macOS
  ./check_disk_usage.sh mac

  # Check disk usage once on Linux
  ./check_disk_usage.sh linux

  # Auto-detect OS and check disk usage
  ./check_disk_usage.sh auto

  # Auto-detect OS and show all filesystems
  ./check_disk_usage.sh --all

  # Show all filesystems including system ones
  ./check_disk_usage.sh mac --all

  # Check disk usage continuously on macOS every 10 seconds
  ./check_disk_usage.sh mac --continuous --interval 10

EOF
  exit 1
}

detect_os() {
  local detected_os
  case "$(uname -s)" in
    Linux*)     detected_os="linux" ;;
    Darwin*)    detected_os="mac" ;;
    *)          exit_on_error "Unsupported operating system: $(uname -s)" ;;
  esac
  echo "$detected_os"
}

get_disk_usage_linux() {
  local show_all="$1"
  
  if command -v df &> /dev/null; then
    # Use df command with human-readable output
    local df_output
    if [[ "$show_all" == "true" ]]; then
      df_output=$(df -h)
    else
      # Filter out common system/virtual filesystems
      df_output=$(df -h | grep -E '^/dev/|^/System/|^/usr/|^tmpfs' | grep -v -E 'tmpfs|devtmpfs|sysfs|proc|udev|run')
    fi
    
    echo "$df_output"
  else
    exit_on_error "df command not available on Linux"
  fi
}

get_disk_usage_mac() {
  local show_all="$1"
  
  if command -v df &> /dev/null; then
    # Use df command with human-readable output
    local df_output
    if [[ "$show_all" == "true" ]]; then
      df_output=$(df -h)
    else
      # Filter out common macOS system filesystems
      df_output=$(df -h | grep -E '^/dev/disk' | grep -v -E 'devfs|map')
    fi
    
    echo "$df_output"
  else
    exit_on_error "df command not available on macOS"
  fi
}

get_disk_usage() {
  local target_os="$1"
  local show_all="$2"
  
  case "$target_os" in
    linux)
      get_disk_usage_linux "$show_all"
      ;;
    mac|macos)
      get_disk_usage_mac "$show_all"
      ;;
    *)
      exit_on_error "Unsupported OS: $target_os"
      ;;
  esac
}

parse_usage_percentage() {
  local usage_str="$1"
  # Remove % sign and return just the number
  echo "${usage_str%\%}"
}

convert_to_bytes() {
  local size_str="$1"
  local size_num=$(echo "$size_str" | sed 's/[A-Za-z]*$//')
  local size_unit=$(echo "$size_str" | sed 's/^[0-9.]*//') 
  
  case "$size_unit" in
    "Ki"|"K") echo "scale=0; $size_num * 1024" | bc ;;
    "Mi"|"M") echo "scale=0; $size_num * 1024 * 1024" | bc ;;
    "Gi"|"G") echo "scale=0; $size_num * 1024 * 1024 * 1024" | bc ;;
    "Ti"|"T") echo "scale=0; $size_num * 1024 * 1024 * 1024 * 1024" | bc ;;
    *) echo "scale=0; $size_num" | bc ;;
  esac
}

bytes_to_human() {
  local bytes="$1"
  local units=('B' 'KB' 'MB' 'GB' 'TB')
  local unit=0
  local size="$bytes"
  
  while (( $(echo "$size >= 1024" | bc -l) )) && (( unit < 4 )); do
    size=$(echo "scale=2; $size / 1024" | bc)
    ((unit++))
  done
  
  printf "%.1f%s" "$size" "${units[$unit]}"
}

calculate_totals() {
  local disk_data="$1"
  local total_size_bytes=0
  local total_used_bytes=0
  local total_avail_bytes=0
  
  # Process each line (skip the header from df output)
  local first_line=true
  while IFS= read -r line; do
    if [[ "$first_line" == "true" ]]; then
      first_line=false
      continue  # Skip df header line
    fi
    
    # Parse df output line
    local size=$(echo "$line" | awk '{print $2}')
    local used=$(echo "$line" | awk '{print $3}')
    local avail=$(echo "$line" | awk '{print $4}')
    
    # Convert to bytes and add to totals
    local size_bytes=$(convert_to_bytes "$size")
    local used_bytes=$(convert_to_bytes "$used")
    local avail_bytes=$(convert_to_bytes "$avail")
    
    total_size_bytes=$(echo "$total_size_bytes + $size_bytes" | bc)
    total_used_bytes=$(echo "$total_used_bytes + $used_bytes" | bc)
    total_avail_bytes=$(echo "$total_avail_bytes + $avail_bytes" | bc)
    
  done <<< "$disk_data"
  
  # Calculate overall usage percentage
  local usage_pct="0"
  if [[ "$total_size_bytes" != "0" ]]; then
    usage_pct=$(echo "scale=2; ($total_used_bytes * 100) / $total_size_bytes" | bc)
  fi
  
  # Convert back to human readable
  local total_size_human=$(bytes_to_human "$total_size_bytes")
  local total_used_human=$(bytes_to_human "$total_used_bytes")
  local total_avail_human=$(bytes_to_human "$total_avail_bytes")
  
  echo "$usage_pct|$total_size_human|$total_used_human|$total_avail_human"
}

display_disk_info() {
  local disk_data="$1"
  local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  
  # Calculate totals first
  local totals_data=$(calculate_totals "$disk_data")
  IFS='|' read -r total_usage_pct total_size total_used total_avail <<< "$totals_data"
  
  # Color coding for total usage
  local total_color=""
  local total_status=""
  local reset="\033[0m"
  if (( $(echo "$total_usage_pct >= 90" | bc -l) )); then
    total_color="\033[1;31m" # Red - Critical
    total_status="CRITICAL"
  elif (( $(echo "$total_usage_pct >= 80" | bc -l) )); then
    total_color="\033[1;33m" # Yellow - High
    total_status="HIGH"
  else
    total_color="\033[1;32m" # Green - Normal
    total_status="NORMAL"
  fi
  
  echo "========================================"
  echo "[$timestamp] Disk Usage Report"
  echo "========================================"
  echo -e "TOTAL DISK USAGE: ${total_color}${total_usage_pct}%${reset} ($total_status)"
  echo "Total Size: $total_size | Used: $total_used | Available: $total_avail"
  echo "========================================"
  echo ""
  
  # Print header
  printf "%-20s %-8s %-8s %-8s %-8s %-s\n" "Filesystem" "Size" "Used" "Avail" "Use%" "Mounted on"
  echo "----------------------------------------"
  
  # Process each line (skip the header from df output)
  local first_line=true
  while IFS= read -r line; do
    if [[ "$first_line" == "true" ]]; then
      first_line=false
      continue  # Skip df header line
    fi
    
    # Parse df output line
    local filesystem=$(echo "$line" | awk '{print $1}')
    local size=$(echo "$line" | awk '{print $2}')
    local used=$(echo "$line" | awk '{print $3}')
    local avail=$(echo "$line" | awk '{print $4}')
    local use_pct=$(echo "$line" | awk '{print $5}')
    local mounted_on=$(echo "$line" | awk '{print $6}')
    
    # Get numeric percentage for color coding
    local usage_num=$(parse_usage_percentage "$use_pct")
    
    # Color coding based on disk usage
    local color=""
    local reset="\033[0m"
    if [[ -n "$usage_num" ]] && (( usage_num >= 90 )); then
      color="\033[1;31m" # Red - Critical
    elif [[ -n "$usage_num" ]] && (( usage_num >= 80 )); then
      color="\033[1;33m" # Yellow - High  
    else
      color="\033[1;32m" # Green - Normal
    fi
    
    # Print formatted line with color
    printf "%-20s %-8s %-8s %-8s ${color}%-8s${reset} %-s\n" \
           "$filesystem" "$size" "$used" "$avail" "$use_pct" "$mounted_on"
           
  done <<< "$disk_data"
  
  echo "========================================"
}

display_simple_disk_info() {
  local disk_data="$1"
  local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  
  # Calculate totals first
  local totals_data=$(calculate_totals "$disk_data")
  IFS='|' read -r total_usage_pct total_size total_used total_avail <<< "$totals_data"
  
  # Color coding for total usage
  local total_color=""
  local total_status=""
  local reset="\033[0m"
  if (( $(echo "$total_usage_pct >= 90" | bc -l) )); then
    total_color="\033[1;31m" # Red - Critical
    total_status="CRITICAL"
  elif (( $(echo "$total_usage_pct >= 80" | bc -l) )); then
    total_color="\033[1;33m" # Yellow - High
    total_status="HIGH"
  else
    total_color="\033[1;32m" # Green - Normal  
    total_status="NORMAL"
  fi
  
  echo -e "[$timestamp] TOTAL DISK: ${total_color}${total_usage_pct}%${reset} ($total_status) | Size: $total_size | Used: $total_used | Avail: $total_avail"
  
  # Process each line (skip the header from df output)
  local first_line=true
  while IFS= read -r line; do
    if [[ "$first_line" == "true" ]]; then
      first_line=false
      continue  # Skip df header line
    fi
    
    # Parse df output line
    local filesystem=$(echo "$line" | awk '{print $1}')
    local size=$(echo "$line" | awk '{print $2}')
    local used=$(echo "$line" | awk '{print $3}')
    local avail=$(echo "$line" | awk '{print $4}')
    local use_pct=$(echo "$line" | awk '{print $5}')
    local mounted_on=$(echo "$line" | awk '{print $6}')
    
    # Get numeric percentage for color coding
    local usage_num=$(parse_usage_percentage "$use_pct")
    
    # Color coding and status
    local color=""
    local status=""
    local reset="\033[0m"
    if [[ -n "$usage_num" ]] && (( usage_num >= 90 )); then
      color="\033[1;31m" # Red
      status="CRITICAL"
    elif [[ -n "$usage_num" ]] && (( usage_num >= 80 )); then
      color="\033[1;33m" # Yellow
      status="HIGH"
    else
      color="\033[1;32m" # Green
      status="NORMAL"
    fi
    
    # Show simplified output
    echo -e "  ${mounted_on}: ${color}${use_pct}${reset} ($status) | Size: $size | Used: $used | Avail: $avail"
           
  done <<< "$disk_data"
  
  echo ""
}

main() {
  local continuous=false
  local interval=5
  local target_os=""
  local show_all=false
  
  # If no arguments provided, auto-detect OS
  if [[ $# -eq 0 ]]; then
    target_os="auto"
  else
    # First argument should be the OS or option
    case "$1" in
      -h|--help)
        usage
        ;;
      linux|mac|macos|auto)
        target_os="$1"
        shift
        ;;
      -c|--continuous|-i|--interval|-a|--all)
        # If first argument is an option, auto-detect OS
        target_os="auto"
        ;;
      *)
        exit_on_error "Invalid OS argument: $1. Valid options: linux, mac, macos, auto. Use -h for help."
        ;;
    esac
  fi
  
  # Handle auto-detection
  if [[ "$target_os" == "auto" ]]; then
    target_os=$(detect_os)
    log_info "Auto-detected OS: $target_os"
  fi
  
  # Parse remaining command line arguments
  while [[ $# -gt 0 ]]; do
    case $1 in
      -c|--continuous)
        continuous=true
        shift
        ;;
      -i|--interval)
        if [[ -n "${2:-}" && "$2" =~ ^[0-9]+$ ]]; then
          interval="$2"
          shift 2
        else
          exit_on_error "Invalid interval value. Please provide a positive integer."
        fi
        ;;
      -a|--all)
        show_all=true
        shift
        ;;
      -h|--help)
        usage
        ;;
      *)
        exit_on_error "Unknown option: $1. Use -h for help."
        ;;
    esac
  done
  
  log_info "Starting disk usage monitoring for $target_os..."
  
  if [[ "$continuous" == true ]]; then
    log_info "Running in continuous mode (interval: ${interval}s). Press Ctrl+C to stop."
    echo ""
    
    # Set up trap for graceful exit
    trap 'echo; log_info "Monitoring stopped."; exit 0' INT
    
    while true; do
      disk_data=$(get_disk_usage "$target_os" "$show_all")
      display_simple_disk_info "$disk_data"
      sleep "$interval"
    done
  else
    # Single check mode - show detailed table
    disk_data=$(get_disk_usage "$target_os" "$show_all")
    display_disk_info "$disk_data"
  fi
}

main "$@"