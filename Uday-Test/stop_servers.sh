#!/usr/bin/env bash
set -euo pipefail
trap 'log_error "<PERSON>ript failed at line $LINENO"; exit 1' ERR

# Logging functions
log_info()  { echo -e "\033[1;32m[INFO]\033[0m $*"; }
log_warn()  { echo -e "\033[1;33m[WARN]\033[0m $*"; }
log_error() { echo -e "\033[1;31m[ERROR]\033[0m $*"; }

# Function to stop Ollama server
stop_ollama() {
    log_info "Stopping Ollama server..."
    
    # Check if Ollama is running
    if pgrep -x "ollama" > /dev/null; then
        log_info "Found Ollama process, stopping..."
        pkill ollama || true
        sleep 2
        
        # Force kill if still running
        if pgrep -x "ollama" > /dev/null; then
            log_warn "Force killing Ollama process..."
            pkill -9 ollama || true
        fi
        
        log_info "Ollama server stopped"
    else
        log_info "Ollama server is not running"
    fi
    
    # Also try stopping via systemctl if available
    if command -v systemctl > /dev/null && systemctl is-active --quiet ollama 2>/dev/null; then
        log_info "Stopping Ollama service via systemctl..."
        sudo systemctl stop ollama || log_warn "Failed to stop Ollama service"
    fi
}

# Function to stop Node.js servers
stop_nodejs() {
    log_info "Stopping Node.js servers..."
    
    # Find and stop Node.js processes
    if pgrep -f "node" > /dev/null; then
        log_info "Found Node.js processes, stopping..."
        
        # Get all node processes with details
        NODE_PIDS=$(pgrep -f "node" || true)
        
        if [ -n "$NODE_PIDS" ]; then
            for pid in $NODE_PIDS; do
                # Get process details
                if ps -p "$pid" > /dev/null 2>&1; then
                    PROCESS_INFO=$(ps -p "$pid" -o pid,command --no-headers 2>/dev/null || echo "Unknown process")
                    log_info "Stopping: $PROCESS_INFO"
                    kill "$pid" 2>/dev/null || true
                fi
            done
            
            # Wait a moment for graceful shutdown
            sleep 3
            
            # Force kill any remaining node processes
            NODE_PIDS=$(pgrep -f "node" || true)
            if [ -n "$NODE_PIDS" ]; then
                log_warn "Force killing remaining Node.js processes..."
                for pid in $NODE_PIDS; do
                    kill -9 "$pid" 2>/dev/null || true
                done
            fi
        fi
        
        log_info "Node.js servers stopped"
    else
        log_info "No Node.js servers are running"
    fi
}

# Function to stop specific common Node.js applications
stop_common_node_apps() {
    log_info "Checking for common Node.js applications..."
    
    # Common Node.js application patterns
    local apps=("npm" "yarn" "next" "react-scripts" "nodemon" "pm2")
    
    for app in "${apps[@]}"; do
        if pgrep -f "$app" > /dev/null; then
            log_info "Stopping $app processes..."
            pkill -f "$app" || true
        fi
    done
    
    # Stop PM2 processes if PM2 is installed
    if command -v pm2 > /dev/null; then
        log_info "Stopping PM2 processes..."
        pm2 stop all 2>/dev/null || true
        pm2 kill 2>/dev/null || true
    fi
}

# Main function
main() {
    log_info "Starting server shutdown process..."
    
    stop_ollama
    stop_nodejs
    stop_common_node_apps
    
    log_info "Server shutdown process completed"
    
    # Show remaining processes for verification
    log_info "Checking for any remaining Ollama or Node.js processes..."
    if pgrep -f "ollama\|node" > /dev/null; then
        log_warn "Some processes are still running:"
        ps aux | grep -E "(ollama|node)" | grep -v grep || true
    else
        log_info "All targeted servers have been stopped successfully"
    fi
}

# Run main function
main "$@"