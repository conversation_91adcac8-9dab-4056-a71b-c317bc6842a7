

## Mention the type of change (Select one or more)?
- [ ] Task
- [ ] Improvement
- [ ] Bug Fix
- [ ] Others

Heading: Heading of PR

[longer description explaining your change and reasoning]

- [Bullet list of specific changes made]
- [Mention affected scripts, folders, or tasks]
- [State if anything was added, refactored, fixed, removed]

Affected Areas:
- [x] data_collection
- [ ] sizing_engine
- [ ] dba_tasks
- [ ] cloud_sre_tasks
- [ ] product_sre_tasks
- [ ] migration_flows
- [ ] docs
- [ ] vendor_envs

Test Use Cases:
- [Describe what inputs were tested]
- [Mention edge cases or platform-specific testing]
- [Optional: input/output format verification]

Screenshots (if applicable):
- [Paste link to screenshot or drag image in GitHub]
