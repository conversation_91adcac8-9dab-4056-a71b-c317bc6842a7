# Operating System Files
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# IDE and Editor Files
# PyCharm
.idea/
*.iws
*.iml
*.ipr
__pycache__/
*.py[cod]
*$py.class

# VS Code
.vscode/
*.code-workspace
.history/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Atom
.atom/

# JetBrains IDEs (IntelliJ, WebStorm, etc.)
.idea/
*.iml
*.ipr
*.iws
out/

# Python
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
vendor_envs/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Poetry
poetry.lock

# Node.js (if any JavaScript tools are used)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Logs
*.log
logs/
log/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.bak
*.backup
*.swp
*.swo

# Configuration files with sensitive data
*.conf.local
*.conf.secret
*secret*
*password*
*credentials*
.env.local
.env.production
.env.staging

# Cloud provider credentials
.aws/
.azure/
gcloud/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
*.tfvars

# Ansible
*.retry

# Docker
.dockerignore
Dockerfile.local

# Database files
*.db
*.sqlite
*.sqlite3

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Output directories
output/
results/
reports/

# Cache directories
.cache/
cache/

# Local development files
local/
dev/
development/

# OS generated files
.fseventsd
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Network Trash Folder
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk
