#!/usr/bin/env python3
"""
TessellCS AI Agent - Streamlit Web Interface
User-friendly web interface for the AI agent
"""

import streamlit as st
import requests
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

# Page configuration
st.set_page_config(
    page_title="TessellCS AI Agent",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Constants
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000/api")

# Session state initialization
if "token" not in st.session_state:
    st.session_state.token = None
if "user_id" not in st.session_state:
    st.session_state.user_id = None
if "security_level" not in st.session_state:
    st.session_state.security_level = None
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []

def make_request(endpoint: str, method: str = "GET", data: Dict = None, auth_required: bool = True):
    """Make API request with error handling"""
    
    headers = {"Content-Type": "application/json"}
    
    if auth_required and st.session_state.token:
        headers["Authorization"] = f"Bearer {st.session_state.token}"
    
    url = f"{API_BASE_URL}{endpoint}"
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        else:
            st.error(f"Unsupported HTTP method: {method}")
            return None
        
        if response.status_code == 401:
            st.session_state.token = None
            st.error("Session expired. Please login again.")
            st.rerun()
        
        response.raise_for_status()
        return response.json()
        
    except requests.exceptions.RequestException as e:
        st.error(f"API request failed: {str(e)}")
        return None

def login_page():
    """Login page"""
    
    st.title("🤖 TessellCS AI Agent")
    st.subheader("Login to access the AI assistant")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        with st.form("login_form"):
            username = st.text_input("Username")
            password = st.text_input("Password", type="password")
            submit = st.form_submit_button("Login")
            
            if submit:
                if username and password:
                    response = make_request(
                        "/login",
                        method="POST",
                        data={"username": username, "password": password},
                        auth_required=False
                    )
                    
                    if response:
                        st.session_state.token = response["token"]
                        st.session_state.user_id = response["user_id"]
                        st.session_state.security_level = response["security_level"]
                        st.success("Login successful!")
                        st.rerun()
                else:
                    st.error("Please enter both username and password")
        
        # Demo credentials info
        with st.expander("Demo Credentials"):
            st.write("**Admin**: admin / admin123")
            st.write("**Standard User**: user / user123")
            st.write("**Restricted User**: guest / guest123")

def main_interface():
    """Main application interface"""
    
    # Header
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        st.title("🤖 TessellCS AI Agent")
    
    with col2:
        st.write(f"**User**: {st.session_state.user_id}")
        st.write(f"**Level**: {st.session_state.security_level}")
    
    with col3:
        if st.button("Logout"):
            st.session_state.token = None
            st.session_state.user_id = None
            st.session_state.security_level = None
            st.session_state.chat_history = []
            st.rerun()
    
    # Sidebar
    with st.sidebar:
        st.header("🛠️ Tools")
        
        # Quick actions
        st.subheader("Quick Actions")
        
        if st.button("📚 Initialize Knowledge Base"):
            response = make_request("/initialize", method="POST")
            if response:
                st.success(f"Knowledge base updated: {response['stats']}")
        
        if st.button("📜 View Scripts"):
            st.session_state.show_scripts = True
        
        if st.button("📋 Audit Log") and st.session_state.security_level == "admin":
            st.session_state.show_audit = True
        
        # Categories
        st.subheader("Script Categories")
        categories = [
            "database_patching", "migration", "mysql", "oracle", 
            "postgres", "backup", "ssl", "monitoring", "cloud_tasks"
        ]
        
        selected_category = st.selectbox("Filter by category:", ["All"] + categories)
        
        # Settings
        st.subheader("Settings")
        auto_execute = st.checkbox("Auto-execute safe scripts", value=False)
        show_debug = st.checkbox("Show debug info", value=False)
    
    # Main content area
    tab1, tab2, tab3 = st.tabs(["💬 Chat", "📜 Scripts", "📊 Dashboard"])
    
    with tab1:
        chat_interface(auto_execute, show_debug)
    
    with tab2:
        scripts_interface(selected_category)
    
    with tab3:
        dashboard_interface()

def chat_interface(auto_execute: bool, show_debug: bool):
    """Chat interface with the AI agent"""
    
    st.subheader("Chat with TessellCS AI Agent")
    
    # Chat history
    chat_container = st.container()
    
    with chat_container:
        for i, (role, message, metadata) in enumerate(st.session_state.chat_history):
            if role == "user":
                with st.chat_message("user"):
                    st.write(message)
                    if show_debug and metadata:
                        st.json(metadata)
            else:
                with st.chat_message("assistant", avatar="🤖"):
                    st.write(message)
                    
                    # Show suggested scripts
                    if metadata and "suggested_scripts" in metadata:
                        scripts = metadata["suggested_scripts"]
                        if scripts:
                            st.write("**Suggested Scripts:**")
                            for script in scripts[:3]:
                                col1, col2, col3 = st.columns([3, 1, 1])
                                with col1:
                                    st.write(f"📄 `{script['path']}`")
                                    st.caption(f"Risk: {script['risk_level']} | Category: {script['category']}")
                                with col2:
                                    if st.button("ℹ️", key=f"info_{i}_{script['path']}"):
                                        show_script_info(script['path'])
                                with col3:
                                    if st.button("▶️", key=f"run_{i}_{script['path']}"):
                                        execute_script_ui(script['path'])
                    
                    # Show execution results
                    if metadata and "execution_results" in metadata and metadata["execution_results"]:
                        result = metadata["execution_results"]
                        if result["success"]:
                            st.success("Script executed successfully!")
                        else:
                            st.error("Script execution failed!")
                        
                        with st.expander("Execution Details"):
                            st.text("Output:")
                            st.code(result.get("output", "No output"))
                            if result.get("errors"):
                                st.text("Errors:")
                                st.code(result["errors"])
                    
                    if show_debug and metadata:
                        with st.expander("Debug Info"):
                            st.json(metadata)
    
    # Chat input
    query = st.chat_input("Ask me about database operations, scripts, or get help...")
    
    if query:
        # Add user message to history
        st.session_state.chat_history.append(("user", query, None))
        
        # Make API request
        response = make_request(
            "/query",
            method="POST",
            data={"query": query, "execute_if_safe": auto_execute}
        )
        
        if response:
            # Add assistant response to history
            st.session_state.chat_history.append((
                "assistant",
                response["message"],
                {
                    "suggested_scripts": response["suggested_scripts"],
                    "execution_results": response.get("execution_results"),
                    "confidence": response["confidence"],
                    "requires_approval": response.get("requires_approval", False)
                }
            ))
        
        st.rerun()

def scripts_interface(selected_category: str):
    """Scripts management interface"""
    
    st.subheader("Available Scripts")
    
    # Get scripts
    endpoint = "/scripts"
    if selected_category != "All":
        endpoint += f"?category={selected_category}"
    
    response = make_request(endpoint)
    
    if response and "scripts" in response:
        scripts = response["scripts"]
        
        if not scripts:
            st.info("No scripts found for the selected category.")
            return
        
        # Display scripts in a table
        for script in scripts:
            with st.expander(f"📄 {script['name']} ({script.get('category', 'general')})"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.write(f"**Path**: `{script['path']}`")
                    st.write(f"**Risk Level**: {script.get('risk_level', 'unknown')}")
                    st.write(f"**Size**: {script.get('size', 0)} bytes")
                    if script.get('description'):
                        st.write(f"**Description**: {script['description']}")
                    
                    if script.get('warnings'):
                        st.warning("Warnings: " + ", ".join(script['warnings']))
                
                with col2:
                    if st.button("ℹ️ Info", key=f"info_{script['path']}"):
                        show_script_info(script['path'])
                    
                    if st.button("▶️ Execute", key=f"execute_{script['path']}"):
                        execute_script_ui(script['path'])

def dashboard_interface():
    """Dashboard with stats and monitoring"""
    
    st.subheader("Dashboard")
    
    # System health
    health_response = make_request("/health", auth_required=False)
    if health_response:
        st.success(f"System Status: {health_response['status'].title()}")
        st.info(f"Version: {health_response['version']}")
    
    # Audit log for admins
    if st.session_state.security_level == "admin":
        st.subheader("Audit Log")
        
        hours = st.selectbox("Show events from last:", [1, 6, 24, 72, 168], index=2)
        
        audit_response = make_request(f"/audit-log?hours={hours}")
        
        if audit_response and "audit_log" in audit_response:
            events = audit_response["audit_log"]
            
            if events:
                # Display events
                for event in events[-20:]:  # Show last 20 events
                    timestamp = datetime.fromisoformat(event['timestamp']).strftime("%Y-%m-%d %H:%M:%S")
                    
                    if event['success']:
                        st.success(f"✅ {timestamp} - {event['user_id']} - {event['action']}")
                    else:
                        st.error(f"❌ {timestamp} - {event['user_id']} - {event['action']}")
                    
                    if event.get('details'):
                        with st.expander("Details"):
                            st.json(event['details'])
            else:
                st.info("No audit events found.")
    
    # Usage stats (placeholder)
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Queries", len(st.session_state.chat_history))
    
    with col2:
        st.metric("Session Duration", "Active")
    
    with col3:
        st.metric("Security Level", st.session_state.security_level.title())

def show_script_info(script_path: str):
    """Show detailed script information"""
    
    response = make_request(f"/script/{script_path}/info")
    
    if response:
        st.subheader(f"Script Information: {script_path}")
        
        if "error" in response:
            st.error(response["error"])
            return
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**Risk Level**: {response.get('risk_level', 'unknown')}")
            st.write(f"**Size**: {response.get('size', 0)} bytes")
            st.write(f"**Category**: {response.get('category', 'general')}")
        
        with col2:
            if response.get('warnings'):
                st.warning("⚠️ Warnings:")
                for warning in response['warnings']:
                    st.write(f"- {warning}")
        
        if response.get('description'):
            st.subheader("Description")
            st.write(response['description'])
        
        if response.get('usage'):
            st.subheader("Usage")
            st.code(response['usage'])

def execute_script_ui(script_path: str):
    """UI for script execution"""
    
    st.subheader(f"Execute Script: {script_path}")
    
    # Get script info first
    info_response = make_request(f"/script/{script_path}/info")
    
    if info_response and "error" not in info_response:
        risk_level = info_response.get('risk_level', 'unknown')
        
        if risk_level in ['high', 'critical']:
            st.warning(f"⚠️ This script has {risk_level} risk level. Proceed with caution!")
        
        # Arguments input
        args_input = st.text_input("Script arguments (space-separated):", "")
        args = args_input.split() if args_input.strip() else []
        
        # Execute button
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("▶️ Execute", type="primary"):
                execute_script(script_path, args, force_approval=False)
        
        with col2:
            if risk_level in ['medium', 'high'] and st.button("⚡ Force Execute", type="secondary"):
                st.warning("Forcing execution with approval override...")
                execute_script(script_path, args, force_approval=True)

def execute_script(script_path: str, args: List[str], force_approval: bool = False):
    """Execute script via API"""
    
    response = make_request(
        "/execute-script",
        method="POST",
        data={
            "script_path": script_path,
            "args": args,
            "force_approval": force_approval
        }
    )
    
    if response:
        if response.get("requires_approval"):
            st.warning("Script requires approval due to high risk level.")
            if st.button("Approve and Execute"):
                execute_script(script_path, args, force_approval=True)
        else:
            if response.get("success"):
                st.success("Script executed successfully!")
                if response.get("output"):
                    st.text("Output:")
                    st.code(response["output"])
            else:
                st.error("Script execution failed!")
                if response.get("errors"):
                    st.text("Errors:")
                    st.code(response["errors"])

# Main application logic
def main():
    """Main application entry point"""
    
    if not st.session_state.token:
        login_page()
    else:
        main_interface()

if __name__ == "__main__":
    main()