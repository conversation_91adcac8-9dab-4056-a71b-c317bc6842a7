version: '3.8'

services:
  tessell-ai-agent:
    build: .
    ports:
      - "8000:8000"
    environment:
      - TESSELL_REPO_PATH=/app/repository
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - USE_DOCKER=false
      - LOG_LEVEL=INFO
      - HOST=0.0.0.0
      - PORT=8000
    volumes:
      - ../:/app/repository:ro  # Mount repository as read-only
      - ./chroma_db:/app/chroma_db  # Persistent storage for vector DB
      - ./logs:/app/logs  # Persistent logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - redis

  streamlit-ui:
    build: 
      context: .
      dockerfile: Dockerfile.streamlit
    ports:
      - "8501:8501"
    environment:
      - API_BASE_URL=http://tessell-ai-agent:8000/api
    depends_on:
      - tessell-ai-agent
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - tessell-ai-agent
      - streamlit-ui
    restart: unless-stopped

volumes:
  redis_data: