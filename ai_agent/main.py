#!/usr/bin/env python3
"""
TessellCS AI Agent - Main Application
FastAPI-based REST API and Streamlit interface
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from src.tessell_agent import TessellAgent, AgentResponse
from src.security import SecurityManager, SecurityLevel, InputSanitizer
from src.script_executor import RiskLevel

# Initialize components
app = FastAPI(
    title="TessellCS AI Agent",
    description="AI Assistant for TessellCS Operations",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

# Security setup
security = HTTPBearer()
security_manager = SecurityManager()
input_sanitizer = InputSanitizer()

# Initialize agent
repo_path = os.getenv("TESSELL_REPO_PATH", "/Users/<USER>/DEV/claude-test/tessellcs-ops")
anthropic_key = os.getenv("ANTHROPIC_API_KEY")
agent = TessellAgent(repo_path, anthropic_key)

# Pydantic models
class QueryRequest(BaseModel):
    query: str
    execute_if_safe: bool = False
    context: Optional[Dict[str, Any]] = None

class ExecuteScriptRequest(BaseModel):
    script_path: str
    args: Optional[List[str]] = None
    force_approval: bool = False

class LoginRequest(BaseModel):
    username: str
    password: str

class AgentResponseModel(BaseModel):
    message: str
    suggested_scripts: List[Dict[str, Any]]
    execution_results: Optional[Dict] = None
    requires_approval: bool = False
    confidence: float

# Authentication dependency
async def get_current_session(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Validate session token and return session info"""
    session = security_manager.validate_session(credentials.credentials)
    if not session:
        raise HTTPException(status_code=401, detail="Invalid or expired session")
    return session

# Routes
@app.post("/api/login")
async def login(request: LoginRequest):
    """User login endpoint"""
    # Simple authentication for demo - replace with real auth
    demo_users = {
        "admin": {"password": "admin123", "level": SecurityLevel.ADMIN},
        "user": {"password": "user123", "level": SecurityLevel.STANDARD},
        "guest": {"password": "guest123", "level": SecurityLevel.RESTRICTED}
    }
    
    user = demo_users.get(request.username)
    if not user or user["password"] != request.password:
        security_manager.log_security_event(
            "auth", request.username, "login_failed", "authentication",
            {"reason": "invalid_credentials"}, "medium", False
        )
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    token = security_manager.create_session(request.username, user["level"])
    
    security_manager.log_security_event(
        "auth", request.username, "login_success", "authentication",
        {"level": user["level"].value}, "low", True
    )
    
    return {
        "token": token,
        "user_id": request.username,
        "security_level": user["level"].value
    }

@app.post("/api/query", response_model=AgentResponseModel)
async def process_query(request: QueryRequest, session=Depends(get_current_session)):
    """Process user query"""
    
    # Validate input
    valid, reason = input_sanitizer.validate_query(request.query)
    if not valid:
        security_manager.log_security_event(
            "query", session.user_id, "invalid_query", "input_validation",
            {"query": request.query[:100], "reason": reason}, "medium", False
        )
        raise HTTPException(status_code=400, detail=f"Invalid query: {reason}")
    
    # Check rate limits
    if not security_manager.check_rate_limit(session):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    
    try:
        # Process query
        response = agent.process_query(request.query, request.execute_if_safe)
        
        # Log successful query
        security_manager.log_security_event(
            "query", session.user_id, "query_processed", "agent_interaction",
            {"query": request.query[:100], "confidence": response.confidence}, "low", True
        )
        
        return AgentResponseModel(
            message=response.message,
            suggested_scripts=response.suggested_scripts,
            execution_results=response.execution_results,
            requires_approval=response.requires_approval,
            confidence=response.confidence
        )
        
    except Exception as e:
        security_manager.log_security_event(
            "query", session.user_id, "query_error", "agent_interaction",
            {"query": request.query[:100], "error": str(e)}, "high", False
        )
        raise HTTPException(status_code=500, detail=f"Query processing failed: {str(e)}")

@app.post("/api/execute-script")
async def execute_script(request: ExecuteScriptRequest, session=Depends(get_current_session)):
    """Execute a script"""
    
    # Sanitize script path
    clean_path = input_sanitizer.sanitize_filename(request.script_path)
    
    # Get script info for risk assessment
    script_info = agent.executor.get_script_info(clean_path)
    if "error" in script_info:
        raise HTTPException(status_code=404, detail=script_info["error"])
    
    risk_level = script_info["risk_level"]
    
    # Validate execution permissions
    allowed, reason = security_manager.validate_script_execution(
        session, clean_path, risk_level
    )
    
    if not allowed:
        security_manager.log_security_event(
            "execution", session.user_id, "script_execution_denied", clean_path,
            {"reason": reason, "risk_level": risk_level}, "medium", False
        )
        raise HTTPException(status_code=403, detail=reason)
    
    # Check if approval is required
    if security_manager.require_approval(session, "script_execution", risk_level) and not request.force_approval:
        security_manager.log_security_event(
            "execution", session.user_id, "approval_required", clean_path,
            {"risk_level": risk_level}, "medium", True
        )
        return {
            "requires_approval": True,
            "message": f"Script execution requires approval due to {risk_level} risk level",
            "script_info": script_info
        }
    
    try:
        # Execute script
        result = agent.execute_script_with_approval(clean_path, request.args)
        
        security_manager.log_security_event(
            "execution", session.user_id, "script_executed", clean_path,
            {"success": result["success"], "exit_code": result["exit_code"]}, 
            risk_level, result["success"]
        )
        
        return result
        
    except Exception as e:
        security_manager.log_security_event(
            "execution", session.user_id, "script_execution_error", clean_path,
            {"error": str(e)}, "high", False
        )
        raise HTTPException(status_code=500, detail=f"Script execution failed: {str(e)}")

@app.get("/api/scripts")
async def list_scripts(category: Optional[str] = None, session=Depends(get_current_session)):
    """List available scripts"""
    
    try:
        if category:
            scripts = agent.indexer.get_scripts_by_category(category)
        else:
            # Get all scripts
            results = agent.indexer.search("", n_results=100)
            scripts = [
                {
                    "path": item["metadata"]["file_path"],
                    "name": item["metadata"]["file_name"],
                    "category": item["metadata"].get("category", "general")
                }
                for item in results
                if item["metadata"].get("file_type") == ".sh"
            ]
        
        # Add script info
        for script in scripts:
            info = agent.executor.get_script_info(script["path"])
            script.update(info)
        
        return {"scripts": scripts}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list scripts: {str(e)}")

@app.get("/api/script/{script_path:path}/info")
async def get_script_info(script_path: str, session=Depends(get_current_session)):
    """Get detailed information about a script"""
    
    clean_path = input_sanitizer.sanitize_filename(script_path)
    
    try:
        info = agent.get_script_documentation(clean_path)
        return info
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get script info: {str(e)}")

@app.get("/api/audit-log")
async def get_audit_log(hours: int = 24, session=Depends(get_current_session)):
    """Get audit log (admin only)"""
    
    if session.security_level != SecurityLevel.ADMIN:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    logs = security_manager.get_audit_log(hours=hours)
    return {"audit_log": logs}

@app.post("/api/initialize")
async def initialize_knowledge_base(force_reindex: bool = False, session=Depends(get_current_session)):
    """Initialize or update knowledge base"""
    
    if session.security_level not in [SecurityLevel.ADMIN, SecurityLevel.ELEVATED]:
        raise HTTPException(status_code=403, detail="Elevated access required")
    
    try:
        stats = agent.initialize_knowledge_base(force_reindex=force_reindex)
        
        security_manager.log_security_event(
            "admin", session.user_id, "knowledge_base_updated", "system",
            stats, "low", True
        )
        
        return {"message": "Knowledge base updated", "stats": stats}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Initialization failed: {str(e)}")

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": "1.0.0"}

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logging.info("Starting TessellCS AI Agent...")
    
    # Initialize knowledge base if empty
    try:
        # Check if knowledge base exists and has content
        results = agent.indexer.search("test", n_results=1)
        if not results:
            logging.info("Initializing knowledge base...")
            stats = agent.initialize_knowledge_base()
            logging.info(f"Knowledge base initialized: {stats}")
        else:
            logging.info("Knowledge base already exists")
    except Exception as e:
        logging.error(f"Failed to initialize knowledge base: {e}")

if __name__ == "__main__":
    # Run with uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )