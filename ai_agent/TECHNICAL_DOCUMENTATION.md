# TessellCS AI Agent - Technical Documentation

This document provides detailed descriptions of each Python file in the AI agent system, their purpose, key functions, and how they interact with each other.

## 📁 File Structure Overview

```
ai_agent/
├── src/
│   ├── knowledge_base.py      # Repository indexing and search
│   ├── script_executor.py     # Safe script execution engine
│   ├── tessell_agent.py       # Main AI agent logic
│   └── security.py            # Security and authentication
├── main.py                    # FastAPI REST API server
├── streamlit_app.py           # Web user interface
├── config.py                  # Configuration management
└── requirements.txt           # Python dependencies
```

---

## 🧠 src/knowledge_base.py

### Purpose
Manages the repository indexing, content search, and knowledge base creation using ChromaDB for vector embeddings.

### Key Classes

#### `RepositoryIndexer`
**Description**: Main class that indexes repository content and provides semantic search capabilities.

**Key Methods**:
- `__init__(repo_path, db_path)`: Initialize with repository path and ChromaDB storage location
- `index_repository(force_reindex=False)`: Scan and index all repository files
- `search(query, n_results=5, category=None)`: Search indexed content using semantic similarity
- `get_file_content(file_path)`: Retrieve full content of a specific file
- `get_scripts_by_category(category)`: Get all scripts in a specific category

**Key Features**:
- **File Categorization**: Automatically categorizes files (database_patching, migration, mysql, oracle, etc.)
- **Content Chunking**: Splits large files into manageable chunks with overlap
- **Metadata Extraction**: Extracts file metadata including git information
- **Smart Filtering**: Only indexes relevant files (scripts, docs, configs)
- **Incremental Updates**: Skips unchanged files during reindexing

**Dependencies**:
- `chromadb`: Vector database for embeddings
- `gitpython`: Git repository information
- `langchain`: Text splitting utilities
- `pygments`: Syntax highlighting support

**Usage Example**:
```python
indexer = RepositoryIndexer("/path/to/repo")
stats = indexer.index_repository()  # Index all files
results = indexer.search("Oracle patching scripts", n_results=5)
```

---

## ⚡ src/script_executor.py

### Purpose
Provides safe, controlled execution of repository scripts with comprehensive risk assessment and security validation.

### Key Classes

#### `RiskLevel` (Enum)
**Values**: `LOW`, `MEDIUM`, `HIGH`, `CRITICAL`
**Description**: Defines risk levels for script classification.

#### `ExecutionResult` (Dataclass)
**Fields**:
- `success`: Boolean indicating execution success
- `exit_code`: Script exit code
- `stdout/stderr`: Output and error streams
- `execution_time`: Duration of execution
- `risk_assessment`: Assessed risk level
- `warnings`: List of security warnings

#### `ScriptValidator`
**Description**: Validates scripts before execution by analyzing content for dangerous patterns.

**Key Methods**:
- `assess_risk(script_content)`: Analyze script and return risk level with warnings
- `validate_script_path(script_path, repo_path)`: Ensure script is within repository bounds

**Risk Assessment Logic**:
- **Critical**: Blocked patterns like `rm -rf /`, `format`, `DROP DATABASE`
- **High**: Dangerous operations like `sudo`, `delete from`, `systemctl stop`
- **Medium**: File operations like `rm`, `chmod`, `mount`
- **Low**: Safe operations like monitoring commands

#### `ScriptExecutor`
**Description**: Main execution engine with safety mechanisms.

**Key Methods**:
- `execute_script(script_path, args, force_approval)`: Main execution method
- `execute_in_docker(script_path, args)`: Execute in Docker container (sandboxed)
- `execute_native(script_path, args, timeout)`: Execute directly on host
- `approve_script(script_path)`: Add script to approved list
- `get_script_info(script_path)`: Get script information without executing

**Safety Features**:
- **Path Validation**: Ensures scripts are within repository bounds
- **Risk Assessment**: Automatic risk classification
- **Approval Workflow**: High-risk scripts require explicit approval
- **Docker Sandboxing**: Optional containerized execution
- **Timeout Protection**: Prevents runaway scripts
- **Approved Scripts Cache**: Maintains list of pre-approved scripts

**Usage Example**:
```python
executor = ScriptExecutor("/path/to/repo")
result = executor.execute_script("monitoring/check_db.sh")
if result.success:
    print(result.stdout)
```

---

## 🤖 src/tessell_agent.py

### Purpose
Main AI agent that processes natural language queries, provides intelligent responses, and coordinates script execution.

### Key Classes

#### `AgentResponse` (Dataclass)
**Fields**:
- `message`: AI-generated response text
- `suggested_scripts`: List of relevant scripts
- `execution_results`: Results if script was executed
- `requires_approval`: Whether operation needs approval
- `confidence`: Confidence score of the response

#### `TessellAgent`
**Description**: Main AI agent orchestrating all operations.

**Key Methods**:
- `__init__(repo_path, anthropic_api_key)`: Initialize agent with repository and optional Claude API
- `process_query(query, execute_if_safe)`: Main query processing pipeline
- `classify_intent(query)`: Determine user intent (execution, information, search, guidance)
- `extract_context(query)`: Extract database type, operation type, urgency, environment
- `search_relevant_content(query, context)`: Find relevant repository content
- `generate_response(query, context, content)`: Generate AI response using Claude or templates

**AI Integration**:
- **Claude API**: Uses Anthropic's Claude 3 Sonnet for intelligent responses
- **Template Fallback**: Provides responses even without API key
- **Context Awareness**: Understands database types, operations, environments
- **Intent Classification**: Recognizes different types of user requests

**Query Processing Pipeline**:
1. **Intent Classification**: Determine what user wants to do
2. **Context Extraction**: Extract technical context (DB type, operation, etc.)
3. **Content Search**: Find relevant scripts and documentation
4. **Response Generation**: Use Claude or templates to generate helpful response
5. **Script Suggestion**: Recommend appropriate scripts
6. **Auto-execution**: Execute safe scripts if requested

**Usage Example**:
```python
agent = TessellAgent("/path/to/repo", "your-claude-key")
response = agent.process_query("How do I check Oracle database health?")
print(response.message)
for script in response.suggested_scripts:
    print(f"Suggested: {script['path']}")
```

---

## 🔐 src/security.py

### Purpose
Comprehensive security framework providing authentication, authorization, audit logging, and input validation.

### Key Classes

#### `SecurityLevel` (Enum)
**Values**: `RESTRICTED`, `STANDARD`, `ELEVATED`, `ADMIN`
**Description**: Defines user permission levels.

#### `SecurityEvent` (Dataclass)
**Description**: Represents a security event for audit logging.

#### `UserSession` (Dataclass)
**Description**: Represents an active user session with permissions and rate limiting.

#### `SecurityManager`
**Description**: Main security orchestrator.

**Key Methods**:
- `create_session(user_id, security_level, permissions)`: Create authenticated session
- `validate_session(token)`: Validate JWT session token
- `validate_script_execution(session, script_path, risk_level)`: Check execution permissions
- `validate_file_access(session, file_path, access_type)`: Validate file access
- `log_security_event(...)`: Log security events for audit trail

**Security Features**:
- **JWT Authentication**: Secure session tokens
- **Role-based Access Control**: Different permission levels
- **Rate Limiting**: Prevent API abuse
- **Audit Logging**: Comprehensive security event logging
- **Operation Approval**: High-risk operations require approval

**Permission Matrix**:
- **Restricted**: Read-only, no script execution
- **Standard**: Execute low-risk scripts
- **Elevated**: Execute medium-risk scripts with approval
- **Admin**: Full access with audit logging

#### `InputSanitizer`
**Description**: Validates and sanitizes user inputs.

**Key Methods**:
- `sanitize_filename(filename)`: Clean potentially dangerous filenames
- `sanitize_command_args(args)`: Clean command line arguments
- `validate_query(query)`: Check queries for dangerous patterns

**Usage Example**:
```python
security = SecurityManager()
token = security.create_session("user123", SecurityLevel.STANDARD)
session = security.validate_session(token)
allowed, reason = security.validate_script_execution(session, "script.sh", "medium")
```

---

## 🌐 main.py

### Purpose
FastAPI-based REST API server that exposes the AI agent functionality through HTTP endpoints.

### Key Components

#### **FastAPI Application Setup**
- CORS middleware for cross-origin requests
- HTTP Bearer authentication
- Request/response models with Pydantic
- Error handling and validation

#### **Core Dependencies**
- `SecurityManager`: Handles authentication and authorization
- `TessellAgent`: Main AI agent instance
- `InputSanitizer`: Input validation and sanitization

#### **API Endpoints**

**Authentication**:
- `POST /api/login`: User authentication with demo credentials
  - Returns JWT token for session management

**Agent Interaction**:
- `POST /api/query`: Process natural language queries
  - Input: Query text, execution preferences, context
  - Output: AI response with suggested scripts
  - Security: Rate limiting, input validation

**Script Management**:
- `GET /api/scripts`: List available scripts with filtering
- `GET /api/script/{path}/info`: Get detailed script information
- `POST /api/execute-script`: Execute scripts with approval workflow

**Administration**:
- `POST /api/initialize`: Initialize/update knowledge base
- `GET /api/audit-log`: View security audit logs (admin only)
- `GET /api/health`: System health check

#### **Security Integration**
- JWT token validation on all protected endpoints
- Input sanitization for all user inputs
- Rate limiting per user session
- Comprehensive audit logging
- Role-based access control

#### **Error Handling**
- HTTP status codes for different error types
- Detailed error messages for debugging
- Security event logging for failed operations

**Usage Example**:
```bash
# Login
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# Query agent
curl -X POST http://localhost:8000/api/query \
  -H "Authorization: Bearer <token>" \
  -d '{"query": "Show me Oracle patching scripts"}'
```

---

## 🖥️ streamlit_app.py

### Purpose
User-friendly web interface built with Streamlit for interacting with the AI agent.

### Key Components

#### **Application Structure**
- **Login Page**: Secure authentication interface
- **Main Interface**: Chat, scripts management, and dashboard
- **Session Management**: Maintains user state and authentication

#### **User Interface Features**

**Chat Interface**:
- Real-time conversation with AI agent
- Display of suggested scripts with action buttons
- Execution results and debug information
- Chat history maintenance

**Scripts Management**:
- Browse available scripts by category
- View detailed script information
- Execute scripts with safety prompts
- Filter and search capabilities

**Dashboard**:
- System health monitoring
- Audit log viewing (admin users)
- Usage statistics and metrics
- Configuration status

#### **Security Integration**
- Session token management
- Role-based UI components
- Input validation and sanitization
- Secure API communication

#### **Interactive Features**
- **Auto-execution Toggle**: Execute safe scripts automatically
- **Debug Mode**: Show detailed API responses
- **Category Filtering**: Filter scripts by type
- **Approval Workflows**: Handle high-risk operations

**Key Functions**:
- `make_request()`: Secure API communication with error handling
- `chat_interface()`: Main chat functionality
- `scripts_interface()`: Script browsing and management
- `execute_script_ui()`: Script execution interface with safety prompts

**Usage**:
```bash
streamlit run streamlit_app.py
# Access at http://localhost:8501
```

---

## ⚙️ config.py

### Purpose
Centralized configuration management with support for file-based and environment variable configuration.

### Key Classes

#### `AgentConfig` (Dataclass)
**Description**: Complete configuration structure with defaults.

**Configuration Categories**:
- **Repository Settings**: Path to tessellcs-ops repository
- **API Settings**: Anthropic API key, model parameters
- **Security Settings**: JWT secrets, session management
- **Execution Settings**: Docker usage, timeouts, limits
- **Knowledge Base**: Chunking parameters, search limits
- **Server Settings**: Host, port, reload options

#### `ConfigManager`
**Description**: Loads, validates, and manages configuration.

**Key Methods**:
- `load_config()`: Load from file and environment variables
- `save_config()`: Save configuration to JSON file
- `validate_config()`: Validate configuration and return warnings/errors

**Configuration Priority** (highest to lowest):
1. Environment variables
2. Configuration file (`ai_agent/config.json`)
3. Default values

**Validation Features**:
- Repository path existence check
- API key format validation
- Docker availability check
- Network port validation
- Security parameter validation

**Environment Variables**:
- `TESSELL_REPO_PATH`: Repository location
- `ANTHROPIC_API_KEY`: Claude API key
- `USE_DOCKER`: Enable Docker sandboxing
- `LOG_LEVEL`: Logging verbosity
- `HOST`, `PORT`: Server binding

**Usage Example**:
```python
manager = ConfigManager()
config = manager.config
validation = manager.validate_config()
if validation['valid']:
    print("Configuration is valid")
```

---

## 📋 requirements.txt

### Purpose
Defines all Python package dependencies with specific versions for reproducible deployments.

### Key Dependencies

#### **Core Framework**
- `fastapi`: Modern, fast web framework for APIs
- `uvicorn`: ASGI server for FastAPI
- `streamlit`: Web app framework for data science

#### **AI and ML**
- `anthropic`: Official Anthropic Claude API client
- `langchain`: LLM application framework
- `langchain-anthropic`: Anthropic integration for LangChain
- `chromadb`: Vector database for embeddings

#### **Security and Validation**
- `pydantic`: Data validation using Python type hints
- `python-multipart`: Form data parsing
- `aiofiles`: Async file operations

#### **Utilities**
- `docker`: Docker API client
- `gitpython`: Git repository interaction
- `pygments`: Syntax highlighting
- `watchdog`: File system monitoring
- `python-dotenv`: Environment variable loading

#### **Version Management**
All packages are pinned to specific versions to ensure:
- Reproducible deployments
- Compatibility between packages
- Security through known-good versions

---

## 🔄 Component Interactions

### Data Flow Diagram
```
User Query → Streamlit UI → FastAPI → Security Manager → TessellAgent
                                                            ↓
Knowledge Base ← Script Executor ← Agent Logic ← Context Extraction
       ↓                ↓              ↓
   ChromaDB        Docker/Native    Claude API
```

### Security Flow
```
Login → JWT Token → Session Validation → Permission Check → Operation → Audit Log
```

### Query Processing Flow
```
Query → Intent Classification → Context Extraction → Content Search → Response Generation → Script Suggestion → Execution (if safe)
```

---

## 🛠️ Development Guidelines

### Adding New Features

1. **New Script Categories**: Update `knowledge_base.py` categorization logic
2. **Security Policies**: Modify risk patterns in `script_executor.py`
3. **API Endpoints**: Add routes in `main.py` with proper security
4. **UI Components**: Extend `streamlit_app.py` interfaces
5. **Configuration**: Add new settings to `config.py`

### Testing Components

```bash
# Test individual components
python src/knowledge_base.py      # Repository indexing
python src/script_executor.py    # Script execution
python src/security.py           # Security validation
python src/tessell_agent.py      # AI agent logic
python config.py validate        # Configuration validation
```

### Error Handling Patterns

Each component implements consistent error handling:
- **Graceful Degradation**: Fallback to basic functionality when advanced features fail
- **Detailed Logging**: Comprehensive error information for debugging
- **User-Friendly Messages**: Clear error messages for end users
- **Security Events**: Failed operations trigger security logging

### Performance Considerations

- **ChromaDB**: Vector database provides fast semantic search
- **Caching**: Repository indexing includes intelligent caching
- **Rate Limiting**: Prevents API abuse and resource exhaustion
- **Docker Sandboxing**: Optional for better security vs. performance trade-off

This technical documentation provides a comprehensive understanding of each component in the TessellCS AI Agent system.