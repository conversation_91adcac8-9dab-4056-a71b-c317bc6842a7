# TessellCS AI Agent

An intelligent AI assistant for the TessellCS operations repository that provides natural language querying of scripts and documentation with safe execution capabilities.

## Features

- 🤖 **Natural Language Interface**: Ask questions about database operations, scripts, and procedures
- 📚 **Knowledge Base**: Automatically indexes repository content for intelligent search
- 🔒 **Safe Script Execution**: Multi-level security with risk assessment and approval workflows
- 🎯 **Context-Aware Responses**: Understands database types, operations, and environments
- 📊 **Audit Logging**: Comprehensive security and activity logging
- 🌐 **Web Interface**: User-friendly Streamlit interface and REST API
- 🐳 **Containerized Deployment**: Docker support for easy deployment

## Architecture

The AI agent consists of several key components:

- **Knowledge Base Manager**: Indexes and searches repository content
- **Script Execution Engine**: Safely executes scripts with validation
- **Security Layer**: Authentication, authorization, and audit logging
- **Query Processing**: Intent recognition and response generation
- **Web Interface**: REST API and Streamlit UI

## Quick Start

### Prerequisites

- Python 3.11+
- Docker (optional but recommended)
- Anthropic API key (optional, for enhanced responses)

### Installation

1. **Clone and setup**:
   ```bash
   cd tessellcs-ops/ai_agent
   pip install -r requirements.txt
   ```

2. **Configuration**:
   ```bash
   # Create default configuration
   python config.py create
   
   # Set environment variables
   export TESSELL_REPO_PATH=/path/to/tessellcs-ops
   export ANTHROPIC_API_KEY=your_anthropic_key  # Optional
   ```

3. **Initialize knowledge base**:
   ```bash
   python -c "from src.tessell_agent import TessellAgent; agent = TessellAgent('.'); agent.initialize_knowledge_base()"
   ```

4. **Run the application**:
   ```bash
   # Start API server
   python main.py
   
   # Start Streamlit UI (in another terminal)
   streamlit run streamlit_app.py
   ```

### Docker Deployment

```bash
# Using Docker Compose (recommended)
docker-compose up -d

# Access the application
# API: http://localhost:8000
# Web UI: http://localhost:8501
```

## Usage

### Web Interface

1. Open http://localhost:8501 in your browser
2. Login with demo credentials:
   - **Admin**: admin / admin123
   - **Standard**: user / user123  
   - **Restricted**: guest / guest123
3. Start chatting with the AI agent!

### API Examples

```bash
# Login
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# Query the agent
curl -X POST http://localhost:8000/api/query \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"query": "How do I check Oracle database health?"}'

# List available scripts
curl -X GET http://localhost:8000/api/scripts \
  -H "Authorization: Bearer <token>"

# Execute a script
curl -X POST http://localhost:8000/api/execute-script \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"script_path": "Uday-Test/check_cpu_usage.sh"}'
```

## Security

The AI agent implements multiple security layers:

### Security Levels

- **Restricted**: Read-only access, no script execution
- **Standard**: Can execute low-risk scripts
- **Elevated**: Can execute medium-risk scripts with approval
- **Admin**: Full access with audit logging

### Risk Assessment

Scripts are automatically classified by risk level:

- **Low**: Safe scripts (monitoring, status checks)
- **Medium**: Moderate risk (configuration changes)
- **High**: High risk (database operations, system modifications)
- **Critical**: Blocked operations (destructive commands)

### Safety Mechanisms

- Input sanitization and validation
- Script path validation (repository bounds)
- Rate limiting per user
- Execution timeouts
- Docker sandboxing (optional)
- Comprehensive audit logging

## Configuration

Configuration is managed through `ai_agent/config.json` and environment variables:

```json
{
  "repo_path": "/path/to/tessellcs-ops",
  "anthropic_api_key": "sk-ant-...",
  "use_docker": true,
  "script_timeout": 300,
  "session_timeout": 3600,
  "log_level": "INFO"
}
```

### Environment Variables

- `TESSELL_REPO_PATH`: Path to tessellcs-ops repository
- `ANTHROPIC_API_KEY`: Anthropic API key for enhanced responses
- `USE_DOCKER`: Enable Docker sandboxing
- `JWT_SECRET`: JWT signing secret
- `LOG_LEVEL`: Logging level

## Example Queries

The AI agent can handle various types of queries:

### Information Queries
- "What Oracle patching scripts are available?"
- "How do I perform a MySQL migration?"
- "Show me SSL certificate generation procedures"

### Script Discovery
- "Find scripts for database backup"
- "What monitoring scripts do we have?"
- "Show me Oracle RMAN scripts"

### Execution Requests
- "Run the CPU usage check script"
- "Execute Oracle pre-patch validation"
- "Start MySQL replication monitoring"

### Guidance Requests
- "Help me troubleshoot database connectivity"
- "What's the process for applying Oracle patches?"
- "Guide me through certificate renewal"

## Development

### Project Structure

```
ai_agent/
├── src/
│   ├── knowledge_base.py      # Repository indexing and search
│   ├── script_executor.py     # Safe script execution
│   ├── tessell_agent.py       # Main agent logic
│   └── security.py            # Security and validation
├── main.py                    # FastAPI application
├── streamlit_app.py           # Web interface
├── config.py                  # Configuration management
├── requirements.txt           # Python dependencies
├── docker-compose.yml         # Docker deployment
└── README.md                  # This file
```

### Adding New Features

1. **New Script Categories**: Update categorization logic in `knowledge_base.py`
2. **Security Policies**: Modify risk patterns in `script_executor.py`
3. **UI Components**: Add new tabs/pages in `streamlit_app.py`
4. **API Endpoints**: Add routes in `main.py`

### Testing

```bash
# Run basic functionality test
python src/tessell_agent.py

# Test script execution
python src/script_executor.py

# Test security validation
python src/security.py
```

## Monitoring and Maintenance

### Audit Logs

Access audit logs through:
- Web interface (Admin dashboard)
- API endpoint: `/api/audit-log`
- Log files: `ai_agent/logs/security.log`

### Knowledge Base Updates

The knowledge base automatically detects changes but can be manually refreshed:

```bash
# Via API
curl -X POST http://localhost:8000/api/initialize?force_reindex=true

# Via Python
from src.tessell_agent import TessellAgent
agent = TessellAgent('.')
agent.initialize_knowledge_base(force_reindex=True)
```

### Health Monitoring

- Health endpoint: `/api/health`
- Docker health checks included
- Prometheus metrics (planned feature)

## Troubleshooting

### Common Issues

1. **Knowledge base empty**: Run initialization script
2. **Docker execution fails**: Check Docker daemon and permissions
3. **OpenAI API errors**: Verify API key and rate limits
4. **Permission errors**: Check repository read permissions
5. **Script not found**: Ensure script paths are relative to repository root

### Debugging

Enable debug mode:
```bash
export LOG_LEVEL=DEBUG
python main.py
```

Or use the web interface debug toggle.

## Contributing

1. Follow existing code patterns and security practices
2. Add tests for new functionality
3. Update documentation for API changes
4. Ensure security validation for new features

## License

This project is part of the TessellCS operations repository and follows the same licensing terms.

## Support

For issues and questions:
1. Check this README and troubleshooting section
2. Review audit logs for security issues
3. Enable debug mode for detailed logging
4. Contact the TessellCS team for repository-specific questions