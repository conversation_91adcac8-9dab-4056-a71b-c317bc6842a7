#!/usr/bin/env python3
"""
Configuration management for TessellCS AI Agent
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class AgentConfig:
    """Main configuration class"""
    
    # Repository settings
    repo_path: str = "/Users/<USER>/DEV/claude-test/tessellcs-ops"
    
    # API settings
    anthropic_api_key: Optional[str] = None
    max_tokens: int = 500
    temperature: float = 0.7
    
    # Security settings
    jwt_secret: Optional[str] = None
    session_timeout: int = 3600  # 1 hour
    max_sessions_per_user: int = 3
    
    # Execution settings
    use_docker: bool = True
    script_timeout: int = 300  # 5 minutes
    max_output_size: int = 10000  # characters
    
    # Knowledge base settings
    chunk_size: int = 1000
    chunk_overlap: int = 200
    max_search_results: int = 10
    
    # Logging settings
    log_level: str = "INFO"
    audit_retention_days: int = 90
    
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False

class ConfigManager:
    """Manages configuration loading and validation"""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or "ai_agent/config.json"
        self.config = self.load_config()
    
    def load_config(self) -> AgentConfig:
        """Load configuration from file and environment"""
        
        # Start with defaults
        config_dict = {}
        
        # Load from file if exists
        config_file = Path(self.config_path)
        if config_file.exists():
            try:
                with open(config_file) as f:
                    file_config = json.load(f)
                    config_dict.update(file_config)
            except Exception as e:
                print(f"Warning: Failed to load config file {self.config_path}: {e}")
        
        # Override with environment variables
        env_mapping = {
            "TESSELL_REPO_PATH": "repo_path",
            "ANTHROPIC_API_KEY": "anthropic_api_key",
            "JWT_SECRET": "jwt_secret",
            "USE_DOCKER": "use_docker",
            "SCRIPT_TIMEOUT": "script_timeout",
            "LOG_LEVEL": "log_level",
            "HOST": "host",
            "PORT": "port"
        }
        
        for env_var, config_key in env_mapping.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Convert types as needed
                if config_key in ["port", "script_timeout", "session_timeout", "max_sessions_per_user", 
                                 "chunk_size", "chunk_overlap", "max_search_results", "audit_retention_days"]:
                    try:
                        config_dict[config_key] = int(env_value)
                    except ValueError:
                        print(f"Warning: Invalid integer value for {env_var}: {env_value}")
                elif config_key in ["temperature"]:
                    try:
                        config_dict[config_key] = float(env_value)
                    except ValueError:
                        print(f"Warning: Invalid float value for {env_var}: {env_value}")
                elif config_key in ["use_docker", "reload"]:
                    config_dict[config_key] = env_value.lower() in ['true', '1', 'yes', 'on']
                else:
                    config_dict[config_key] = env_value
        
        return AgentConfig(**config_dict)
    
    def save_config(self, config: AgentConfig = None):
        """Save configuration to file"""
        if config is None:
            config = self.config
        
        config_file = Path(self.config_path)
        config_file.parent.mkdir(exist_ok=True)
        
        # Convert to dict, excluding None values
        config_dict = {}
        for key, value in config.__dict__.items():
            if value is not None:
                config_dict[key] = value
        
        try:
            with open(config_file, 'w') as f:
                json.dump(config_dict, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def validate_config(self) -> Dict[str, Any]:
        """Validate configuration and return validation results"""
        results = {"valid": True, "warnings": [], "errors": []}
        
        # Check repository path
        if not Path(self.config.repo_path).exists():
            results["errors"].append(f"Repository path does not exist: {self.config.repo_path}")
            results["valid"] = False
        
        # Check Anthropic API key if provided
        if self.config.anthropic_api_key and not self.config.anthropic_api_key.startswith('sk-ant-'):
            results["warnings"].append("Anthropic API key format appears invalid")
        
        # Check JWT secret
        if not self.config.jwt_secret:
            results["warnings"].append("No JWT secret configured - using auto-generated one")
            self.config.jwt_secret = os.urandom(32).hex()
        
        # Check Docker availability if enabled
        if self.config.use_docker:
            try:
                import docker
                client = docker.from_env()
                client.ping()
            except Exception:
                results["warnings"].append("Docker not available - falling back to native execution")
                self.config.use_docker = False
        
        # Validate numeric ranges
        if self.config.port < 1 or self.config.port > 65535:
            results["errors"].append(f"Invalid port number: {self.config.port}")
            results["valid"] = False
        
        if self.config.session_timeout < 300:  # 5 minutes minimum
            results["warnings"].append("Session timeout is very short (< 5 minutes)")
        
        return results

def create_default_config(config_path: str = "ai_agent/config.json"):
    """Create a default configuration file"""
    
    config = AgentConfig()
    manager = ConfigManager(config_path)
    manager.config = config
    manager.save_config()
    
    print(f"Default configuration created at: {config_path}")
    print("Please review and modify the configuration as needed.")

if __name__ == "__main__":
    # CLI for configuration management
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "create":
            config_path = sys.argv[2] if len(sys.argv) > 2 else "ai_agent/config.json"
            create_default_config(config_path)
        elif sys.argv[1] == "validate":
            config_path = sys.argv[2] if len(sys.argv) > 2 else "ai_agent/config.json"
            manager = ConfigManager(config_path)
            results = manager.validate_config()
            
            print(f"Configuration validation results:")
            print(f"Valid: {results['valid']}")
            
            if results['warnings']:
                print("Warnings:")
                for warning in results['warnings']:
                    print(f"  - {warning}")
            
            if results['errors']:
                print("Errors:")
                for error in results['errors']:
                    print(f"  - {error}")
        else:
            print("Usage: python config.py [create|validate] [config_path]")
    else:
        # Default behavior - load and display config
        manager = ConfigManager()
        print("Current configuration:")
        for key, value in manager.config.__dict__.items():
            if key == "jwt_secret" and value:
                print(f"  {key}: [REDACTED]")
            elif key == "anthropic_api_key" and value:
                print(f"  {key}: {value[:8]}...")
            else:
                print(f"  {key}: {value}")