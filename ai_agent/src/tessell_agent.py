#!/usr/bin/env python3
"""
TessellCS AI Agent - Main Agent Interface
Provides intelligent assistance for tessellcs-ops repository
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path
import anthropic
from knowledge_base import RepositoryIndexer
from script_executor import <PERSON>riptExecutor, RiskLevel

@dataclass
class AgentResponse:
    message: str
    suggested_scripts: List[Dict[str, Any]]
    execution_results: Optional[Dict] = None
    requires_approval: bool = False
    confidence: float = 0.0

class TessellAgent:
    """Main AI Agent for TessellCS Operations"""
    
    def __init__(self, repo_path: str, anthropic_api_key: Optional[str] = None):
        self.repo_path = Path(repo_path)
        self.indexer = RepositoryIndexer(str(repo_path))
        self.executor = ScriptExecutor(str(repo_path))
        
        # Initialize Anthropic Claude if key provided
        self.use_claude = False
        if anthropic_api_key:
            self.anthropic_client = anthropic.Anthropic(api_key=anthropic_api_key)
            self.use_claude = True
        
        # Agent personality and context
        self.system_prompt = """You are TessellCS AI Agent, an expert assistant for database operations and infrastructure management. 

You have access to a comprehensive repository of scripts and documentation for:
- Database patching (Oracle, MySQL, PostgreSQL, MSSQL)
- Database migrations and replication
- Cloud infrastructure tasks
- SSL certificate management
- Monitoring and metrics collection
- Backup and restore operations

Your capabilities:
1. Answer questions about database operations and best practices
2. Recommend appropriate scripts for specific tasks
3. Execute safe scripts with proper validation
4. Provide step-by-step guidance for complex operations

Always prioritize safety and ask for confirmation before executing high-risk operations.
When suggesting scripts, explain what they do and any prerequisites or risks.
"""
        
        # Intent classification patterns
        self.intent_patterns = {
            "script_execution": [
                "run", "execute", "start", "launch", "perform"
            ],
            "information_query": [
                "what", "how", "why", "when", "where", "explain", "describe", "show"
            ],
            "script_search": [
                "find", "search", "locate", "available", "scripts for"
            ],
            "guidance": [
                "help", "guide", "steps", "process", "procedure", "how to"
            ]
        }
    
    def classify_intent(self, query: str) -> str:
        """Classify user intent from query"""
        query_lower = query.lower()
        
        for intent, patterns in self.intent_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                return intent
        
        return "information_query"  # default
    
    def extract_context(self, query: str) -> Dict[str, Any]:
        """Extract context information from query"""
        context = {
            "database_type": None,
            "operation_type": None,
            "urgency": "normal",
            "environment": None
        }
        
        # Database type detection
        db_types = {
            "oracle": ["oracle", "ora", "rman"],
            "mysql": ["mysql", "mariadb"],
            "postgresql": ["postgres", "postgresql", "pg"],
            "mssql": ["mssql", "sqlserver", "sql server"]
        }
        
        query_lower = query.lower()
        for db, keywords in db_types.items():
            if any(keyword in query_lower for keyword in keywords):
                context["database_type"] = db
                break
        
        # Operation type detection
        operations = {
            "backup": ["backup", "dump", "export"],
            "restore": ["restore", "import", "recover"],
            "migration": ["migrate", "migration", "move", "transfer"],
            "patching": ["patch", "update", "upgrade"],
            "monitoring": ["monitor", "metrics", "check", "health"],
            "ssl": ["ssl", "tls", "certificate", "cert"],
            "replication": ["replication", "replica", "sync"]
        }
        
        for op, keywords in operations.items():
            if any(keyword in query_lower for keyword in keywords):
                context["operation_type"] = op
                break
        
        # Environment detection
        environments = ["prod", "production", "dev", "development", "test", "staging"]
        for env in environments:
            if env in query_lower:
                context["environment"] = env
                break
        
        # Urgency detection
        urgent_keywords = ["urgent", "emergency", "critical", "asap", "immediately"]
        if any(keyword in query_lower for keyword in urgent_keywords):
            context["urgency"] = "high"
        
        return context
    
    def search_relevant_content(self, query: str, context: Dict[str, Any]) -> List[Dict]:
        """Search for relevant scripts and documentation"""
        # Build search query with context
        search_terms = [query]
        
        if context.get("database_type"):
            search_terms.append(context["database_type"])
        
        if context.get("operation_type"):
            search_terms.append(context["operation_type"])
        
        search_query = " ".join(search_terms)
        
        # Search in knowledge base
        results = self.indexer.search(search_query, n_results=10)
        
        # Filter and rank results
        filtered_results = []
        for result in results:
            # Prioritize scripts over documentation for execution intents
            if result["metadata"].get("file_type") == ".sh":
                result["relevance_boost"] = 1.2
            elif result["metadata"].get("file_type") == ".md":
                result["relevance_boost"] = 1.0
            else:
                result["relevance_boost"] = 0.8
            
            # Boost based on category match
            if context.get("operation_type") == result["metadata"].get("category"):
                result["relevance_boost"] *= 1.5
            
            filtered_results.append(result)
        
        # Sort by boosted relevance
        filtered_results.sort(key=lambda x: (x.get("relevance_boost", 1.0) * (1 - (x.get("score", 0.5)))), reverse=True)
        
        return filtered_results[:5]
    
    def generate_response(self, query: str, context: Dict[str, Any], 
                         relevant_content: List[Dict]) -> str:
        """Generate response using Claude or template-based approach"""
        
        if self.use_claude:
            return self._generate_claude_response(query, context, relevant_content)
        else:
            return self._generate_template_response(query, context, relevant_content)
    
    def _generate_claude_response(self, query: str, context: Dict[str, Any], 
                                 relevant_content: List[Dict]) -> str:
        """Generate response using Anthropic Claude"""
        
        # Prepare context for Claude
        content_summary = "\n".join([
            f"File: {item['metadata']['file_path']}\nContent: {item['content'][:200]}...\n"
            for item in relevant_content[:3]
        ])
        
        user_message = f"""{query}

Context Information:
{json.dumps(context, indent=2)}

Relevant Repository Content:
{content_summary}

Please provide a helpful response addressing the user's query about the TessellCS operations repository. Focus on practical guidance and suggest appropriate scripts when relevant."""
        
        try:
            response = self.anthropic_client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=500,
                temperature=0.7,
                system=self.system_prompt,
                messages=[
                    {"role": "user", "content": user_message}
                ]
            )
            return response.content[0].text
        except Exception as e:
            logging.error(f"Claude API error: {e}")
            return self._generate_template_response(query, context, relevant_content)
    
    def _generate_template_response(self, query: str, context: Dict[str, Any], 
                                   relevant_content: List[Dict]) -> str:
        """Generate response using template-based approach"""
        
        response_parts = []
        
        # Add contextual greeting
        if context.get("urgency") == "high":
            response_parts.append("I understand this is urgent. Let me help you quickly.")
        else:
            response_parts.append("I can help you with that.")
        
        # Add specific guidance based on context
        if context.get("database_type") and context.get("operation_type"):
            response_parts.append(f"For {context['database_type']} {context['operation_type']} operations, here's what I found:")
        if relevant_content:
            response_parts.append("\nRelevant resources:")
            for i, item in enumerate(relevant_content[:3], 1):
                file_path = item['metadata']['file_path']
                file_type = item['metadata'].get('file_type', '')
                category = item['metadata'].get('category', 'general')
                
                if file_type == '.sh':
                    response_parts.append(f"{i}. Script: {file_path} (Category: {category})")
                elif file_type == '.md':
                    response_parts.append(f"{i}. Documentation: {file_path}")
                else:
                    response_parts.append(f"{i}. File: {file_path}")
        
        # Add safety note for high-risk operations
        high_risk_operations = ["patching", "migration", "restore"]
        if context.get("operation_type") in high_risk_operations:
            response_parts.append("\n⚠️  Important: This is a high-risk operation. Please ensure you have proper backups and test in non-production first.")
        
        return "\n".join(response_parts)
    
    def suggest_scripts(self, query: str, context: Dict[str, Any], 
                       relevant_content: List[Dict]) -> List[Dict[str, Any]]:
        """Suggest appropriate scripts based on query and context"""
        
        suggested = []
        
        for item in relevant_content:
            if item['metadata'].get('file_type') == '.sh':
                script_path = item['metadata']['file_path']
                script_info = self.executor.get_script_info(script_path)
                
                if 'error' not in script_info:
                    suggested.append({
                        "path": script_path,
                        "description": item['content'][:200] + "...",
                        "risk_level": script_info['risk_level'],
                        "approved": script_info['approved'],
                        "category": item['metadata'].get('category', 'general')
                    })
        
        return suggested[:3]  # Top 3 suggestions
    
    def process_query(self, query: str, execute_if_safe: bool = False) -> AgentResponse:
        """Main query processing method"""
        
        # Classify intent and extract context
        intent = self.classify_intent(query)
        context = self.extract_context(query)
        
        # Search for relevant content
        relevant_content = self.search_relevant_content(query, context)
        
        # Generate response
        message = self.generate_response(query, context, relevant_content)
        
        # Suggest scripts
        suggested_scripts = self.suggest_scripts(query, context, relevant_content)
        
        # Auto-execute if requested and safe
        execution_results = None
        requires_approval = False
        
        if execute_if_safe and intent == "script_execution" and suggested_scripts:
            best_script = suggested_scripts[0]
            if best_script["risk_level"] == "low":
                result = self.executor.execute_script(best_script["path"])
                execution_results = {
                    "script": best_script["path"],
                    "success": result.success,
                    "output": result.stdout,
                    "errors": result.stderr
                }
            elif best_script["risk_level"] in ["medium", "high"]:
                requires_approval = True
        
        return AgentResponse(
            message=message,
            suggested_scripts=suggested_scripts,
            execution_results=execution_results,
            requires_approval=requires_approval,
            confidence=0.8 if relevant_content else 0.3
        )
    
    def execute_script_with_approval(self, script_path: str, args: List[str] = None) -> Dict:
        """Execute script after explicit approval"""
        result = self.executor.execute_script(script_path, args, force_approval=True)
        
        return {
            "script": script_path,
            "success": result.success,
            "exit_code": result.exit_code,
            "output": result.stdout,
            "errors": result.stderr,
            "execution_time": result.execution_time,
            "warnings": result.warnings
        }
    
    def get_script_documentation(self, script_path: str) -> Dict[str, Any]:
        """Get documentation and usage info for a script"""
        
        # Get script info
        script_info = self.executor.get_script_info(script_path)
        if 'error' in script_info:
            return script_info
        
        # Get full content
        content = self.indexer.get_file_content(script_path)
        if not content:
            return {"error": "Cannot read script content"}
        
        # Extract documentation from comments
        lines = content.split('\n')
        doc_lines = []
        usage_lines = []
        
        in_doc_block = False
        for line in lines[:50]:  # Check first 50 lines
            line = line.strip()
            if line.startswith('#'):
                doc_line = line[1:].strip()
                if 'usage:' in doc_line.lower() or 'example:' in doc_line.lower():
                    in_doc_block = True
                
                if in_doc_block:
                    usage_lines.append(doc_line)
                else:
                    doc_lines.append(doc_line)
        
        return {
            "path": script_path,
            "description": '\n'.join(doc_lines[:5]),  # First 5 comment lines
            "usage": '\n'.join(usage_lines),
            "risk_level": script_info['risk_level'],
            "warnings": script_info['warnings'],
            "size": script_info['size'],
            "category": script_info.get('category', 'general')
        }
    
    def initialize_knowledge_base(self, force_reindex: bool = False):
        """Initialize or update the knowledge base"""
        return self.indexer.index_repository(force_reindex=force_reindex)

if __name__ == "__main__":
    # Example usage
    agent = TessellAgent("/Users/<USER>/DEV/claude-test/tessellcs-ops")
    
    # Initialize knowledge base
    print("Initializing knowledge base...")
    stats = agent.initialize_knowledge_base()
    print(f"Indexing stats: {stats}")
    
    # Test queries
    test_queries = [
        "How do I check CPU usage?",
        "Show me Oracle patching scripts",
        "I need to migrate a MySQL database",
        "What SSL certificate scripts are available?"
    ]
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        response = agent.process_query(query)
        print(f"Response: {response.message}")
        if response.suggested_scripts:
            print("Suggested scripts:")
            for script in response.suggested_scripts:
                print(f"  - {script['path']} (Risk: {script['risk_level']})")