#!/usr/bin/env python3
"""
Security and Validation Framework for TessellCS AI Agent
Implements comprehensive security controls and audit logging
"""

import os
import json
import hashlib
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import jwt
import bcrypt

class SecurityLevel(Enum):
    RESTRICTED = "restricted"
    STANDARD = "standard"
    ELEVATED = "elevated"
    ADMIN = "admin"

@dataclass
class SecurityEvent:
    timestamp: str
    event_type: str
    user_id: str
    action: str
    resource: str
    risk_level: str
    success: bool
    details: Dict[str, Any]
    ip_address: Optional[str] = None

@dataclass
class UserSession:
    user_id: str
    session_id: str
    security_level: SecurityLevel
    created_at: datetime
    last_activity: datetime
    permissions: Set[str]
    rate_limit_tokens: int = 100

class SecurityManager:
    """Manages security policies, authentication, and authorization"""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or "ai_agent/security_config.json"
        self.config = self.load_security_config()
        self.active_sessions: Dict[str, UserSession] = {}
        self.audit_log: List[SecurityEvent] = []
        self.setup_logging()
        
        # Rate limiting
        self.rate_limits = {
            SecurityLevel.RESTRICTED: {"requests": 10, "window": 300},  # 10 req/5min
            SecurityLevel.STANDARD: {"requests": 50, "window": 300},    # 50 req/5min
            SecurityLevel.ELEVATED: {"requests": 200, "window": 300},   # 200 req/5min
            SecurityLevel.ADMIN: {"requests": 1000, "window": 300}      # 1000 req/5min
        }
        
        # Blocked operations by security level
        self.blocked_operations = {
            SecurityLevel.RESTRICTED: {
                "script_execution", "file_modification", "system_commands"
            },
            SecurityLevel.STANDARD: {
                "high_risk_scripts", "system_modification"
            },
            SecurityLevel.ELEVATED: {
                "critical_operations"
            },
            SecurityLevel.ADMIN: set()  # Admin can do everything
        }
    
    def load_security_config(self) -> Dict[str, Any]:
        """Load security configuration"""
        default_config = {
            "jwt_secret": os.urandom(32).hex(),
            "session_timeout": 3600,  # 1 hour
            "max_sessions_per_user": 3,
            "audit_retention_days": 90,
            "require_approval_for": ["high", "critical"],
            "blocked_patterns": [
                "rm -rf /",
                "format c:",
                "DROP DATABASE",
                "TRUNCATE TABLE",
                "DELETE FROM.*WHERE.*1=1"
            ],
            "allowed_scripts": [],
            "restricted_paths": [
                "/etc/passwd",
                "/etc/shadow",
                "/root",
                "/boot"
            ]
        }
        
        config_file = Path(self.config_path)
        if config_file.exists():
            try:
                with open(config_file) as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logging.error(f"Error loading security config: {e}")
        else:
            # Create default config file
            config_file.parent.mkdir(exist_ok=True)
            with open(config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
        
        return default_config
    
    def setup_logging(self):
        """Setup security audit logging"""
        log_file = Path("ai_agent/logs/security.log")
        log_file.parent.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("SecurityManager")
    
    def generate_session_token(self, user_id: str, security_level: SecurityLevel) -> str:
        """Generate JWT session token"""
        payload = {
            "user_id": user_id,
            "security_level": security_level.value,
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(seconds=self.config["session_timeout"])
        }
        
        return jwt.encode(payload, self.config["jwt_secret"], algorithm="HS256")
    
    def validate_session_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Validate JWT session token"""
        try:
            payload = jwt.decode(token, self.config["jwt_secret"], algorithms=["HS256"])
            return payload
        except jwt.ExpiredSignatureError:
            self.log_security_event("auth", "session_expired", "", "token_validation", "", "low", False)
            return None
        except jwt.InvalidTokenError:
            self.log_security_event("auth", "invalid_token", "", "token_validation", "", "medium", False)
            return None
    
    def create_session(self, user_id: str, security_level: SecurityLevel, 
                      permissions: Set[str] = None) -> str:
        """Create new user session"""
        
        # Check max sessions per user
        user_sessions = [s for s in self.active_sessions.values() if s.user_id == user_id]
        if len(user_sessions) >= self.config["max_sessions_per_user"]:
            # Remove oldest session
            oldest = min(user_sessions, key=lambda s: s.last_activity)
            del self.active_sessions[oldest.session_id]
        
        session_id = hashlib.sha256(f"{user_id}{time.time()}".encode()).hexdigest()
        
        session = UserSession(
            user_id=user_id,
            session_id=session_id,
            security_level=security_level,
            created_at=datetime.utcnow(),
            last_activity=datetime.utcnow(),
            permissions=permissions or set()
        )
        
        self.active_sessions[session_id] = session
        
        self.log_security_event("auth", user_id, "session_created", "authentication", 
                               session_id, "low", True)
        
        return self.generate_session_token(user_id, security_level)
    
    def validate_session(self, token: str) -> Optional[UserSession]:
        """Validate session and return session info"""
        payload = self.validate_session_token(token)
        if not payload:
            return None
        
        # Find active session
        user_sessions = [s for s in self.active_sessions.values() 
                        if s.user_id == payload["user_id"]]
        
        if not user_sessions:
            return None
        
        session = user_sessions[0]  # Get most recent
        
        # Check session timeout
        if datetime.utcnow() - session.last_activity > timedelta(seconds=self.config["session_timeout"]):
            del self.active_sessions[session.session_id]
            self.log_security_event("auth", session.user_id, "session_timeout", 
                                   "authentication", session.session_id, "low", False)
            return None
        
        # Update last activity
        session.last_activity = datetime.utcnow()
        
        return session
    
    def check_rate_limit(self, session: UserSession) -> bool:
        """Check if user is within rate limits"""
        limit_config = self.rate_limits[session.security_level]
        
        if session.rate_limit_tokens <= 0:
            # Check if window has passed
            window_start = session.last_activity - timedelta(seconds=limit_config["window"])
            if datetime.utcnow() > window_start:
                session.rate_limit_tokens = limit_config["requests"]
            else:
                return False
        
        session.rate_limit_tokens -= 1
        return True
    
    def is_operation_allowed(self, session: UserSession, operation: str) -> bool:
        """Check if operation is allowed for user's security level"""
        blocked = self.blocked_operations.get(session.security_level, set())
        return operation not in blocked
    
    def validate_script_execution(self, session: UserSession, script_path: str, 
                                 risk_level: str) -> Tuple[bool, str]:
        """Validate script execution request"""
        
        # Check basic permissions
        if not self.is_operation_allowed(session, "script_execution"):
            return False, "Script execution not allowed for your security level"
        
        # Check rate limits
        if not self.check_rate_limit(session):
            return False, "Rate limit exceeded"
        
        # Check risk level permissions
        if risk_level in ["high", "critical"] and session.security_level in [SecurityLevel.RESTRICTED, SecurityLevel.STANDARD]:
            return False, f"High-risk script execution requires elevated permissions"
        
        # Check if script is in allowed list
        if self.config["allowed_scripts"] and script_path not in self.config["allowed_scripts"]:
            if session.security_level != SecurityLevel.ADMIN:
                return False, "Script not in approved list"
        
        # Check for blocked patterns in script path
        for pattern in self.config.get("blocked_patterns", []):
            if pattern.lower() in script_path.lower():
                return False, f"Script contains blocked pattern: {pattern}"
        
        return True, "Approved"
    
    def validate_file_access(self, session: UserSession, file_path: str, 
                           access_type: str = "read") -> Tuple[bool, str]:
        """Validate file access request"""
        
        # Check restricted paths
        for restricted in self.config.get("restricted_paths", []):
            if file_path.startswith(restricted):
                if session.security_level != SecurityLevel.ADMIN:
                    return False, f"Access to {restricted} requires admin privileges"
        
        # Check write access
        if access_type in ["write", "modify", "delete"]:
            if not self.is_operation_allowed(session, "file_modification"):
                return False, "File modification not allowed for your security level"
        
        return True, "Approved"
    
    def log_security_event(self, event_type: str, user_id: str, action: str, 
                          resource: str, details: Any, risk_level: str, 
                          success: bool, ip_address: str = None):
        """Log security event"""
        
        event = SecurityEvent(
            timestamp=datetime.utcnow().isoformat(),
            event_type=event_type,
            user_id=user_id,
            action=action,
            resource=resource,
            risk_level=risk_level,
            success=success,
            details=details if isinstance(details, dict) else {"info": str(details)},
            ip_address=ip_address
        )
        
        self.audit_log.append(event)
        
        # Log to file
        self.logger.info(f"Security Event: {event_type} - {user_id} - {action} - {success}")
        
        # Alert on high-risk events
        if risk_level in ["high", "critical"] and not success:
            self.logger.warning(f"HIGH RISK SECURITY EVENT: {action} by {user_id} failed")
    
    def get_audit_log(self, user_id: str = None, hours: int = 24) -> List[Dict]:
        """Get audit log entries"""
        cutoff = datetime.utcnow() - timedelta(hours=hours)
        
        filtered_logs = []
        for event in self.audit_log:
            event_time = datetime.fromisoformat(event.timestamp)
            if event_time > cutoff:
                if not user_id or event.user_id == user_id:
                    filtered_logs.append(asdict(event))
        
        return filtered_logs
    
    def cleanup_old_logs(self):
        """Clean up old audit logs"""
        retention_days = self.config.get("audit_retention_days", 90)
        cutoff = datetime.utcnow() - timedelta(days=retention_days)
        
        self.audit_log = [
            event for event in self.audit_log 
            if datetime.fromisoformat(event.timestamp) > cutoff
        ]
    
    def require_approval(self, session: UserSession, operation: str, 
                        risk_level: str) -> bool:
        """Check if operation requires additional approval"""
        
        # Admin bypass
        if session.security_level == SecurityLevel.ADMIN:
            return False
        
        # Check config requirements
        require_approval = self.config.get("require_approval_for", [])
        if risk_level in require_approval:
            return True
        
        # High-risk operations always require approval for non-admin
        if risk_level == "critical":
            return True
        
        return False

class InputSanitizer:
    """Sanitizes and validates user inputs"""
    
    def __init__(self):
        self.dangerous_patterns = [
            r'[;&|`$(){}[\]<>]',  # Shell metacharacters
            r'\.\./',             # Path traversal
            r'eval\s*\(',         # Code evaluation
            r'exec\s*\(',         # Code execution
            r'import\s+os',       # OS module import
            r'subprocess',        # Subprocess calls
            r'__import__',        # Dynamic imports
        ]
    
    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename input"""
        # Remove dangerous characters
        sanitized = "".join(c for c in filename if c.isalnum() or c in ".-_/")
        
        # Remove path traversal attempts
        sanitized = sanitized.replace("../", "").replace("..\\", "")
        
        return sanitized
    
    def sanitize_command_args(self, args: List[str]) -> List[str]:
        """Sanitize command line arguments"""
        sanitized = []
        
        for arg in args:
            # Remove shell metacharacters
            clean_arg = "".join(c for c in arg if c.isalnum() or c in ".-_=:/")
            if clean_arg:
                sanitized.append(clean_arg)
        
        return sanitized
    
    def validate_query(self, query: str) -> Tuple[bool, str]:
        """Validate user query for dangerous patterns"""
        
        import re
        
        for pattern in self.dangerous_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return False, f"Query contains potentially dangerous pattern: {pattern}"
        
        # Check length
        if len(query) > 1000:
            return False, "Query too long"
        
        return True, "Valid"

if __name__ == "__main__":
    # Example usage
    security = SecurityManager()
    
    # Create test session
    token = security.create_session("test_user", SecurityLevel.STANDARD)
    print(f"Session token: {token}")
    
    # Validate session
    session = security.validate_session(token)
    if session:
        print(f"Session valid for user: {session.user_id}")
        
        # Test script validation
        allowed, reason = security.validate_script_execution(
            session, "test_script.sh", "medium"
        )
        print(f"Script execution allowed: {allowed}, reason: {reason}")
    
    # Test input sanitization
    sanitizer = InputSanitizer()
    test_filename = "../../../etc/passwd"
    clean_filename = sanitizer.sanitize_filename(test_filename)
    print(f"Sanitized filename: {test_filename} -> {clean_filename}")
    
    valid, reason = sanitizer.validate_query("How do I check CPU usage?")
    print(f"Query validation: {valid}, reason: {reason}")