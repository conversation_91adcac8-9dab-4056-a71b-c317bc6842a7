#!/usr/bin/env python3
"""
Knowledge Base Manager for TessellCS AI Agent
Handles repository indexing, embeddings, and content retrieval
"""

import os
import json
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional
import chromadb
from chromadb.config import Settings
import git
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from pygments import highlight
from pygments.lexers import get_lexer_for_filename, TextLexer
from pygments.formatters import get_formatter_by_name

class RepositoryIndexer:
    """Indexes repository content for AI agent consumption"""
    
    def __init__(self, repo_path: str, db_path: str = "./chroma_db"):
        self.repo_path = Path(repo_path)
        self.db_path = db_path
        self.client = chromadb.PersistentClient(path=db_path)
        self.collection = self.client.get_or_create_collection(
            name="tessellcs_ops",
            metadata={"description": "TessellCS Operations Repository"}
        )
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len
        )
        
    def get_file_hash(self, file_path: Path) -> str:
        """Generate hash for file content"""
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    
    def extract_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Extract metadata from file"""
        metadata = {
            "file_path": str(file_path.relative_to(self.repo_path)),
            "file_name": file_path.name,
            "file_type": file_path.suffix.lower(),
            "category": self.categorize_file(file_path),
            "size": file_path.stat().st_size,
            "modified": file_path.stat().st_mtime
        }
        
        # Add git information if available
        try:
            repo = git.Repo(self.repo_path)
            commits = list(repo.iter_commits(paths=str(file_path), max_count=1))
            if commits:
                commit = commits[0]
                metadata.update({
                    "last_commit": commit.hexsha,
                    "last_author": commit.author.name,
                    "commit_date": commit.committed_date
                })
        except:
            pass
            
        return metadata
    
    def categorize_file(self, file_path: Path) -> str:
        """Categorize file based on path and content"""
        path_str = str(file_path.relative_to(self.repo_path)).lower()
        
        categories = {
            "database_patching": ["database_patching", "patch"],
            "migration": ["migration", "migrate"],
            "mysql": ["mysql"],
            "oracle": ["oracle"],
            "postgres": ["postgres", "postgresql"],
            "mssql": ["mssql", "sqlserver"],
            "cloud_tasks": ["cloud_tasks", "aws", "azure", "gcp"],
            "monitoring": ["metrics", "monitoring", "observability"],
            "backup": ["backup", "restore"],
            "ssl": ["ssl", "tls", "cert"],
            "documentation": ["readme", ".md", "doc"],
            "configuration": [".conf", ".json", ".ini", "config"],
            "script": [".sh", ".py", ".sql"]
        }
        
        for category, keywords in categories.items():
            if any(keyword in path_str for keyword in keywords):
                return category
                
        return "general"
    
    def should_index_file(self, file_path: Path) -> bool:
        """Determine if file should be indexed"""
        # Skip hidden files, binaries, and temp files
        if file_path.name.startswith('.'):
            return False
            
        # Skip binary files
        binary_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.gif', '.zip', '.tar', '.gz'}
        if file_path.suffix.lower() in binary_extensions:
            return False
            
        # Include text files, scripts, configs, docs
        include_extensions = {'.sh', '.py', '.sql', '.md', '.txt', '.conf', '.json', '.ini', '.yaml', '.yml'}
        if file_path.suffix.lower() in include_extensions:
            return True
            
        # Include files without extensions that might be scripts
        if not file_path.suffix and file_path.is_file():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    first_line = f.readline()
                    if first_line.startswith('#!'):
                        return True
            except:
                pass
                
        return False
    
    def process_file(self, file_path: Path) -> List[Document]:
        """Process a single file into documents"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            return []
        
        metadata = self.extract_metadata(file_path)
        
        # For small files, keep as single document
        if len(content) <= 1000:
            return [Document(page_content=content, metadata=metadata)]
        
        # Split larger files into chunks
        documents = self.text_splitter.create_documents([content], [metadata])
        
        # Add chunk information
        for i, doc in enumerate(documents):
            doc.metadata.update({
                "chunk_id": i,
                "total_chunks": len(documents)
            })
            
        return documents
    
    def index_repository(self, force_reindex: bool = False) -> Dict[str, int]:
        """Index entire repository"""
        stats = {"processed": 0, "indexed": 0, "errors": 0, "skipped": 0}
        
        print(f"Indexing repository: {self.repo_path}")
        
        for file_path in self.repo_path.rglob('*'):
            if not file_path.is_file() or not self.should_index_file(file_path):
                continue
                
            stats["processed"] += 1
            
            try:
                # Check if file needs reindexing
                if not force_reindex:
                    file_hash = self.get_file_hash(file_path)
                    existing = self.collection.get(
                        where={"file_path": str(file_path.relative_to(self.repo_path))}
                    )
                    if existing['documents'] and existing['metadatas']:
                        if existing['metadatas'][0].get('file_hash') == file_hash:
                            stats["skipped"] += 1
                            continue
                
                # Process file
                documents = self.process_file(file_path)
                if not documents:
                    continue
                
                # Remove existing entries for this file
                self.collection.delete(
                    where={"file_path": str(file_path.relative_to(self.repo_path))}
                )
                
                # Add to collection
                for doc in documents:
                    doc.metadata["file_hash"] = self.get_file_hash(file_path)
                    
                self.collection.add(
                    documents=[doc.page_content for doc in documents],
                    metadatas=[doc.metadata for doc in documents],
                    ids=[f"{file_path.stem}_{i}_{hashlib.md5(doc.page_content.encode()).hexdigest()[:8]}" 
                         for i, doc in enumerate(documents)]
                )
                
                stats["indexed"] += 1
                
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                stats["errors"] += 1
        
        print(f"Indexing complete: {stats}")
        return stats
    
    def search(self, query: str, n_results: int = 5, category: Optional[str] = None) -> List[Dict]:
        """Search indexed content"""
        where_clause = {}
        if category:
            where_clause["category"] = category
            
        results = self.collection.query(
            query_texts=[query],
            n_results=n_results,
            where=where_clause if where_clause else None
        )
        
        formatted_results = []
        for i in range(len(results['documents'][0])):
            formatted_results.append({
                "content": results['documents'][0][i],
                "metadata": results['metadatas'][0][i],
                "score": results['distances'][0][i] if 'distances' in results else None
            })
            
        return formatted_results
    
    def get_file_content(self, file_path: str) -> Optional[str]:
        """Get full content of a specific file"""
        full_path = self.repo_path / file_path
        if not full_path.exists():
            return None
            
        try:
            with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        except Exception as e:
            print(f"Error reading {full_path}: {e}")
            return None
    
    def get_scripts_by_category(self, category: str) -> List[Dict]:
        """Get all scripts in a specific category"""
        results = self.collection.get(
            where={"category": category, "file_type": ".sh"},
            include=["metadatas", "documents"]
        )
        
        scripts = []
        for i in range(len(results['documents'])):
            scripts.append({
                "path": results['metadatas'][i]['file_path'],
                "name": results['metadatas'][i]['file_name'],
                "content": results['documents'][i]
            })
            
        return scripts

if __name__ == "__main__":
    # Example usage
    indexer = RepositoryIndexer("/Users/<USER>/DEV/claude-test/tessellcs-ops")
    stats = indexer.index_repository()
    print(f"Indexing stats: {stats}")