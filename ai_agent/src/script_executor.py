#!/usr/bin/env python3
"""
Script Execution Framework for TessellCS AI Agent
Provides safe, controlled execution of repository scripts
"""

import os
import subprocess
import tempfile
import json
import docker
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ExecutionResult:
    success: bool
    exit_code: int
    stdout: str
    stderr: str
    execution_time: float
    risk_assessment: RiskLevel
    warnings: List[str]

class ScriptValidator:
    """Validates scripts before execution"""
    
    def __init__(self):
        # Dangerous commands that should be blocked or require approval
        self.dangerous_patterns = {
            RiskLevel.CRITICAL: [
                'rm -rf /',
                'format',
                'mkfs',
                '> /dev/sd',
                'dd if=',
                'chmod 777',
                'chown root',
                'sudo su',
                'curl | sh',
                'wget | sh'
            ],
            RiskLevel.HIGH: [
                'rm -rf',
                'drop database',
                'truncate table',
                'delete from',
                'sudo',
                'su -',
                'passwd',
                'useradd',
                'userdel',
                'groupadd',
                'systemctl stop',
                'service stop',
                'kill -9',
                'pkill',
                'reboot',
                'shutdown'
            ],
            RiskLevel.MEDIUM: [
                'rm ',
                'mv ',
                'cp ',
                'chmod',
                'chown',
                'mount',
                'umount',
                'crontab',
                'systemctl',
                'service',
                'iptables'
            ]
        }
        
        # Safe patterns that are generally OK
        self.safe_patterns = [
            'echo',
            'cat',
            'ls',
            'grep',
            'awk',
            'sed',
            'sort',
            'uniq',
            'head',
            'tail',
            'wc',
            'find',
            'which',
            'whereis',
            'ps',
            'top',
            'df',
            'du',
            'free',
            'uptime',
            'date',
            'whoami',
            'pwd',
            'uname'
        ]
    
    def assess_risk(self, script_content: str) -> Tuple[RiskLevel, List[str]]:
        """Assess risk level of script content"""
        warnings = []
        max_risk = RiskLevel.LOW
        
        lines = script_content.lower().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Check for dangerous patterns
            for risk_level, patterns in self.dangerous_patterns.items():
                for pattern in patterns:
                    if pattern in line:
                        warnings.append(f"Detected {risk_level.value} risk pattern: {pattern}")
                        if risk_level.value == 'critical' or (risk_level.value == 'high' and max_risk != RiskLevel.CRITICAL):
                            max_risk = risk_level
                        elif risk_level.value == 'medium' and max_risk == RiskLevel.LOW:
                            max_risk = risk_level
        
        # Additional checks
        if 'eval' in script_content or '$((' in script_content:
            warnings.append("Script contains dynamic evaluation - potential security risk")
            if max_risk == RiskLevel.LOW:
                max_risk = RiskLevel.MEDIUM
        
        if script_content.count('sudo') > 3:
            warnings.append("Multiple sudo commands detected")
            if max_risk == RiskLevel.LOW:
                max_risk = RiskLevel.MEDIUM
                
        return max_risk, warnings
    
    def validate_script_path(self, script_path: str, repo_path: str) -> bool:
        """Validate that script is within repository bounds"""
        try:
            script_full = Path(script_path).resolve()
            repo_full = Path(repo_path).resolve()
            return str(script_full).startswith(str(repo_full))
        except:
            return False

class ScriptExecutor:
    """Executes scripts with safety mechanisms"""
    
    def __init__(self, repo_path: str, use_docker: bool = True):
        self.repo_path = Path(repo_path)
        self.use_docker = use_docker
        self.validator = ScriptValidator()
        self.docker_client = None
        
        if use_docker:
            try:
                self.docker_client = docker.from_env()
            except Exception as e:
                logging.warning(f"Docker not available: {e}")
                self.use_docker = False
        
        # Approved scripts cache
        self.approved_scripts = set()
        self.load_approved_scripts()
    
    def load_approved_scripts(self):
        """Load list of pre-approved scripts"""
        approved_file = self.repo_path / "ai_agent" / "approved_scripts.json"
        if approved_file.exists():
            try:
                with open(approved_file) as f:
                    data = json.load(f)
                    self.approved_scripts = set(data.get("approved", []))
            except Exception as e:
                logging.error(f"Error loading approved scripts: {e}")
    
    def save_approved_scripts(self):
        """Save approved scripts list"""
        approved_file = self.repo_path / "ai_agent" / "approved_scripts.json"
        approved_file.parent.mkdir(exist_ok=True)
        
        try:
            with open(approved_file, 'w') as f:
                json.dump({"approved": list(self.approved_scripts)}, f, indent=2)
        except Exception as e:
            logging.error(f"Error saving approved scripts: {e}")
    
    def execute_in_docker(self, script_path: str, args: List[str] = None) -> ExecutionResult:
        """Execute script in Docker container"""
        if not self.docker_client:
            raise RuntimeError("Docker not available")
        
        # Create container with limited resources
        container_config = {
            "image": "ubuntu:20.04",
            "command": f"bash /repo/{script_path.relative_to(self.repo_path)}",
            "volumes": {str(self.repo_path): {"bind": "/repo", "mode": "ro"}},
            "mem_limit": "256m",
            "cpuset_cpus": "0",
            "network_disabled": True,
            "user": "nobody",
            "remove": True
        }
        
        if args:
            container_config["command"] += " " + " ".join(args)
        
        try:
            container = self.docker_client.containers.run(**container_config, detach=True)
            result = container.wait()
            
            logs = container.logs().decode('utf-8')
            
            return ExecutionResult(
                success=result['StatusCode'] == 0,
                exit_code=result['StatusCode'],
                stdout=logs,
                stderr="",
                execution_time=0,  # TODO: measure time
                risk_assessment=RiskLevel.LOW,
                warnings=[]
            )
            
        except Exception as e:
            return ExecutionResult(
                success=False,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                execution_time=0,
                risk_assessment=RiskLevel.HIGH,
                warnings=[f"Docker execution failed: {e}"]
            )
    
    def execute_native(self, script_path: str, args: List[str] = None, timeout: int = 300) -> ExecutionResult:
        """Execute script natively with subprocess"""
        import time
        
        start_time = time.time()
        
        try:
            cmd = [str(script_path)]
            if args:
                cmd.extend(args)
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=script_path.parent
            )
            
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                success=result.returncode == 0,
                exit_code=result.returncode,
                stdout=result.stdout,
                stderr=result.stderr,
                execution_time=execution_time,
                risk_assessment=RiskLevel.MEDIUM,
                warnings=[]
            )
            
        except subprocess.TimeoutExpired:
            return ExecutionResult(
                success=False,
                exit_code=-1,
                stdout="",
                stderr="Script execution timed out",
                execution_time=timeout,
                risk_assessment=RiskLevel.HIGH,
                warnings=["Script timed out"]
            )
        except Exception as e:
            return ExecutionResult(
                success=False,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                execution_time=time.time() - start_time,
                risk_assessment=RiskLevel.HIGH,
                warnings=[f"Execution failed: {e}"]
            )
    
    def execute_script(self, script_path: str, args: List[str] = None, 
                      force_approval: bool = False) -> ExecutionResult:
        """Main script execution method"""
        
        # Validate script path
        if not self.validator.validate_script_path(script_path, str(self.repo_path)):
            return ExecutionResult(
                success=False,
                exit_code=-1,
                stdout="",
                stderr="Script path not within repository bounds",
                execution_time=0,
                risk_assessment=RiskLevel.CRITICAL,
                warnings=["Invalid script path"]
            )
        
        script_full_path = Path(script_path)
        if not script_full_path.exists():
            script_full_path = self.repo_path / script_path
        
        if not script_full_path.exists():
            return ExecutionResult(
                success=False,
                exit_code=-1,
                stdout="",
                stderr="Script not found",
                execution_time=0,
                risk_assessment=RiskLevel.LOW,
                warnings=["Script file not found"]
            )
        
        # Read and assess script
        try:
            with open(script_full_path, 'r') as f:
                script_content = f.read()
        except Exception as e:
            return ExecutionResult(
                success=False,
                exit_code=-1,
                stdout="",
                stderr=f"Cannot read script: {e}",
                execution_time=0,
                risk_assessment=RiskLevel.MEDIUM,
                warnings=["Script read error"]
            )
        
        # Risk assessment
        risk_level, warnings = self.validator.assess_risk(script_content)
        
        # Check if script is pre-approved or requires approval
        script_rel_path = str(script_full_path.relative_to(self.repo_path))
        
        if risk_level == RiskLevel.CRITICAL:
            return ExecutionResult(
                success=False,
                exit_code=-1,
                stdout="",
                stderr="Script contains critical risk patterns and cannot be executed",
                execution_time=0,
                risk_assessment=risk_level,
                warnings=warnings
            )
        
        if risk_level == RiskLevel.HIGH and script_rel_path not in self.approved_scripts and not force_approval:
            return ExecutionResult(
                success=False,
                exit_code=-1,
                stdout="",
                stderr="High-risk script requires explicit approval",
                execution_time=0,
                risk_assessment=risk_level,
                warnings=warnings + ["Script requires approval"]
            )
        
        # Execute script
        if force_approval and script_rel_path not in self.approved_scripts:
            self.approved_scripts.add(script_rel_path)
            self.save_approved_scripts()
        
        # Choose execution method
        if self.use_docker and risk_level in [RiskLevel.MEDIUM, RiskLevel.HIGH]:
            result = self.execute_in_docker(script_full_path, args)
        else:
            result = self.execute_native(script_full_path, args)
        
        result.risk_assessment = risk_level
        result.warnings.extend(warnings)
        
        return result
    
    def approve_script(self, script_path: str):
        """Approve a script for future execution"""
        script_full_path = Path(script_path)
        if not script_full_path.exists():
            script_full_path = self.repo_path / script_path
        
        if script_full_path.exists():
            script_rel_path = str(script_full_path.relative_to(self.repo_path))
            self.approved_scripts.add(script_rel_path)
            self.save_approved_scripts()
            return True
        return False
    
    def get_script_info(self, script_path: str) -> Dict[str, Any]:
        """Get information about a script without executing it"""
        script_full_path = Path(script_path)
        if not script_full_path.exists():
            script_full_path = self.repo_path / script_path
        
        if not script_full_path.exists():
            return {"error": "Script not found"}
        
        try:
            with open(script_full_path, 'r') as f:
                content = f.read()
        except Exception as e:
            return {"error": f"Cannot read script: {e}"}
        
        risk_level, warnings = self.validator.assess_risk(content)
        script_rel_path = str(script_full_path.relative_to(self.repo_path))
        
        return {
            "path": script_rel_path,
            "size": script_full_path.stat().st_size,
            "risk_level": risk_level.value,
            "warnings": warnings,
            "approved": script_rel_path in self.approved_scripts,
            "executable": os.access(script_full_path, os.X_OK)
        }

if __name__ == "__main__":
    # Example usage
    executor = ScriptExecutor("/Users/<USER>/DEV/claude-test/tessellcs-ops")
    
    # Get info about a script
    info = executor.get_script_info("Uday-Test/check_cpu_usage.sh")
    print(f"Script info: {info}")
    
    # Execute if safe
    if info.get("risk_level") in ["low", "medium"]:
        result = executor.execute_script("Uday-Test/check_cpu_usage.sh")
        print(f"Execution result: {result}")