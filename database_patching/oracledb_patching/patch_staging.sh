#!/bin/bash
# Oracle Database Patching - Patch Download & Staging
# ==================================================

# Source configuration and logging
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/patch_config.conf"
source "$SCRIPT_DIR/logging_utils.sh"

# Patch staging variables
PATCH_ID=""
PATCH_FILE=""
PATCH_CHECKSUM=""
STAGED_PATCH_DIR=""

# Function to download patch from Oracle Support (MOS)
download_patch_from_mos() {
    local patch_id="$1"
    local patch_file="$2"
    
    log_info "Downloading patch $patch_id from Oracle Support..."
    
    if [[ -z "$MOS_USERNAME" || ! -f "$MOS_PASSWORD_FILE" ]]; then
        log_error "MOS credentials not configured properly"
        return 1
    fi
    
    # This is a template - Oracle doesn't provide direct API access
    # You would typically use tools like wget/curl with proper authentication
    # or Oracle's own download utilities if available
    
    log_warn "MOS download automation requires custom implementation"
    log_info "Please manually download patch $patch_id and place in $PATCH_STAGING_DIR"
    
    return 1
}

# Function to stage patch from local source
stage_patch_from_local() {
    local source_path="$1"
    local patch_id="$2"
    
    log_info "Staging patch from local source: $source_path"
    
    if [[ ! -f "$source_path" ]]; then
        log_error "Patch file not found: $source_path"
        return 1
    fi
    
    # Create staging directory
    STAGED_PATCH_DIR="$PATCH_STAGING_DIR/${patch_id}_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$STAGED_PATCH_DIR"
    
    # Copy patch file to staging area
    log_info "Copying patch file to staging directory..."
    if execute_and_log "cp '$source_path' '$STAGED_PATCH_DIR/'" "Copy patch file"; then
        PATCH_FILE="$STAGED_PATCH_DIR/$(basename "$source_path")"
        log_info "Patch staged successfully: $PATCH_FILE"
        return 0
    else
        log_error "Failed to stage patch file"
        return 1
    fi
}

# Function to verify patch file integrity
verify_patch_integrity() {
    local patch_file="$1"
    local expected_checksum="$2"
    
    log_info "Verifying patch file integrity..."
    
    if [[ ! -f "$patch_file" ]]; then
        log_error "Patch file not found: $patch_file"
        return 1
    fi
    
    # Calculate checksum
    local actual_checksum=""
    if command -v sha256sum >/dev/null 2>&1; then
        actual_checksum=$(sha256sum "$patch_file" | awk '{print $1}')
    elif command -v shasum >/dev/null 2>&1; then
        actual_checksum=$(shasum -a 256 "$patch_file" | awk '{print $1}')
    else
        log_warn "No checksum utility available - skipping integrity check"
        return 0
    fi
    
    log_info "Calculated checksum: $actual_checksum"
    
    if [[ -n "$expected_checksum" ]]; then
        if [[ "$actual_checksum" == "$expected_checksum" ]]; then
            log_info "Patch file integrity verified successfully"
            return 0
        else
            log_error "Patch file integrity check failed"
            log_error "Expected: $expected_checksum"
            log_error "Actual: $actual_checksum"
            return 1
        fi
    else
        log_warn "No expected checksum provided - storing calculated checksum"
        PATCH_CHECKSUM="$actual_checksum"
        return 0
    fi
}

# Function to extract patch files
extract_patch_files() {
    local patch_file="$1"
    local extract_dir="$2"
    
    log_info "Extracting patch files from: $patch_file"
    
    # Determine file type and extract accordingly
    local file_type=$(file "$patch_file" | tr '[:upper:]' '[:lower:]')
    
    if [[ "$file_type" == *"zip"* ]]; then
        if command -v unzip >/dev/null 2>&1; then
            execute_and_log "cd '$extract_dir' && unzip -q '$patch_file'" "Extract ZIP patch file"
        else
            log_error "unzip utility not available"
            return 1
        fi
    elif [[ "$file_type" == *"gzip"* || "$file_type" == *"tar"* ]]; then
        execute_and_log "cd '$extract_dir' && tar -xzf '$patch_file'" "Extract TAR.GZ patch file"
    else
        log_warn "Unknown patch file format - attempting as ZIP"
        execute_and_log "cd '$extract_dir' && unzip -q '$patch_file'" "Extract unknown format as ZIP"
    fi
    
    local extract_result=$?
    
    if [[ $extract_result -eq 0 ]]; then
        log_info "Patch files extracted successfully"
        
        # List extracted contents
        log_info "Extracted patch contents:"
        find "$extract_dir" -type f -name "*.xml" -o -name "README*" -o -name "*.txt" | while read -r file; do
            log_info "  $(basename "$file")"
        done
        
        return 0
    else
        log_error "Failed to extract patch files"
        return 1
    fi
}

# Function to validate patch compatibility
validate_patch_compatibility() {
    local patch_dir="$1"
    
    log_info "Validating patch compatibility with current Oracle installation..."
    
    # Look for patch metadata files
    local patch_xml=$(find "$patch_dir" -name "*.xml" | head -1)
    local readme_file=$(find "$patch_dir" -name "README*" | head -1)
    
    if [[ -f "$patch_xml" ]]; then
        log_info "Found patch metadata: $(basename "$patch_xml")"
        
        # Extract patch information (basic parsing)
        local patch_info=$(grep -i "description\|version\|platform" "$patch_xml" 2>/dev/null || echo "No metadata found")
        log_info "Patch metadata: $patch_info"
    fi
    
    if [[ -f "$readme_file" ]]; then
        log_info "Found README file: $(basename "$readme_file")"
        
        # Check for compatibility warnings
        local warnings=$(grep -i "warning\|caution\|prerequisite" "$readme_file" 2>/dev/null | head -5)
        if [[ -n "$warnings" ]]; then
            log_warn "Patch warnings found:"
            echo "$warnings" | while read -r line; do
                log_warn "  $line"
            done
        fi
    fi
    
    # Check OPatch compatibility
    if [[ -d "$patch_dir" ]]; then
        local opatch_check=$(opatch query -all "$patch_dir" 2>/dev/null)
        if [[ $? -eq 0 ]]; then
            log_info "OPatch compatibility check passed"
            log_debug "OPatch query result: $opatch_check"
        else
            log_warn "OPatch compatibility check failed or patch not recognized"
        fi
    fi
    
    return 0
}

# Function to create patch staging report
create_staging_report() {
    local patch_id="$1"
    local staging_dir="$2"
    
    local report_file="$PATCH_LOGS_DIR/patch_staging_report_${patch_id}_$(date +%Y%m%d).txt"
    
    cat > "$report_file" << EOF
Oracle Patch Staging Report
==========================

Patch ID: $patch_id
Staging Directory: $staging_dir
Timestamp: $(date)
Hostname: $(hostname)
Oracle SID: $ORACLE_SID

Patch File Information:
- File: $PATCH_FILE
- Size: $(ls -lh "$PATCH_FILE" 2>/dev/null | awk '{print $5}' || echo "N/A")
- Checksum: $PATCH_CHECKSUM

Staged Files:
$(find "$staging_dir" -type f | sort)

Oracle Environment:
- Oracle Home: $ORACLE_HOME
- Oracle Version: $(sqlplus -s / as sysdba <<< "SELECT banner FROM v\$version WHERE banner LIKE 'Oracle%';" 2>/dev/null | grep Oracle || echo "N/A")
- OPatch Version: $(opatch version 2>/dev/null | grep "OPatch Version" | awk '{print $3}' || echo "N/A")

System Information:
- Available Disk Space: $(df -h "$PATCH_STAGING_DIR" | tail -1 | awk '{print $4}')
- Current Load: $(uptime | awk -F'load average:' '{print $2}')

EOF
    
    log_info "Patch staging report created: $report_file"
    echo "$report_file"
}

# Function to cleanup old staged patches
cleanup_old_patches() {
    log_info "Cleaning up old staged patches..."
    
    # Remove staging directories older than 7 days
    find "$PATCH_STAGING_DIR" -type d -name "*_[0-9]*" -mtime +7 -exec rm -rf {} \; 2>/dev/null
    
    # Remove patch files older than 30 days
    find "$PATCH_STAGING_DIR" -type f -name "*.zip" -o -name "*.tar.gz" -mtime +30 -delete 2>/dev/null
    
    log_info "Old patch cleanup completed"
}

# Main patch staging function
stage_oracle_patch() {
    local patch_source="$1"
    local patch_id="$2"
    local expected_checksum="$3"
    
    log_info "=== Starting Oracle Patch Staging Process ==="
    log_info "Patch ID: $patch_id"
    log_info "Source: $patch_source"
    
    # Set global variables
    PATCH_ID="$patch_id"
    
    # Create base staging directory
    mkdir -p "$PATCH_STAGING_DIR"
    
    # Stage patch based on source type
    if [[ "$patch_source" == "MOS" ]]; then
        # Download from Oracle Support
        if ! download_patch_from_mos "$patch_id" "$patch_file"; then
            log_error "Failed to download patch from MOS"
            return 1
        fi
    elif [[ -f "$patch_source" ]]; then
        # Stage from local file
        if ! stage_patch_from_local "$patch_source" "$patch_id"; then
            log_error "Failed to stage patch from local source"
            return 1
        fi
    else
        log_error "Invalid patch source: $patch_source"
        return 1
    fi
    
    # Verify patch integrity
    if [[ "$CHECKSUM_VERIFICATION" == "true" ]]; then
        if ! verify_patch_integrity "$PATCH_FILE" "$expected_checksum"; then
            log_error "Patch integrity verification failed"
            return 1
        fi
    fi
    
    # Extract patch files
    if ! extract_patch_files "$PATCH_FILE" "$STAGED_PATCH_DIR"; then
        log_error "Failed to extract patch files"
        return 1
    fi
    
    # Validate patch compatibility
    validate_patch_compatibility "$STAGED_PATCH_DIR"
    
    # Create staging report
    local report_file=$(create_staging_report "$patch_id" "$STAGED_PATCH_DIR")
    
    # Cleanup old patches
    cleanup_old_patches
    
    log_info "=== Patch Staging Completed Successfully ==="
    log_info "Staged patch directory: $STAGED_PATCH_DIR"
    log_info "Staging report: $report_file"
    
    # Export variables for use by other modules
    export STAGED_PATCH_DIR
    export PATCH_FILE
    export PATCH_CHECKSUM
    
    return 0
}

# Export functions for use by main script
export -f stage_oracle_patch
