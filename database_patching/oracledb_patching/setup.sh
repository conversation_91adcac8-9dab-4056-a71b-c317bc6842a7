#!/bin/bash
# Oracle Patching Automation - Setup Script
# =========================================

echo "Oracle Database Patching Automation Setup"
echo "=========================================="

# Make all shell scripts executable
echo "Making scripts executable..."
chmod +x *.sh
chmod +x setup.sh

# Create required directories
echo "Creating required directories..."
mkdir -p /u01/patches/{staging,backups,logs}
mkdir -p /secure

echo "Setting up directory permissions..."
# Set appropriate permissions (adjust as needed for your environment)
chmod 755 /u01/patches
chmod 755 /u01/patches/{staging,backups,logs}
chmod 700 /secure

# Create sample password files (you should replace these with actual secure passwords)
echo "Creating sample password files..."
echo "# Replace with actual Oracle SYS password" > /secure/oracle_sys_password
echo "# Replace with actual MOS credentials" > /secure/mos_credentials

# Set secure permissions on password files
chmod 600 /secure/*

# Validate Oracle environment
echo "Validating Oracle environment..."
if [[ -z "$ORACLE_HOME" ]]; then
    echo "WARNING: ORACLE_HOME not set. Please set it in patch_config.conf"
fi

if [[ -z "$ORACLE_SID" ]]; then
    echo "WARNING: ORACLE_SID not set. Please set it in patch_config.conf"
fi

# Check if Oracle is accessible
if command -v sqlplus >/dev/null 2>&1; then
    echo "Oracle SQL*Plus found: $(which sqlplus)"
else
    echo "WARNING: Oracle SQL*Plus not found in PATH"
fi

if command -v opatch >/dev/null 2>&1; then
    echo "OPatch found: $(which opatch)"
    echo "OPatch version: $(opatch version | grep "OPatch Version" | awk '{print $3}')"
else
    echo "WARNING: OPatch not found in PATH"
fi

# Check for required system utilities
echo "Checking system utilities..."
for cmd in tar gzip unzip mail df ps grep awk sed; do
    if command -v $cmd >/dev/null 2>&1; then
        echo "✓ $cmd found"
    else
        echo "✗ $cmd NOT found - please install"
    fi
done

# Test configuration file
echo "Testing configuration file..."
if [[ -f "patch_config.conf" ]]; then
    echo "✓ Configuration file found"
    
    # Source and validate key variables
    source patch_config.conf
    
    if [[ -n "$ORACLE_HOME" && -n "$ORACLE_SID" ]]; then
        echo "✓ Basic Oracle configuration present"
    else
        echo "✗ Oracle configuration incomplete"
    fi
    
    if [[ -n "$PATCH_BASE_DIR" ]]; then
        echo "✓ Patch directories configured"
    else
        echo "✗ Patch directories not configured"
    fi
else
    echo "✗ Configuration file not found"
fi

# Create a simple test script
echo "Creating test script..."
cat > test_environment.sh << 'EOF'
#!/bin/bash
# Simple environment test script

echo "Oracle Patching Automation - Environment Test"
echo "============================================="

# Source configuration
source patch_config.conf

echo "Configuration Test:"
echo "- Oracle Home: $ORACLE_HOME"
echo "- Oracle SID: $ORACLE_SID"
echo "- Patch Base Dir: $PATCH_BASE_DIR"
echo "- Backup Type: $BACKUP_TYPE"

echo ""
echo "Directory Test:"
for dir in "$PATCH_STAGING_DIR" "$PATCH_BACKUP_DIR" "$PATCH_LOGS_DIR"; do
    if [[ -d "$dir" ]]; then
        echo "✓ $dir exists"
    else
        echo "✗ $dir does not exist"
    fi
done

echo ""
echo "Oracle Connectivity Test:"
if sqlplus -s / as sysdba <<< "SELECT 'Connection successful' FROM dual;" 2>/dev/null | grep -q "Connection successful"; then
    echo "✓ Oracle database connection successful"
else
    echo "✗ Oracle database connection failed"
fi

echo ""
echo "OPatch Test:"
if opatch version >/dev/null 2>&1; then
    echo "✓ OPatch accessible"
    echo "  Version: $(opatch version | grep "OPatch Version" | awk '{print $3}')"
else
    echo "✗ OPatch not accessible"
fi

echo ""
echo "Validation Test:"
if ./oracle_patch_automation.sh validate 2>/dev/null; then
    echo "✓ Pre-patch validation passed"
else
    echo "✗ Pre-patch validation failed (check logs for details)"
fi

echo ""
echo "Test completed. Check any failed items before proceeding with patching."
EOF

chmod +x test_environment.sh

echo ""
echo "Setup completed!"
echo ""
echo "Next steps:"
echo "1. Edit patch_config.conf to match your environment"
echo "2. Update password files in /secure/ with actual credentials"
echo "3. Run ./test_environment.sh to validate setup"
echo "4. Review README.md and RUNBOOK.md for usage instructions"
echo ""
echo "Example usage:"
echo "  ./oracle_patch_automation.sh validate"
echo "  ./oracle_patch_automation.sh status"
echo "  ./oracle_patch_automation.sh apply-patch -p 12345678 -s /path/to/patch.zip -t OPATCH"
echo ""
echo "For help: ./oracle_patch_automation.sh --help"
