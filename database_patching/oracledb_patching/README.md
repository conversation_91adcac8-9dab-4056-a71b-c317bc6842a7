# Oracle Database Patching Automation

A comprehensive shell script framework for automating Oracle database patching processes with built-in validation, backup, rollback, and reporting capabilities.

## Features

- **Automated Pre-patch Validation**: Disk space, system load, database status, and backup verification
- **Flexible Backup Strategies**: RMAN, filesystem snapshots, or traditional file backups
- **Intelligent Patch Staging**: Download, verify, and extract patches with integrity checking
- **Multiple Patch Application Methods**: OPatch, DataPatch, and RAC-aware patching
- **Comprehensive Health Checks**: Post-patch validation and performance monitoring
- **Automatic Rollback**: Multiple rollback strategies including restore points and RMAN recovery
- **Detailed Logging and Reporting**: Centralized logging with HTML reports and email notifications
- **Version-Specific Support**: Handles Oracle 11g, 12c, 18c, 19c, and 21c variations

## Quick Start

### 1. Configuration

Edit `patch_config.conf` to match your environment:

```bash
# Oracle Environment
export ORACLE_BASE="/u01/app/oracle"
export ORACLE_HOME="/u01/app/oracle/product/19.0.0/dbhome_1"
export ORACLE_SID="ORCL"

# Patching Directories
PATCH_BASE_DIR="/u01/patches"
PATCH_STAGING_DIR="$PATCH_BASE_DIR/staging"
PATCH_BACKUP_DIR="$PATCH_BASE_DIR/backups"
PATCH_LOGS_DIR="$PATCH_BASE_DIR/logs"

# Backup Configuration
BACKUP_TYPE="RMAN"  # Options: RMAN, SNAPSHOT, FILESYSTEM
```

### 2. Basic Usage

```bash
# Make scripts executable
chmod +x *.sh

# Apply a patch
./oracle_patch_automation.sh apply-patch -p 12345678 -s /patches/p12345678.zip -t OPATCH

# Run validation only
./oracle_patch_automation.sh validate

# Check system status
./oracle_patch_automation.sh status

# Rollback a patch
./oracle_patch_automation.sh rollback -p 12345678
```

## Architecture

### Core Components

1. **oracle_patch_automation.sh** - Main orchestration script
2. **patch_config.conf** - Configuration file
3. **logging_utils.sh** - Logging and reporting utilities
4. **pre_patch_validation.sh** - Pre-patch validation checks
5. **backup_automation.sh** - Backup creation and verification
6. **patch_staging.sh** - Patch download and staging
7. **patch_application.sh** - Patch application logic
8. **post_patch_validation.sh** - Post-patch health checks
9. **rollback_handler.sh** - Rollback and error handling

### Workflow

```
Pre-patch Validation → Backup Creation → Patch Staging → 
Patch Application → Post-patch Validation → Reporting
                ↓ (on failure)
            Automatic Rollback
```

## Commands

### apply-patch
Apply a patch to the Oracle database with full validation and backup.

```bash
./oracle_patch_automation.sh apply-patch [OPTIONS]
```

**Options:**
- `-p, --patch-id ID` - Patch ID (required)
- `-s, --patch-source PATH` - Path to patch file (required)
- `-t, --patch-type TYPE` - OPATCH, DATAPATCH, or RAC
- `-c, --checksum HASH` - Expected checksum for verification
- `-f, --force` - Skip some validations
- `-d, --dry-run` - Validation only, no actual patching

### validate
Run pre-patch validation checks only.

```bash
./oracle_patch_automation.sh validate
```

### backup
Create a pre-patch backup only.

```bash
./oracle_patch_automation.sh backup
```

### health-check
Run post-patch health checks.

```bash
./oracle_patch_automation.sh health-check -p PATCH_ID
```

### rollback
Rollback a previously applied patch.

```bash
./oracle_patch_automation.sh rollback -p PATCH_ID
```

### status
Display current system and patch status.

```bash
./oracle_patch_automation.sh status
```

### cleanup
Clean up old logs and temporary files.

```bash
./oracle_patch_automation.sh cleanup
```

## Configuration Options

### Oracle Environment
- `ORACLE_BASE`, `ORACLE_HOME`, `ORACLE_SID` - Oracle environment variables
- `DB_USER`, `DB_PASSWORD_FILE` - Database connection settings

### Backup Configuration
- `BACKUP_TYPE` - RMAN, SNAPSHOT, or FILESYSTEM
- `RMAN_BACKUP_LOCATION` - RMAN backup destination
- `SNAPSHOT_MOUNT_POINT` - Snapshot mount point for LVM snapshots

### Validation Thresholds
- `MIN_DISK_SPACE_GB` - Minimum required disk space
- `MAX_CPU_LOAD` - Maximum acceptable CPU load
- `MAX_MEMORY_USAGE` - Maximum acceptable memory usage

### RAC Configuration
- `RAC_ENABLED` - Enable RAC-specific patching
- `RAC_NODES` - Comma-separated list of RAC nodes

### Notification Settings
- `EMAIL_ENABLED` - Enable email notifications
- `EMAIL_RECIPIENTS` - Email addresses for notifications
- `SMTP_SERVER` - SMTP server for email delivery

## Backup Strategies

### RMAN Backup
- Full database backup with archive logs
- Automatic retention policy management
- Backup verification and validation

### Snapshot Backup
- LVM snapshot creation
- Supports multiple volume groups
- Fast backup and restore capabilities

### Filesystem Backup
- Oracle Home and configuration backup
- Compressed archive creation
- Suitable for smaller environments

## Rollback Methods

### Restore Point
- Oracle Flashback Database
- Fastest rollback method
- Requires flashback to be enabled

### RMAN Restore
- Complete database restore from backup
- Most reliable rollback method
- Longer recovery time

### OPatch Rollback
- Native OPatch rollback functionality
- Suitable for Oracle Home patches
- Quick rollback for compatible patches

### Filesystem Restore
- Restore Oracle Home from backup
- Manual process with service restart
- Last resort option

## Logging and Reporting

### Log Levels
- `DEBUG` - Detailed debugging information
- `INFO` - General information messages
- `WARN` - Warning messages
- `ERROR` - Error messages
- `AUDIT` - Audit trail events

### Reports
- HTML patch reports with system information
- Email notifications with status updates
- Centralized audit logging
- Performance metrics capture

## Security Considerations

- Store passwords in secure files with restricted permissions
- Use Oracle Wallet for database authentication when possible
- Implement proper file system permissions for patch directories
- Enable audit logging for compliance requirements

## Troubleshooting

### Common Issues

1. **Permission Errors**
   - Ensure Oracle user has proper permissions
   - Check file system permissions on patch directories

2. **Backup Failures**
   - Verify RMAN configuration
   - Check disk space availability
   - Validate backup destinations

3. **Patch Application Failures**
   - Review OPatch prerequisites
   - Check for patch conflicts
   - Verify Oracle inventory

4. **Rollback Issues**
   - Ensure backup integrity
   - Check flashback database settings
   - Verify restore point availability

### Log Analysis
Check the following log files for troubleshooting:
- `$PATCH_LOGS_DIR/oracle_patch_*.log` - Main execution logs
- `$PATCH_LOGS_DIR/patch_audit.log` - Audit trail
- `$ORACLE_HOME/cfgtoollogs/opatch/` - OPatch logs

## Best Practices

1. **Testing**
   - Always test in development environment first
   - Use dry-run mode for validation
   - Verify rollback procedures

2. **Scheduling**
   - Schedule during maintenance windows
   - Consider database workload patterns
   - Plan for extended downtime if needed

3. **Monitoring**
   - Monitor system resources during patching
   - Set up alerting for failures
   - Review logs regularly

4. **Documentation**
   - Document environment-specific configurations
   - Maintain patch application history
   - Update runbooks regularly

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Consult Oracle documentation for patch-specific requirements
4. Test in development environment before production use

## License

This project is provided as-is for educational and operational use. Please review and test thoroughly before using in production environments.
