#!/bin/bash
# Oracle Database Patching Automation - Main Orchestration Script
# ===============================================================

# Script metadata
SCRIPT_VERSION="1.0.0"
SCRIPT_NAME="Oracle Patch Automation"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source all modules
source "$SCRIPT_DIR/patch_config.conf"
source "$SCRIPT_DIR/logging_utils.sh"
source "$SCRIPT_DIR/pre_patch_validation.sh"
source "$SCRIPT_DIR/backup_automation.sh"
source "$SCRIPT_DIR/patch_staging.sh"
source "$SCRIPT_DIR/patch_application.sh"
source "$SCRIPT_DIR/post_patch_validation.sh"
source "$SCRIPT_DIR/rollback_handler.sh"

# Global variables
PATCH_SESSION_ID=""
PATCH_START_TIME=""
PATCH_END_TIME=""
OVERALL_STATUS=""
ROLLBACK_INFO=""

# Function to display usage
show_usage() {
    cat << EOF
$SCRIPT_NAME v$SCRIPT_VERSION

Usage: $0 [OPTIONS] COMMAND

COMMANDS:
    apply-patch     Apply a patch to Oracle database
    rollback        Rollback a previously applied patch
    validate        Run pre-patch validation only
    backup          Create pre-patch backup only
    health-check    Run post-patch health checks only
    status          Show current patch status
    cleanup         Cleanup old logs and temporary files

OPTIONS:
    -p, --patch-id ID           Patch ID to apply/rollback
    -s, --patch-source PATH     Path to patch file or 'MOS' for download
    -t, --patch-type TYPE       Patch type: OPATCH, DATAPATCH, RAC
    -c, --checksum HASH         Expected patch file checksum
    -f, --force                 Force operation (skip some validations)
    -d, --dry-run              Perform dry run (validation only)
    -v, --verbose              Enable verbose logging
    -h, --help                 Show this help message

EXAMPLES:
    # Apply a patch from local file
    $0 apply-patch -p 12345678 -s /patches/p12345678.zip -t OPATCH

    # Apply a patch with checksum verification
    $0 apply-patch -p 12345678 -s /patches/p12345678.zip -c sha256hash -t DATAPATCH

    # Run validation only
    $0 validate

    # Rollback a patch
    $0 rollback -p 12345678

    # Check system status
    $0 status

CONFIGURATION:
    Edit patch_config.conf to customize settings for your environment.

EOF
}

# Function to parse command line arguments
parse_arguments() {
    COMMAND=""
    PATCH_ID=""
    PATCH_SOURCE=""
    PATCH_TYPE="OPATCH"
    EXPECTED_CHECKSUM=""
    FORCE_MODE="false"
    DRY_RUN="false"
    VERBOSE_MODE="false"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            apply-patch|rollback|validate|backup|health-check|status|cleanup)
                COMMAND="$1"
                shift
                ;;
            -p|--patch-id)
                PATCH_ID="$2"
                shift 2
                ;;
            -s|--patch-source)
                PATCH_SOURCE="$2"
                shift 2
                ;;
            -t|--patch-type)
                PATCH_TYPE="$2"
                shift 2
                ;;
            -c|--checksum)
                EXPECTED_CHECKSUM="$2"
                shift 2
                ;;
            -f|--force)
                FORCE_MODE="true"
                shift
                ;;
            -d|--dry-run)
                DRY_RUN="true"
                shift
                ;;
            -v|--verbose)
                VERBOSE_MODE="true"
                LOG_LEVEL="DEBUG"
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Validate required parameters
    if [[ -z "$COMMAND" ]]; then
        log_error "Command is required"
        show_usage
        exit 1
    fi
    
    if [[ "$COMMAND" == "apply-patch" || "$COMMAND" == "rollback" ]]; then
        if [[ -z "$PATCH_ID" ]]; then
            log_error "Patch ID is required for $COMMAND command"
            exit 1
        fi
    fi
    
    if [[ "$COMMAND" == "apply-patch" && -z "$PATCH_SOURCE" ]]; then
        log_error "Patch source is required for apply-patch command"
        exit 1
    fi
}

# Function to initialize patch session
initialize_patch_session() {
    PATCH_SESSION_ID="PATCH_$(date +%Y%m%d_%H%M%S)_$$"
    PATCH_START_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    
    log_info "=== Oracle Patch Automation Session Started ==="
    log_info "Session ID: $PATCH_SESSION_ID"
    log_info "Script Version: $SCRIPT_VERSION"
    log_info "Command: $COMMAND"
    log_info "Patch ID: $PATCH_ID"
    log_info "Oracle SID: $ORACLE_SID"
    log_info "Oracle Home: $ORACLE_HOME"
    
    # Create session directories
    mkdir -p "$PATCH_LOGS_DIR"
    mkdir -p "$PATCH_STAGING_DIR"
    mkdir -p "$PATCH_BACKUP_DIR"
    
    log_audit "Patch automation session started: $PATCH_SESSION_ID"
}

# Function to execute apply-patch command
execute_apply_patch() {
    log_info "=== Executing Apply Patch Workflow ==="
    
    # Step 1: Pre-patch validation
    log_info "Step 1: Pre-patch validation"
    if ! run_pre_patch_validation; then
        if [[ "$FORCE_MODE" == "true" ]]; then
            log_warn "Pre-patch validation failed but continuing due to force mode"
        else
            log_error "Pre-patch validation failed - aborting patch application"
            OVERALL_STATUS="VALIDATION_FAILED"
            return 1
        fi
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "Dry run mode - stopping after validation"
        OVERALL_STATUS="DRY_RUN_SUCCESS"
        return 0
    fi
    
    # Step 2: Create backup
    log_info "Step 2: Creating pre-patch backup"
    if ! create_pre_patch_backup; then
        log_error "Pre-patch backup failed - aborting patch application"
        OVERALL_STATUS="BACKUP_FAILED"
        return 1
    fi
    
    # Store backup info for potential rollback
    ROLLBACK_INFO="BACKUP_ID:$(generate_backup_id)"
    
    # Step 3: Stage patch
    log_info "Step 3: Staging patch files"
    if ! stage_oracle_patch "$PATCH_SOURCE" "$PATCH_ID" "$EXPECTED_CHECKSUM"; then
        log_error "Patch staging failed - aborting patch application"
        OVERALL_STATUS="STAGING_FAILED"
        return 1
    fi
    
    # Step 4: Apply patch
    log_info "Step 4: Applying patch"
    if ! apply_oracle_patch "$STAGED_PATCH_DIR" "$PATCH_ID" "$PATCH_TYPE"; then
        log_error "Patch application failed - initiating rollback"
        OVERALL_STATUS="APPLICATION_FAILED"
        
        # Attempt automatic rollback
        if execute_rollback "$PATCH_ID" "$ROLLBACK_INFO" "Patch application failed"; then
            log_info "Automatic rollback completed successfully"
            OVERALL_STATUS="ROLLBACK_SUCCESS"
        else
            log_error "Automatic rollback failed - manual intervention required"
            OVERALL_STATUS="ROLLBACK_FAILED"
        fi
        return 1
    fi
    
    # Step 5: Post-patch validation
    log_info "Step 5: Post-patch health checks"
    if ! run_post_patch_validation "$PATCH_ID"; then
        log_error "Post-patch validation failed"
        OVERALL_STATUS="POST_VALIDATION_FAILED"
        
        # Consider rollback based on configuration
        if [[ "$AUTO_ROLLBACK_ON_FAILURE" == "true" ]]; then
            log_warn "Initiating rollback due to post-patch validation failure"
            if execute_rollback "$PATCH_ID" "$ROLLBACK_INFO" "Post-patch validation failed"; then
                OVERALL_STATUS="ROLLBACK_SUCCESS"
            else
                OVERALL_STATUS="ROLLBACK_FAILED"
            fi
            return 1
        else
            log_warn "Post-patch validation failed but auto-rollback is disabled"
            return 1
        fi
    fi
    
    # Success
    OVERALL_STATUS="SUCCESS"
    log_info "Patch application completed successfully"
    return 0
}

# Function to execute rollback command
execute_rollback() {
    log_info "=== Executing Rollback Workflow ==="
    
    if [[ $# -eq 3 ]]; then
        # Called from apply-patch workflow
        local patch_id="$1"
        local rollback_info="$2"
        local error_details="$3"
    else
        # Called as standalone command
        local patch_id="$PATCH_ID"
        local rollback_info=""
        local error_details="Manual rollback requested"
    fi
    
    if execute_rollback "$patch_id" "$rollback_info" "$error_details"; then
        log_info "Rollback completed successfully"
        return 0
    else
        log_error "Rollback failed"
        return 1
    fi
}

# Function to execute other commands
execute_command() {
    case "$COMMAND" in
        "validate")
            log_info "=== Executing Pre-patch Validation ==="
            run_pre_patch_validation
            ;;
        "backup")
            log_info "=== Executing Backup Creation ==="
            create_pre_patch_backup
            ;;
        "health-check")
            log_info "=== Executing Health Check ==="
            run_post_patch_validation "$PATCH_ID"
            ;;
        "status")
            log_info "=== System Status ==="
            show_system_status
            ;;
        "cleanup")
            log_info "=== Cleanup Operation ==="
            cleanup_old_logs
            cleanup_old_patches
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            return 1
            ;;
    esac
}

# Function to show system status
show_system_status() {
    echo "Oracle Database Patch Automation Status"
    echo "======================================="
    echo "Timestamp: $(date)"
    echo "Hostname: $(hostname)"
    echo "Oracle SID: $ORACLE_SID"
    echo "Oracle Home: $ORACLE_HOME"
    echo ""
    
    # Database status
    local db_status=$(sqlplus -s / as sysdba <<< "SELECT status FROM v\$instance;" 2>/dev/null | tail -1 || echo "N/A")
    echo "Database Status: $db_status"
    
    # Listener status
    local listener_services=$(lsnrctl status 2>/dev/null | grep -c "Service.*has.*instance" || echo "0")
    echo "Listener Services: $listener_services"
    
    # Applied patches
    echo ""
    echo "Recently Applied Patches:"
    opatch lspatches 2>/dev/null | head -5 || echo "Unable to retrieve patch information"
    
    # Disk space
    echo ""
    echo "Disk Space:"
    df -h "$ORACLE_HOME" "$PATCH_BASE_DIR" 2>/dev/null || echo "Unable to retrieve disk space information"
    
    # Recent logs
    echo ""
    echo "Recent Log Files:"
    find "$PATCH_LOGS_DIR" -name "*.log" -mtime -1 2>/dev/null | head -5 || echo "No recent log files found"
}

# Function to finalize patch session
finalize_patch_session() {
    PATCH_END_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    local duration=$(($(date -d "$PATCH_END_TIME" +%s) - $(date -d "$PATCH_START_TIME" +%s)))
    
    log_info "=== Oracle Patch Automation Session Completed ==="
    log_info "Session ID: $PATCH_SESSION_ID"
    log_info "Overall Status: $OVERALL_STATUS"
    log_info "Duration: ${duration} seconds"
    log_info "End Time: $PATCH_END_TIME"
    
    # Generate final report
    local report_file=$(generate_patch_report "$PATCH_ID" "$OVERALL_STATUS" "$PATCH_START_TIME" "$PATCH_END_TIME" "")
    
    # Send notification
    local subject="Oracle Patch Automation - $OVERALL_STATUS"
    local message="Patch automation session completed.\nSession ID: $PATCH_SESSION_ID\nStatus: $OVERALL_STATUS\nDuration: ${duration} seconds"
    
    send_notification "$subject" "$message" "$report_file"
    
    log_audit "Patch automation session completed: $PATCH_SESSION_ID with status: $OVERALL_STATUS"
    
    # Exit with appropriate code
    case "$OVERALL_STATUS" in
        "SUCCESS"|"DRY_RUN_SUCCESS")
            exit 0
            ;;
        *)
            exit 1
            ;;
    esac
}

# Main execution
main() {
    # Parse command line arguments
    parse_arguments "$@"
    
    # Initialize session
    initialize_patch_session
    
    # Execute command
    case "$COMMAND" in
        "apply-patch")
            execute_apply_patch
            ;;
        "rollback")
            execute_rollback
            ;;
        *)
            execute_command
            ;;
    esac
    
    # Finalize session
    finalize_patch_session
}

# Execute main function with all arguments
main "$@"
