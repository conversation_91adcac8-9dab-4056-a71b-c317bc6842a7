#!/bin/bash
# Oracle Database Patching - Post-Patch Health Checks
# ===================================================

# Source configuration and logging
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/patch_config.conf"
source "$SCRIPT_DIR/logging_utils.sh"

# Health check results
HEALTH_CHECK_ERRORS=0
HEALTH_CHECK_WARNINGS=0
HEALTH_CHECK_RESULTS=()

# Function to check database status and connectivity
check_database_connectivity() {
    log_info "Checking database connectivity and status..."
    
    # Check if database is open and accessible
    local db_status=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT status FROM v\$instance;
EXIT;
EOF
)
    
    db_status=$(echo "$db_status" | tr -d ' \n\r')
    log_info "Database status: $db_status"
    
    if [[ "$db_status" == "OPEN" ]]; then
        log_info "Database connectivity check: PASSED"
        HEALTH_CHECK_RESULTS+=("Database Status: OPEN - PASSED")
        return 0
    else
        log_error "Database is not in OPEN status: $db_status"
        ((HEALTH_CHECK_ERRORS++))
        HEALTH_CHECK_RESULTS+=("Database Status: $db_status - FAILED")
        return 1
    fi
}

# Function to check listener status
check_listener_status() {
    log_info "Checking Oracle listener status..."
    
    local listener_output=$(lsnrctl status 2>&1)
    local listener_status=$?
    
    if [[ $listener_status -eq 0 ]]; then
        local service_count=$(echo "$listener_output" | grep -c "Service.*has.*instance")
        log_info "Listener is running with $service_count registered services"
        HEALTH_CHECK_RESULTS+=("Listener Status: Running ($service_count services) - PASSED")
        
        # Check specific service registration
        if echo "$listener_output" | grep -q "$ORACLE_SID"; then
            log_info "Database service $ORACLE_SID is registered with listener"
            HEALTH_CHECK_RESULTS+=("Service Registration: $ORACLE_SID - PASSED")
        else
            log_warn "Database service $ORACLE_SID not found in listener registration"
            ((HEALTH_CHECK_WARNINGS++))
            HEALTH_CHECK_RESULTS+=("Service Registration: $ORACLE_SID - WARNING")
        fi
        
        return 0
    else
        log_error "Oracle listener is not running or not accessible"
        ((HEALTH_CHECK_ERRORS++))
        HEALTH_CHECK_RESULTS+=("Listener Status: Not Running - FAILED")
        return 1
    fi
}

# Function to validate applied patches
validate_applied_patches() {
    log_info "Validating applied patches..."
    
    # Check OPatch inventory
    local opatch_output=$(opatch lspatches 2>/dev/null)
    if [[ $? -eq 0 ]]; then
        log_info "OPatch inventory accessible"
        
        local patch_count=$(echo "$opatch_output" | grep -c "Patch.*applied on")
        log_info "Total patches applied: $patch_count"
        HEALTH_CHECK_RESULTS+=("OPatch Inventory: $patch_count patches - PASSED")
        
        # Log recent patches
        log_info "Recently applied patches:"
        echo "$opatch_output" | head -10 | while read -r line; do
            log_info "  $line"
        done
    else
        log_error "Unable to access OPatch inventory"
        ((HEALTH_CHECK_ERRORS++))
        HEALTH_CHECK_RESULTS+=("OPatch Inventory: Inaccessible - FAILED")
    fi
    
    # Check database registry for SQL patches
    local sql_patches=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT COUNT(*) FROM dba_registry_sqlpatch WHERE status = 'SUCCESS';
EXIT;
EOF
)
    
    sql_patches=$(echo "$sql_patches" | tr -d ' \n\r')
    log_info "Successful SQL patches in registry: $sql_patches"
    HEALTH_CHECK_RESULTS+=("SQL Patches: $sql_patches successful - PASSED")
    
    return 0
}

# Function to run data integrity checks
check_data_integrity() {
    log_info "Running data integrity checks..."
    
    # Check for invalid objects
    local invalid_objects=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT COUNT(*) FROM dba_objects WHERE status = 'INVALID';
EXIT;
EOF
)
    
    invalid_objects=$(echo "$invalid_objects" | tr -d ' \n\r')
    log_info "Invalid objects count: $invalid_objects"
    
    if [[ "$invalid_objects" -gt 0 ]]; then
        log_warn "Found $invalid_objects invalid objects"
        ((HEALTH_CHECK_WARNINGS++))
        HEALTH_CHECK_RESULTS+=("Invalid Objects: $invalid_objects found - WARNING")
        
        # Try to recompile invalid objects
        log_info "Attempting to recompile invalid objects..."
        local recompile_result=$(sqlplus -s / as sysdba << EOF
@$ORACLE_HOME/rdbms/admin/utlrp.sql
EXIT;
EOF
)
        
        # Check again after recompilation
        local invalid_after=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT COUNT(*) FROM dba_objects WHERE status = 'INVALID';
EXIT;
EOF
)
        
        invalid_after=$(echo "$invalid_after" | tr -d ' \n\r')
        log_info "Invalid objects after recompilation: $invalid_after"
        HEALTH_CHECK_RESULTS+=("Invalid Objects After Recompile: $invalid_after - INFO")
    else
        log_info "No invalid objects found"
        HEALTH_CHECK_RESULTS+=("Invalid Objects: None - PASSED")
    fi
    
    # Check tablespace usage
    local tablespace_check=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT tablespace_name, ROUND(used_percent, 2) 
FROM dba_tablespace_usage_metrics 
WHERE used_percent > 85;
EXIT;
EOF
)
    
    if [[ -n "$tablespace_check" && "$tablespace_check" != *"no rows selected"* ]]; then
        log_warn "Tablespaces with high usage detected:"
        echo "$tablespace_check" | while read -r line; do
            log_warn "  $line"
        done
        ((HEALTH_CHECK_WARNINGS++))
        HEALTH_CHECK_RESULTS+=("Tablespace Usage: High usage detected - WARNING")
    else
        log_info "All tablespaces within normal usage limits"
        HEALTH_CHECK_RESULTS+=("Tablespace Usage: Normal - PASSED")
    fi
    
    return 0
}

# Function to check scheduled jobs
check_scheduled_jobs() {
    log_info "Checking scheduled jobs status..."
    
    # Check DBMS_SCHEDULER jobs
    local scheduler_jobs=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT COUNT(*) FROM dba_scheduler_jobs WHERE enabled = 'TRUE';
EXIT;
EOF
)
    
    scheduler_jobs=$(echo "$scheduler_jobs" | tr -d ' \n\r')
    log_info "Enabled scheduler jobs: $scheduler_jobs"
    
    # Check for failed jobs in last 24 hours
    local failed_jobs=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT COUNT(*) FROM dba_scheduler_job_run_details 
WHERE log_date > SYSDATE - 1 AND status = 'FAILED';
EXIT;
EOF
)
    
    failed_jobs=$(echo "$failed_jobs" | tr -d ' \n\r')
    log_info "Failed jobs in last 24 hours: $failed_jobs"
    
    if [[ "$failed_jobs" -gt 0 ]]; then
        log_warn "Found $failed_jobs failed jobs in last 24 hours"
        ((HEALTH_CHECK_WARNINGS++))
        HEALTH_CHECK_RESULTS+=("Scheduled Jobs: $failed_jobs failures - WARNING")
    else
        log_info "No failed jobs in last 24 hours"
        HEALTH_CHECK_RESULTS+=("Scheduled Jobs: No failures - PASSED")
    fi
    
    return 0
}

# Function to check database performance metrics
check_performance_metrics() {
    log_info "Checking database performance metrics..."
    
    # Check wait events
    local top_wait_events=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT event, total_waits FROM v\$system_event 
WHERE total_waits > 0 ORDER BY total_waits DESC FETCH FIRST 5 ROWS ONLY;
EXIT;
EOF
)
    
    if [[ -n "$top_wait_events" ]]; then
        log_info "Top wait events:"
        echo "$top_wait_events" | while read -r line; do
            log_info "  $line"
        done
        HEALTH_CHECK_RESULTS+=("Wait Events: Captured - INFO")
    fi
    
    # Check SGA and PGA usage
    local memory_usage=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT 'SGA: ' || ROUND(value/1024/1024, 2) || ' MB' FROM v\$sga WHERE name = 'Fixed Size'
UNION ALL
SELECT 'PGA: ' || ROUND(value/1024/1024, 2) || ' MB' FROM v\$pgastat WHERE name = 'total PGA allocated';
EXIT;
EOF
)
    
    if [[ -n "$memory_usage" ]]; then
        log_info "Memory usage:"
        echo "$memory_usage" | while read -r line; do
            log_info "  $line"
        done
        HEALTH_CHECK_RESULTS+=("Memory Usage: Captured - INFO")
    fi
    
    return 0
}

# Function to test application connectivity
test_application_connectivity() {
    log_info "Testing application connectivity..."
    
    # Test basic SQL operations
    local sql_test=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT 'SQL Test: ' || SYSDATE FROM dual;
EXIT;
EOF
)
    
    if [[ -n "$sql_test" && "$sql_test" != *"ERROR"* ]]; then
        log_info "Basic SQL test successful: $sql_test"
        HEALTH_CHECK_RESULTS+=("SQL Connectivity: Working - PASSED")
    else
        log_error "Basic SQL test failed"
        ((HEALTH_CHECK_ERRORS++))
        HEALTH_CHECK_RESULTS+=("SQL Connectivity: Failed - FAILED")
    fi
    
    # Test TNS connectivity (if configured)
    if [[ -n "$DB_CONNECT_STRING" && "$DB_CONNECT_STRING" != "/ as sysdba" ]]; then
        local tns_test=$(sqlplus -s "$DB_CONNECT_STRING" << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT 'TNS Test: Connected' FROM dual;
EXIT;
EOF
)
        
        if [[ -n "$tns_test" && "$tns_test" != *"ERROR"* ]]; then
            log_info "TNS connectivity test successful"
            HEALTH_CHECK_RESULTS+=("TNS Connectivity: Working - PASSED")
        else
            log_warn "TNS connectivity test failed"
            ((HEALTH_CHECK_WARNINGS++))
            HEALTH_CHECK_RESULTS+=("TNS Connectivity: Failed - WARNING")
        fi
    fi
    
    return 0
}

# Function to generate health check report
generate_health_check_report() {
    local patch_id="$1"
    local report_file="$PATCH_LOGS_DIR/post_patch_health_report_${patch_id}_$(date +%Y%m%d).txt"
    
    cat > "$report_file" << EOF
Oracle Post-Patch Health Check Report
====================================

Patch ID: $patch_id
Timestamp: $(date)
Hostname: $(hostname)
Oracle SID: $ORACLE_SID
Oracle Home: $ORACLE_HOME

Health Check Summary:
- Errors: $HEALTH_CHECK_ERRORS
- Warnings: $HEALTH_CHECK_WARNINGS
- Total Checks: ${#HEALTH_CHECK_RESULTS[@]}

Detailed Results:
EOF
    
    for result in "${HEALTH_CHECK_RESULTS[@]}"; do
        echo "- $result" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF

System Information:
- Database Version: $(sqlplus -s / as sysdba <<< "SELECT banner FROM v\$version WHERE banner LIKE 'Oracle%';" 2>/dev/null | grep Oracle || echo "N/A")
- Database Status: $(sqlplus -s / as sysdba <<< "SELECT status FROM v\$instance;" 2>/dev/null | tail -1 || echo "N/A")
- Uptime: $(uptime)
- Disk Space: $(df -h $ORACLE_HOME | tail -1 | awk '{print $4 " available"}')

EOF
    
    log_info "Health check report generated: $report_file"
    echo "$report_file"
}

# Main post-patch validation function
run_post_patch_validation() {
    local patch_id="$1"
    
    log_info "=== Starting Post-Patch Health Checks ==="
    
    # Reset counters
    HEALTH_CHECK_ERRORS=0
    HEALTH_CHECK_WARNINGS=0
    HEALTH_CHECK_RESULTS=()
    
    # Run all health checks
    check_database_connectivity
    check_listener_status
    validate_applied_patches
    check_data_integrity
    check_scheduled_jobs
    check_performance_metrics
    test_application_connectivity
    
    # Generate report
    local report_file=$(generate_health_check_report "$patch_id")
    
    # Summary
    log_info "=== Post-Patch Health Check Summary ==="
    log_info "Errors: $HEALTH_CHECK_ERRORS"
    log_info "Warnings: $HEALTH_CHECK_WARNINGS"
    log_info "Report: $report_file"
    
    if [[ $HEALTH_CHECK_ERRORS -gt 0 ]]; then
        log_error "Post-patch validation FAILED with $HEALTH_CHECK_ERRORS errors"
        return 1
    elif [[ $HEALTH_CHECK_WARNINGS -gt 0 ]]; then
        log_warn "Post-patch validation PASSED with $HEALTH_CHECK_WARNINGS warnings"
        return 0
    else
        log_info "Post-patch validation PASSED successfully"
        return 0
    fi
}

# Export functions for use by main script
export -f run_post_patch_validation
