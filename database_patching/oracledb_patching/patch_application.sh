#!/bin/bash
# Oracle Database Patching - Patch Application
# ============================================

# Source configuration and logging
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/patch_config.conf"
source "$SCRIPT_DIR/logging_utils.sh"

# Patch application variables
PATCH_APPLICATION_START=""
PATCH_APPLICATION_END=""
APPLIED_PATCHES=()
ROLLBACK_INFO=""

# Function to create restore point before patching
create_restore_point() {
    local restore_point_name="PREPATCH_$(date +%Y%m%d_%H%M%S)"
    
    log_info "Creating restore point: $restore_point_name"
    
    local create_rp_sql="CREATE RESTORE POINT $restore_point_name GUARANTEE FLASHBACK DATABASE;"
    
    if execute_and_log "sqlplus -s / as sysdba <<< \"$create_rp_sql\"" "Create Restore Point"; then
        log_info "Restore point created successfully: $restore_point_name"
        ROLLBACK_INFO="RESTORE_POINT:$restore_point_name"
        return 0
    else
        log_warn "Failed to create restore point - continuing without it"
        return 1
    fi
}

# Function to stop Oracle services
stop_oracle_services() {
    log_info "Stopping Oracle services for patching..."
    
    # Stop listener first
    log_info "Stopping Oracle listener..."
    if execute_and_log "lsnrctl stop" "Stop Oracle Listener"; then
        log_info "Oracle listener stopped successfully"
    else
        log_warn "Failed to stop Oracle listener or already stopped"
    fi
    
    # Shutdown database
    log_info "Shutting down Oracle database..."
    local shutdown_sql="SHUTDOWN IMMEDIATE;"
    
    if execute_and_log "sqlplus -s / as sysdba <<< \"$shutdown_sql\"" "Database Shutdown"; then
        log_info "Oracle database shutdown successfully"
        return 0
    else
        log_error "Failed to shutdown Oracle database"
        return 1
    fi
}

# Function to start Oracle services
start_oracle_services() {
    log_info "Starting Oracle services after patching..."
    
    # Start database first
    log_info "Starting Oracle database..."
    local startup_sql="STARTUP;"
    
    if execute_and_log "sqlplus -s / as sysdba <<< \"$startup_sql\"" "Database Startup"; then
        log_info "Oracle database started successfully"
    else
        log_error "Failed to start Oracle database"
        return 1
    fi
    
    # Start listener
    log_info "Starting Oracle listener..."
    if execute_and_log "lsnrctl start" "Start Oracle Listener"; then
        log_info "Oracle listener started successfully"
        return 0
    else
        log_error "Failed to start Oracle listener"
        return 1
    fi
}

# Function to apply patch using OPatch
apply_patch_with_opatch() {
    local patch_dir="$1"
    local patch_id="$2"
    
    log_info "Applying patch using OPatch: $patch_id"
    log_info "Patch directory: $patch_dir"
    
    # Check if patch is already applied
    local existing_patches=$(opatch lspatches | grep -c "$patch_id" || echo "0")
    if [[ $existing_patches -gt 0 ]]; then
        log_warn "Patch $patch_id appears to already be applied"
        return 0
    fi
    
    # Apply the patch
    log_info "Executing OPatch apply command..."
    
    if execute_and_log "opatch apply '$patch_dir' -silent -force" "OPatch Apply"; then
        log_info "Patch applied successfully with OPatch"
        APPLIED_PATCHES+=("$patch_id")
        return 0
    else
        log_error "Failed to apply patch with OPatch"
        return 1
    fi
}

# Function to apply patch using DataPatch (for database patches)
apply_patch_with_datapatch() {
    local patch_id="$1"
    
    log_info "Applying database patch using DataPatch: $patch_id"
    
    # Start database in upgrade mode if needed
    log_info "Starting database for DataPatch application..."
    local startup_upgrade_sql="STARTUP UPGRADE;"
    
    if execute_and_log "sqlplus -s / as sysdba <<< \"$startup_upgrade_sql\"" "Database Startup Upgrade"; then
        log_info "Database started in upgrade mode"
    else
        log_warn "Failed to start in upgrade mode, trying normal startup"
        local startup_sql="STARTUP;"
        execute_and_log "sqlplus -s / as sysdba <<< \"$startup_sql\"" "Database Normal Startup"
    fi
    
    # Run DataPatch
    log_info "Executing DataPatch..."
    
    if execute_and_log "datapatch -verbose" "DataPatch Application"; then
        log_info "DataPatch completed successfully"
        
        # Restart database normally
        local shutdown_sql="SHUTDOWN IMMEDIATE;"
        local startup_sql="STARTUP;"
        
        execute_and_log "sqlplus -s / as sysdba <<< \"$shutdown_sql\"" "Database Shutdown"
        execute_and_log "sqlplus -s / as sysdba <<< \"$startup_sql\"" "Database Startup"
        
        return 0
    else
        log_error "DataPatch application failed"
        return 1
    fi
}

# Function to handle RAC patching
apply_patch_rac() {
    local patch_dir="$1"
    local patch_id="$2"
    
    log_info "Applying patch in RAC environment: $patch_id"
    
    if [[ "$RAC_ENABLED" != "true" ]]; then
        log_error "RAC patching requested but RAC not enabled in configuration"
        return 1
    fi
    
    # Convert comma-separated nodes to array
    IFS=',' read -ra RAC_NODE_ARRAY <<< "$RAC_NODES"
    
    log_info "RAC nodes configured: ${RAC_NODE_ARRAY[*]}"
    
    # Apply patch to each node
    for node in "${RAC_NODE_ARRAY[@]}"; do
        log_info "Applying patch to RAC node: $node"
        
        # This is a template - actual RAC patching requires more complex orchestration
        # You might need to use opatch auto or rolling patch procedures
        
        if execute_and_log "ssh $node 'cd $patch_dir && opatch apply -silent -force'" "RAC Node Patch - $node"; then
            log_info "Patch applied successfully to node: $node"
        else
            log_error "Failed to apply patch to node: $node"
            return 1
        fi
    done
    
    # Run DataPatch on one node (typically the first one)
    log_info "Running DataPatch on primary node: ${RAC_NODE_ARRAY[0]}"
    
    if execute_and_log "ssh ${RAC_NODE_ARRAY[0]} 'datapatch -verbose'" "RAC DataPatch"; then
        log_info "RAC DataPatch completed successfully"
        return 0
    else
        log_error "RAC DataPatch failed"
        return 1
    fi
}

# Function to verify patch application
verify_patch_application() {
    local patch_id="$1"
    
    log_info "Verifying patch application: $patch_id"
    
    # Check OPatch inventory
    local opatch_list=$(opatch lspatches)
    log_info "Current OPatch inventory:"
    echo "$opatch_list" | while read -r line; do
        log_info "  $line"
    done
    
    # Verify specific patch is listed
    if echo "$opatch_list" | grep -q "$patch_id"; then
        log_info "Patch $patch_id found in OPatch inventory"
    else
        log_error "Patch $patch_id NOT found in OPatch inventory"
        return 1
    fi
    
    # Check database registry (for database patches)
    log_info "Checking database registry for applied patches..."
    
    local registry_check=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT patch_id, status FROM dba_registry_sqlpatch WHERE patch_id = '$patch_id';
EXIT;
EOF
)
    
    if [[ -n "$registry_check" && "$registry_check" != *"no rows selected"* ]]; then
        log_info "Patch found in database registry: $registry_check"
        
        if echo "$registry_check" | grep -q "SUCCESS"; then
            log_info "Patch status in registry: SUCCESS"
            return 0
        else
            log_warn "Patch status in registry: $(echo "$registry_check" | awk '{print $2}')"
            return 1
        fi
    else
        log_warn "Patch not found in database registry (may be Oracle Home patch only)"
        return 0
    fi
}

# Function to handle patch conflicts
resolve_patch_conflicts() {
    local patch_dir="$1"
    
    log_info "Checking for patch conflicts..."
    
    # Check for conflicts using OPatch
    local conflict_check=$(opatch prereq CheckConflictAgainstOHWithDetail -ph "$patch_dir" 2>&1)
    
    if echo "$conflict_check" | grep -q "Conflicts/Supersets"; then
        log_warn "Patch conflicts detected:"
        echo "$conflict_check" | grep -A 10 "Conflicts/Supersets" | while read -r line; do
            log_warn "  $line"
        done
        
        # You might want to implement automatic conflict resolution here
        log_warn "Manual intervention may be required for patch conflicts"
        return 1
    else
        log_info "No patch conflicts detected"
        return 0
    fi
}

# Main patch application function
apply_oracle_patch() {
    local patch_dir="$1"
    local patch_id="$2"
    local patch_type="$3"  # Options: OPATCH, DATAPATCH, RAC
    
    log_info "=== Starting Oracle Patch Application ==="
    log_info "Patch ID: $patch_id"
    log_info "Patch Directory: $patch_dir"
    log_info "Patch Type: $patch_type"
    
    PATCH_APPLICATION_START=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Pre-application checks
    if ! resolve_patch_conflicts "$patch_dir"; then
        log_error "Patch conflicts detected - aborting application"
        return 1
    fi
    
    # Create restore point
    create_restore_point
    
    # Apply patch based on type
    case "$patch_type" in
        "OPATCH")
            # Standard OPatch application (requires database shutdown)
            if ! stop_oracle_services; then
                log_error "Failed to stop Oracle services"
                return 1
            fi
            
            if apply_patch_with_opatch "$patch_dir" "$patch_id"; then
                log_info "OPatch application successful"
            else
                log_error "OPatch application failed"
                start_oracle_services  # Try to restart services
                return 1
            fi
            
            if ! start_oracle_services; then
                log_error "Failed to restart Oracle services after patching"
                return 1
            fi
            ;;
            
        "DATAPATCH")
            # Database patch using DataPatch
            if apply_patch_with_datapatch "$patch_id"; then
                log_info "DataPatch application successful"
            else
                log_error "DataPatch application failed"
                return 1
            fi
            ;;
            
        "RAC")
            # RAC environment patching
            if apply_patch_rac "$patch_dir" "$patch_id"; then
                log_info "RAC patch application successful"
            else
                log_error "RAC patch application failed"
                return 1
            fi
            ;;
            
        *)
            log_error "Unknown patch type: $patch_type"
            return 1
            ;;
    esac
    
    # Verify patch application
    if verify_patch_application "$patch_id"; then
        PATCH_APPLICATION_END=$(date '+%Y-%m-%d %H:%M:%S')
        log_info "=== Patch Application Completed Successfully ==="
        log_audit "Oracle patch applied successfully: $patch_id"
        return 0
    else
        log_error "=== Patch Application Verification Failed ==="
        return 1
    fi
}

# Export functions for use by main script
export -f apply_oracle_patch
export -f stop_oracle_services
export -f start_oracle_services
