
DELIMITER //

DROP EVENT IF EXISTS AddAutoGeneratedUniqueKeyEvent //

CREATE EVENT AddAutoGeneratedUniqueK<PERSON>Event
ON SCHEDULE EVERY 1 MINUTE
DO
BEGIN
    DECLARE done INT DEFAULT 0;
    DECLARE db_name VARCHAR(64);
    DECLARE tbl_name VARCHAR(64);
    DECLARE cur CURSOR FOR
        SELECT t.table_schema,t.table_name 
		FROM information_schema.tables t INNER JOIN information_schema .columns c 
		on t.table_schema=c.table_schema and t.table_name=c.table_name
		where t.table_schema not in ('information_schema', 'mysql', 'sys', 'innodb', 'performance_schema', 'mysql_innodb_cluster_metadata')
		and t.table_type = 'BASE TABLE'
		GROUP BY t.table_schema,t.table_name 
		HAVING sum(if(column_key in ('PRI','UNI'), 1,0)) =0; 

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    O<PERSON><PERSON> cur;

    read_loop: LOOP
        FETCH cur INTO db_name, tbl_name;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- Call the procedure to add the auto-generated primary key
        CALL AddAutoGeneratedUniqueKey(db_name, tbl_name);
    END LOOP;

    CLOSE cur;
END //

DELIMITER ;

