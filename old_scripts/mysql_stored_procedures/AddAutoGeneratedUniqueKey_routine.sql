DELIMITER //

DROP PROCEDURE IF EXISTS AddAutoGeneratedUniqueKey //

CREATE PROCEDURE AddAutoGeneratedUniqueKey(IN db_name VARCHAR(64), IN tbl_name VARCHAR(64))
BEGIN
    DECLARE col_count INT;

    -- Check if the table already has a primary key or unique key
    SELECT COUNT(*)
    INTO col_count
    FROM information_schema.TABLE_CONSTRAINTS
    WHERE TABLE_SCHEMA = db_name
    AND TABLE_NAME = tbl_name
    AND (CONSTRAINT_TYPE = 'PRIMARY KEY' OR CONSTRAINT_TYPE = 'UNIQUE');

    IF col_count = 0 THEN
        -- Add auto-generated unique key if no primary key or unique key exists
        SET @alter_stmt = CONCAT('ALTER TABLE ', db_name, '.', tbl_name, ' ADD COLUMN my_row_id INT AUTO_INCREMENT UNIQUE KEY FIRST');
        PREPARE stmt FROM @alter_stmt;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END //

DELIMITER ;
