DROP PROCEDURE IF EXISTS Drop_MyRowID_Add_PK;
DELIMITER //

CREATE PROCEDURE Drop_MyRowID_Add_PK(
    IN dbName VARCHAR(255),
    IN tableName VARCHAR(255),
    IN newPrimaryKeyColumnDef VARCHAR(255)
)
BEGIN

    -- Set a default value for newPrimaryKeyColumnDef if it is empty
    IF newPrimaryKeyColumnDef = '' THEN
        SET newPrimaryKeyColumnDef = 'id INT NOT NULL AUTO_INCREMENT PRIMARY KEY FIRST';
        SELECT 'id INT NOT NULL AUTO_INCREMENT PRIMARY KEY FIRST' as 'Default value set for newPrimaryKeyColumnDef:';

    END IF;

    -- Verify if the table already has my_row_id column

    SET @sqlCheckPK = CONCAT(
        'SELECT COUNT(*) INTO @pkCount
        FROM information_schema.KEY_COLUMN_USAGE k JOIN information_schema.TABLE_CONSTRAINTS c ON
        k.CONSTRAINT_NAME = c.CONSTRAINT_NAME AND k.TABLE_SCHEMA = c.TABLE_SCHEMA AND k.TABLE_NAME = c.TABLE_NAME
        WHERE k.TABLE_SCHEMA = ''', dbName, ''' AND k.TABLE_NAME = ''', tableName, ''' AND c.CONSTRAINT_TYPE IN ( ''PRIMARY KEY'', ''UNIQUE'' )' , ' AND k.COLUMN_NAME= ''my_row_id'' ;'
        );

    PREPARE stmt FROM @sqlCheckPK;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- If the table does not have my_row_id column, raise an error
    IF @pkCount = 0 THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'The specified table does not have an existing my_row_id column.';
    END IF;

    -- Check if the table has a unique key on my_row_id
    SET @sqlCheckMyRowIDUniqueKey = CONCAT(
        'SELECT COUNT(*) INTO @myRowIDUniqueKeyCount FROM information_schema.KEY_COLUMN_USAGE ',
        'WHERE TABLE_SCHEMA = ''', dbName, ''' AND TABLE_NAME = ''', tableName, ''' AND CONSTRAINT_NAME IN ( ''my_row_id'' )', ' AND COLUMN_NAME= ''my_row_id'' ;'
    );
    PREPARE stmt FROM @sqlCheckMyRowIDUniqueKey;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- If my_row_id is a unique key, prepare SQL to drop my_row_id column and add PK provided by user
    IF @myRowIDUniqueKeyCount > 0 THEN
        SET @sql_to_run = CONCAT('ALTER TABLE ', tableName, ' DROP my_row_id, ', ' ADD ', newPrimaryKeyColumnDef, ';');
        PREPARE stmt FROM @sql_to_run;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;

    -- Check if the table has a primary key on my_row_id
    SET @sqlCheckMyRowIDPrimaryKey = CONCAT(
        'SELECT COUNT(*) INTO @myRowIDPrimaryKeyCount FROM information_schema.KEY_COLUMN_USAGE ',
        'WHERE TABLE_SCHEMA = ''', dbName, ''' AND TABLE_NAME = ''', tableName, ''' AND CONSTRAINT_NAME IN ( ''PRIMARY'' )', ' AND COLUMN_NAME= ''my_row_id'' ;'
    );
    PREPARE stmt FROM @sqlCheckMyRowIDPrimaryKey;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;


    -- If my_row_id is a primary key, prepare SQL to drop primary key , drop my_row_id column and add PK provided by user
    IF @myRowIDPrimaryKeyCount > 0 THEN
        SET @sql_to_run = CONCAT('ALTER TABLE ', tableName, ' DROP my_row_id, ', 'DROP PRIMARY KEY, ' ,' ADD ', newPrimaryKeyColumnDef, ';');
        PREPARE stmt FROM @sql_to_run;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;

END //

DELIMITER ;
