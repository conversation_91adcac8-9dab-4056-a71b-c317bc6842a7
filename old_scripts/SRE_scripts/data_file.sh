#!/bin/bash

SIDS=$(ps -ef | grep pmon | grep -v grep | grep -v sed | awk '{print $8}' | sed 's/ora_pmon_//')
for SID in $SIDS; do
    export ORACLE_SID=$SID
    echo "Connecting to database with SID: $SID"
    files=$(sqlplus -S / as sysdba << EOF
    set heading off;
    set feedback off;
    set pagesize 0;
    select file_name from dba_data_files;
    exit;
EOF
    )

    for file in $files; do
        SID_UPPER=$(echo "${SID^^}")
        SID_LOWER=$(echo "${SID,,}")
        if [[ $file = /u02/app/oracle/oradata_${SID_UPPER}* ]] || [[ $file = /u02/app/oracle/oradata_${SID_LOWER}* ]]|| [[ $file = /u02/app/oracle/oradata/data/${SID_UPPER}* ]] || [[ $file = /u02/app/oracle/oradata/data/${SID_LOWER}* ]]; then
        :
        else echo "DATAFILE $file is not at the expected path for SID $SID"
        fi
    done
done