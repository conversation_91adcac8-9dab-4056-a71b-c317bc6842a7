import requests
from tabulate import tabulate

def api_response(customer_name, token):
    api_formats = [f"https://{customer_name}.api.tessell.com",f"https://api.{customer_name}.tessell.com"]
    headers = {"Authorization": f"{token}","Content-Type": "application/json"}

    for i in api_formats:
        svc_url = f"{i}/services?page-size=1000"
        am_url = f"{i}/availability-machines?page-size=1000"

        try:
            response = requests.get(svc_url, headers=headers)
            response1 = requests.get(am_url, headers=headers)

            if response.status_code == 200 and response1.status_code == 200:
                data = response.json()
                data1 = response1.json()
                break
            else:
                raise Exception(f"Error in format: {i}")
        except requests.exceptions.RequestException as e:
            continue
    else:
        print("Failed to fetch data")
        return

    table_data = []

    for i in range(len(data["response"])):
        svc_id = data["response"][i]["id"]
        svc_name = data["response"][i]["name"]
        server_name = data["response"][i]["instances"][0]["computeName"]
        sla = data1["response"][i]["rpoSla"]["sla"]
        table_data.append([svc_id, svc_name, server_name, sla])

    headers = ["SERVICE_ID", "SERVICE_NAME", "SERVER_NAME", "SLA"]
    print(tabulate(table_data, headers=headers, tablefmt="plain"))

customer_name = input("Enter customer name: ")
token = input("Enter your authorization token: ")

api_response(customer_name, token)