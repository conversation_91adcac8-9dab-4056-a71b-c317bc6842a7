import json

def convert_dynamodb_to_json(dynamodb_json):
    json_data = {}
    for key, value in dynamodb_json.items():
        if isinstance(value, dict) and 'S' in value:
            json_data[key] = value['S']
        elif isinstance(value, dict) and 'N' in value:
            json_data[key] = float(value['N'])
        elif isinstance(value, dict) and 'BOOL' in value:
            json_data[key] = bool(value['BOOL'])
        elif isinstance(value, dict) and 'M' in value:
            json_data[key] = convert_dynamodb_to_json(value['M'])
        elif isinstance(value, list) and len(value) > 0:
            json_data[key] = [convert_dynamodb_to_json(item) for item in value]
        else:
            json_data[key] = value
    return json_data

# Example DynamoDB JSON
dynamodb_json = {
    'id': {'S': '123'},
    'name': {'S': 'John'},
    'age': {'N': '30'},
    'isStudent': {'BOOL': True},
    'address': {'M': {'city': {'S': 'New York'}, 'zip': {'N': '10001'}}},
    'interests': {'L': [{'S': 'programming'}, {'S': 'reading'}, {'S': 'hiking'}]}
}

# Convert DynamoDB JSON to normal JSON
normal_json = convert_dynamodb_to_json(dynamodb_json)

# Print the result
print(json.dumps(normal_json, indent=4))
