import requests,base64,sys,json,logging,copy,os,subprocess
from tessell.plugin.database.oracle.tasks.discover.DiscoverOracleDB import DiscoverDatabase
from tessell.plugin.database.oracle.tasks.connection.OracleConnection import OracleConnection

logging.getLogger().addHandler(logging.StreamHandler(sys.stdout))
logging.getLogger().setLevel(logging.INFO)

LOG_SWEEP_CONFIG_FILE = "/opt/tessell-base/.tessell/log_sweep/logSweepConfig.json"
tenant_id=service_id=service_instance_id=""

def read_log_sweep_config():
    f = open(LOG_SWEEP_CONFIG_FILE,)
    logSweepConfig = json.load(f)
    logSweepConfig = logSweepConfig["log_sweep_config"]
    services = logSweepConfig["services"] if 'services' in logSweepConfig else None
    if services:
        oracle_home = services[service_id]['softwareHome']
        oracle_port = services[service_id]['service_port']
    else:
        oracle_home = logSweepConfig['softwareHome']
        oracle_port = logSweepConfig['service_port']
    tenant_id = logSweepConfig['tenantId']
    return oracle_home,oracle_port,tenant_id

def get_metadata(get_endpoint, service="tessell-database-system"):
    modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}
    # Get data from Tessell Agent (/rpc endpoint)
    rpc_data = {
        "rpcName": "get_data",
        "service": service,
        "port": 8080,
        "method": "GET",
        "endpoint": get_endpoint,
        "headers": modified_headers,
        "timeout": 900,
    }

    response_from_request = requests.post(
        "http://localhost:8081/rpc",
        data=json.dumps(rpc_data),
        headers = {"Content-type": "application/json"},
        verify=False,
        timeout=900,
    )

    if response_from_request.status_code:
        print("get metadata response status code: " + str(response_from_request.status_code))

    response_json = json.loads(response_from_request.content.decode())
    response_json = base64.b64decode(response_json['payload']).decode('utf-8')
    response_json = json.loads(response_json)
    print(f"response_json is {json.dumps(response_json)}")
    return response_json

def update_metadata(update_endpoint, data, request="POST"):
    data = json.dumps(data, ensure_ascii=False)
    data = data.encode()
    modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}
    rpc_data = {
        "rpcName": "update_tessell_service",
        "service": "tessell-database-system",
        "port": 8080,
        "method": request,
        "endpoint": update_endpoint,
        "payload": base64.b64encode(data).decode("utf-8"),
        "headers": modified_headers,
        "timeout": 900,
    }

    response_from_request = requests.post(
        "http://localhost:8081/rpc",
        data=json.dumps(rpc_data),
        headers = {"Content-type": "application/json"},
        verify=False,
        timeout=900,
    )

    if response_from_request.status_code:
        print(f"update metadata response status code: " +str(response_from_request.status_code))
    return response_from_request

def update_service(database_info_json, old_oracle_sid):
    get_endpoint = f"/tessell-ops/services/{service_id}"
    print(f"Getting service info ...")
    db_service = get_metadata(get_endpoint)
    service_connect_descriptor = db_service['connectivityInfo']['userView']['connectStrings'][0]['connectDescriptor']
    db_service['connectivityInfo']['userView']['connectStrings'][0]['connectDescriptor'] = service_connect_descriptor.replace(old_oracle_sid,database_info_json['oracle_sid'])
    update_endpoint=f"/tessell-ops/services/{service_id}"
    print(f"Updating service info with {db_service}...")
    update_metadata(update_endpoint, db_service, request="PATCH")
    
def update_service_instance(database_info_json, old_oracle_sid):
    get_endpoint = f"/tessell-ops/services/{service_id}/service-instances/{service_instance_id}"
    print(f"Getting service instance info ...")
    dbservice_instance = get_metadata(get_endpoint)
    service_instance_connect_descriptor = dbservice_instance['connectionInfo']['connectString']['connectDescriptor']
    dbservice_instance['connectionInfo']['connectString']['connectDescriptor'] = service_instance_connect_descriptor.replace(old_oracle_sid,database_info_json['oracle_sid'])
    update_endpoint=f"/tessell-ops/services/{service_id}/service-instances/{service_instance_id}"
    print(f"Updating service instance info with {dbservice_instance}...")
    update_metadata(update_endpoint, dbservice_instance, request="PATCH")
    
def update_service_instance_metadata(database_info_json):
    get_endpoint = f"/tessell-ops/services/{service_id}/service-instances/{service_instance_id}/metadata/version/latest"
    print(f"Getting service instance metadata info ...")
    service_instance_metadata_json = get_metadata(get_endpoint)
    service_instance_metadata_json['metadata']['data']['database_parameters']['database_metadata'] = database_info_json['database_metadata']
    service_instance_metadata_json['metadata']['data']['database_parameters']['db_size'] = database_info_json['db_size']
    service_instance_metadata_json['metadata']['data']['database_parameters']['oracle_sid'] = database_info_json['oracle_sid']
    service_instance_metadata_json['version'] = service_instance_metadata_json['version']+1
    update_endpoint=f"/tessell-ops/services/{service_id}/service-instances/{service_instance_id}/metadata"
    print(f"Updating service instance metadata with {json.dumps(service_instance_metadata_json)}...")
    update_metadata(update_endpoint, service_instance_metadata_json)
    
def update_databases(database_info_json):
    if database_info_json['database_metadata']['database_parameters']['basic_info']['is_cdb']:
        #update all pdbs to tessell_databases
        get_endpoint = f"/tessell-ops/services/{service_id}/databases"
        print(f"Getting databases info ...")
        dbservice_databases = get_metadata(get_endpoint)['response']
        pdb_base = dbservice_databases[0]
        existing_pdb_list={}
        existing_pdb_set = set()
        vm_pdb_set = set()
        for pdb in dbservice_databases:
            if pdb['status'] not in ['CREATING','DELETING','DELETED']:
                existing_pdb_list[pdb['name']] = pdb
                existing_pdb_set.update((pdb['name'].lower(),))
        for pdb in database_info_json['database_metadata']['database_parameters']['cdb_info']['pdb_list']:
            if '$' not in pdb['name']:
                vm_pdb_set.update((pdb['name'].lower(),))
        create_pdbs = set()
        delete_pdbs = set()
        create_pdbs = vm_pdb_set - existing_pdb_set
        delete_pdbs = existing_pdb_set - vm_pdb_set
        for delete_pdb in delete_pdbs:
            delete_endpoint=f"/tessell-ops/services/{service_id}/databases/{existing_pdb_list[delete_pdb]['id']}"
            print(f"Deleting PDB {delete_pdb}...")
            delete_metadata(delete_endpoint)

        for create_pdb in create_pdbs:
            new_pdb = copy.deepcopy(pdb_base)
            new_pdb.pop('id')
            new_pdb['name'] = create_pdb
            new_pdb['status'] = 'READY'
            connect_desc = database_info_json['database_metadata']['database_parameters']['db_connect_params']['pdb_connect_desc']
            connect_desc = connect_desc.replace("PDB"+database_info_json['oracle_sid'],pdb['name'])
            new_pdb['driverInfo']['connectString']['connectDescriptor'] = connect_desc
            update_endpoint=f"/tessell-ops/services/{service_id}/databases"
            print(f"Adding new PDB with data {new_pdb}...")
            update_metadata(update_endpoint, new_pdb, request="POST")                        
    else:
        get_endpoint = f"/tessell-ops/services/{service_id}/databases"
        print(f"Getting databases info ...")
        dbservice_databases = get_metadata(get_endpoint)['response']
        dbservice_databases[0]['name'] = database_info_json['oracle_sid']
        update_endpoint=f"/tessell-ops/services/{service_id}/databases/{dbservice_databases[0]['id']}"
        print(f"Updating service database info with {dbservice_databases[0]}...")
        update_metadata(update_endpoint, dbservice_databases[0],request="PATCH")
    
def update_logsweep(database_info_json):
    print("Updating SID in logsweep config file")
    f = open(LOG_SWEEP_CONFIG_FILE,)
    logSweepConfig = json.load(f)
    f.close()
    services = logSweepConfig["log_sweep_config"]["services"] if 'services' in logSweepConfig["log_sweep_config"] else None
    if services:
        logSweepConfig["log_sweep_config"]['services'][service_id]['dbName'] = database_info_json['oracle_sid']
    else:
        logSweepConfig["log_sweep_config"]['dbName'] = database_info_json['oracle_sid']
    print(logSweepConfig)        
    with open(LOG_SWEEP_CONFIG_FILE, "w") as jsonFile:
        json.dump(logSweepConfig, jsonFile)
    jsonFile.close()

def update_db_exporter(old_oracle_sid, database_info_json):
    print("Updating SID & DBSNMP password in DB Exporter config file")    
    with open('/opt/tessell/db-exporter/sql_exporter.yml') as f:
        lines = f.read().splitlines()
    newfilecontents = []
    for line in lines:
        if service_instance_id in line:
            # line = re.sub("dbsnmp:*([\S]+)@localhost", 'dbsnmp:'+dbsnmp_user_pass+'@localhost', line)
            line = line.replace(old_oracle_sid,database_info_json['oracle_sid'])
        newfilecontents.append(line)
    print(newfilecontents)
    cmd=f'sudo mv /opt/tessell/db-exporter/sql_exporter.yml /opt/tessell/db-exporter/sql_exporter.yml.bkp'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)    
    for line in newfilecontents:
        cmd=f'sudo echo "{line}" | sudo tee -a /opt/tessell/db-exporter/sql_exporter.yml'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    print("Please restart db-exporter for configuration change to take effect...")
    
def delete_metadata(update_endpoint):
    modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}
    rpc_data = {
        "rpcName": "update_tessell_service",
        "service": "tessell-database-system",
        "port": 8080,
        "method": "DELETE",
        "endpoint": update_endpoint,
        "payload": None,
        "headers": modified_headers,
        "timeout": 900,
    }

    response_from_request = requests.post(
        "http://localhost:8081/rpc",
        data=json.dumps(rpc_data),
        headers = {"Content-type": "application/json"},
        verify=False,
        timeout=900,
    )

    if response_from_request.status_code:
        print(f"update metadata response status code: " +str(response_from_request.status_code))
    return response_from_request

def update_dbsnmp_password(database_info_json,dbsnmp_user_pass):
    print("Resetting DBSNMP password in database")
    query = f"alter user dbsnmp identified by \"{dbsnmp_user_pass}\" account unlock"
    oracle_db_connection = OracleConnection(connect_params=database_info_json['database_metadata']['database_parameters']['db_connect_params'])
    oracle_db_connection.execute(query, fetch_res=False)
    
if __name__ == "__main__":
    service_id = sys.argv[1]
    service_instance_id = sys.argv[2]
    host_endpoint = sys.argv[3]         #--- put db home page -> overview -> host value here
    monitor_endpoint = sys.argv[4]      #--- put db home page -> service instance -> connection endpoint -> host value here
    master_user = sys.argv[5]           #--- put db home page -> overview -> Username value here
    ssl_enabled = sys.argv[6]
    old_oracle_sid = sys.argv[7]
    new_oracle_sid = sys.argv[8]
    oracle_sid = new_oracle_sid

    oracle_home,oracle_port,tenant_id = read_log_sweep_config()

    get_endpoint = f"/tessell-ops/secret-store/secret?secret-type=TENANT_ASSET&key-name=db--oracle--dbsnmpPwd--"+service_id
    dbsnmp_user_secret = get_metadata(get_endpoint,service="tessell-security")
    dbsnmp_user_pass = dbsnmp_user_secret['secret']['value']

    discover_db = DiscoverDatabase()
    kwargs = {}
    kwargs['oracle_home'] = oracle_home
    kwargs['oracle_sid'] = oracle_sid
    kwargs['db_user'] = 'sys'
    kwargs['oracle_user'] = 'oracle'
    kwargs['sysdba'] = 'true'
    kwargs['listener_port'] = str(oracle_port)
    kwargs['host_ip'] = host_endpoint
    kwargs['monitor_endpoint'] = monitor_endpoint
    kwargs['connect_user'] = master_user
    kwargs['ssl_enabled'] = True if ssl_enabled == 'True' else False
    if ssl_enabled:
        kwargs['non_ssl_monitor_port'] = '1599'
    kwargs['connect_pass'] = dbsnmp_user_pass
    kwargs['work_directory'] = "/tmp/tessell-logs/discover"
    database_info_json = discover_db.execute(**kwargs)
    print(f"json extracted from database is {json.dumps(database_info_json)}...")

    update_dbsnmp_password(database_info_json,dbsnmp_user_pass)
    update_service(database_info_json, old_oracle_sid)
    update_service_instance(database_info_json, old_oracle_sid)
    update_service_instance_metadata(database_info_json)
    update_databases(database_info_json)
    update_logsweep(database_info_json)
    update_db_exporter(old_oracle_sid, database_info_json)
