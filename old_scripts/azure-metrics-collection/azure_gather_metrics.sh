#!/bin/bash

########################
#Author: <PERSON><PERSON><PERSON>
#Purpose: This script collects Azure monitoring metrics for Azure VMs.
#Usage : ./azure_gather_metrics.sh "Subscription Id" "Resource Group Name"  "<Space seperated VM Names>"
          #Eg: ./azure_gather_metrics.sh "ddasxxxxxxxxxxxxxxx" "test-rg" "vm1 vm2 vm3"

#######################
subscription=$1
resource_group=$2
vmlist=$3

#Crete or Replace directory for storing metrics data
rm -r azure_metrics; mkdir azure_metrics

for i in $vmlist
do 

vm_name=`echo ${3} | awk -F "/" '{print $(NF)}'`
echo "Processing for Resource: $vm_name"

resource_id="/subscriptions/${subscription}/resourceGroups/${2}/providers/Microsoft.Compute/virtualMachines/${i}"
echo "Resource Id is: $resource_id"

`az monitor metrics list --resource ${resource_id} --metrics "Percentage CPU"  "Available Memory Bytes" "Disk Read Bytes" "Disk Write Bytes" "Network In Total" "Network Out Total" "VmAvailabilityMetric" "VM Cached IOPS Consumed Percentage" "VM Cached Bandwidth Consumed Percentage" "VM Uncached IOPS Consumed Percentage" "VM Uncached Bandwidth Consumed Percentage" --interval PT5M --offset 15d --aggregation Average Minimum Maximum Total > azure_metrics/${vm_name}.json`

done

timestamp=`date "+%Y%m%d%H%M%S"`
zip -r azure_metrics${timestamp}.zip azure_metrics