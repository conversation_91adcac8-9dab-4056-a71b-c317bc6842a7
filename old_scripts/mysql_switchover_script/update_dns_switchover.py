from tessell.clouds.aws.AWSConnection import AWSConnection
from tessell.clouds.aws.handlers.NetworkHandler import NetworkHandler
from tessell.plugin.database.mysql.tasks.utils.MySQLTaskUtils import MySQLTaskUtils
from tessell.plugin.database.mysql import constants as mysql_constants
import json, os, sys

LOG_SWEEP_CONFIG_FILE = "/opt/tessell-base/.tessell/log_sweep/logSweepConfig.json"


class UpdateDNS:
    def __init__(self):
        self.read_log_config()
        self.is_ha_innodb_cluster = os.path.exists(mysql_constants.IS_HA_INNODB_CLUSTER)
        self.is_innodb_cluster_set = os.path.exists(mysql_constants.IS_HA_INNODB_CLUSTER_SET)
        self.taskUtils = MySQLTaskUtils()

    def read_log_config(self):
        f = open(
            LOG_SWEEP_CONFIG_FILE,
        )
        self._logSweepConfig = json.load(f)
        self._logSweepConfig = self._logSweepConfig["log_sweep_config"]
        f.close()

    def update_cluster_dns(self, commit_update=False):
        # Update Cluster DNS with the public IP
        self._awsConn = AWSConnection(
            aws_access_key=self._logSweepConfig["infraCloudInfo"]["awsAccessKeyId"],
            aws_secret_key=self._logSweepConfig["infraCloudInfo"]["awsSecretAccessKey"],
            region_name="ap-south-1",
            az_name="ap-south-1a",
            assume_role_urn=None,
        )
        networkHandler = NetworkHandler(self._awsConn)
        public_ip = self.taskUtils.get_tessell_compute_ip(self._logSweepConfig["computeResourceId"],
                                                          self._logSweepConfig["tenantId"])
        print(f"Public IP: {public_ip},\n ")
        print(f"Cluster FQDN: {self._logSweepConfig['clusterDNSInfo']['clusterFqdn']},\n "
              f"Cluster Domain: {self._logSweepConfig['clusterDNSInfo']['clusterDomain']}")
        if not commit_update:
            print("Dry run, not updating DNS")
            return
        networkHandler.update_host_dns_record(
            public_ip,
            self._logSweepConfig["clusterDNSInfo"]["clusterFqdn"],
            self._logSweepConfig["clusterDNSInfo"]["clusterDomain"],
            5,
        )
        print("Updated Cluster DNS with the public IP")

if __name__ == "__main__":
    update_dns = UpdateDNS()
    commit_update = False
    if len(sys.argv) > 1:
        if sys.argv[1] == "commit_update":
            print("this is final run")
            commit_update = True

    update_dns.update_cluster_dns(commit_update=commit_update)
    # check with argument 2 is present to try dry run
