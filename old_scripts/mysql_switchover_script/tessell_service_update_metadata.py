import json, base64, requests, sys

LOG_SWEEP_CONFIG_FILE = "/opt/tessell-base/.tessell/log_sweep/logSweepConfig.json"


class UpdateServiceInfo:
    def __init__(self, service_id):
        self.service_id = service_id
        self.read_log_config()

    def read_log_config(self):
        f = open(
            LOG_SWEEP_CONFIG_FILE,
        )
        self._logSweepConfig = json.load(f)
        self._logSweepConfig = self._logSweepConfig["log_sweep_config"]
        f.close()

    def get_tessell_service_instances(self, serviceId):
        modified_headers = {
            "Content-Type": "application/json",
            "tenant-id": self._logSweepConfig["tenantId"],
        }
        get_endpoint = "/tessell-ops/services/" + serviceId + "/service-instances"
        # Get data from Tessell Agent (/rpc endpoint)
        rpc_data = {
            "rpcName": "get_tessell_service_instance",
            "service": "tessell-database-system",
            "port": 8080,
            "method": "GET",
            "endpoint": get_endpoint,
            "headers": modified_headers,
            "timeout": 900,
        }

        response_from_request = requests.post(
            "http://localhost:8081/rpc",
            data=json.dumps(rpc_data),
            headers={"Content-type": "application/json"},
            verify=False,
            timeout=900,
        )

        if response_from_request.status_code:
            print("response status code: " + str(response_from_request.status_code))

        service_instances_response = json.loads(response_from_request.content.decode())
        service_instances_response = base64.b64decode(
            service_instances_response["payload"]
        ).decode("utf-8")
        service_instances_response = json.loads(service_instances_response)
        service_instances = service_instances_response["response"]
        return service_instances



    def update_tessell_service_instance(
            self, serviceId, serviceInstanceId, service_instance
    ):
        data = json.dumps(service_instance, ensure_ascii=False)
        data = data.encode()
        modified_headers = {
            "Content-Type": "application/json",
            "tenant-id": self._logSweepConfig["tenantId"],
        }
        update_endpoint = (
                "/tessell-ops/services/"
                + serviceId
                + "/service-instances/"
                + serviceInstanceId
        )
        # Send the data to Tessell Agent (/rpc endpoint)
        rpc_data = {
            "rpcName": "update_tessell_service_instance",
            "service": "tessell-database-system",
            "port": 8080,
            "method": "PATCH",
            "endpoint": update_endpoint,
            "payload": base64.b64encode(data).decode("utf-8"),
            "headers": modified_headers,
            "timeout": 900,
        }

        response_from_request = requests.post(
            "http://localhost:8081/rpc",
            data=json.dumps(rpc_data),
            headers={"Content-type": "application/json"},
            verify=False,
            timeout=900,
        )

        if response_from_request.status_code:
            print(
                "response status code: " + str(response_from_request.status_code)
            )
        return response_from_request

    def update_tessell_service_instances(self,service_id,service_instances, commit_update):
        updated_instance = []
        for instance in service_instances:
            if instance['status'].upper() == "UP" and instance['computeResourceId'] not in updated_instance:
                updated_instance.append(instance['computeResourceId'])
                if instance["role"] == "primary":
                    instance["role"] = "dr"
                    instance["type"] = "DR"
                elif instance["role"] == "dr":
                    instance["role"] = "primary"
                    instance["type"] = "DC"
                print(f"We are changing the instance metadata to {instance}")
                if commit_update:
                    print("Performing update.")
                    self.update_tessell_service_instance(service_id,instance['id'],instance)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Please provide service id as argument 1")
        sys.exit(1)
    commit_update = False
    if len(sys.argv) > 2:
        if sys.argv[2] == "commit_update":
            print("this is final run")
            commit_update = True
    service_id = sys.argv[1]
    update_service_info = UpdateServiceInfo(service_id)
    service_instances = update_service_info.get_tessell_service_instances(service_id)
    update_service_info.update_tessell_service_instances(service_id,service_instances,commit_update)
    # print service_instances in proper format
    print(json.dumps(service_instances, indent=4, sort_keys=True))

