#!/bin/bash
# Install Blobfuse2
sudo rpm -Uvh https://packages.microsoft.com/config/rhel/8/packages-microsoft-prod.rpm
sudo yum install blobfuse2 -y

# Create directory for mounting
sudo mkdir /rmanbackup

# Create Blobfuse configuration file in oracle's home directory
sudo bash -c "cat <<EOF > /home/<USER>/azure.yaml
logging:
  type: syslog
  level: log_debug
components:
  - libfuse
  - stream
  - azstorage
libfuse:
  attribute-expiration-sec: 120
  entry-expiration-sec: 120
  negative-entry-expiration-sec: 240
  direct-io: true
stream:
  block-size-mb: 2
  max-buffers: 8
  buffer-size-mb: 4
azstorage:
  type: block
  account-name: $1
  account-key: $2
  endpoint: https://$1.blob.core.windows.net
  mode: key
  container: $3
EOF"

# Change ownership of /rmanbackup directory to oracle user
sudo chown -R oracle:oinstall /rmanbackup

# Mount Blobfuse as oracle user
sudo -u oracle blobfuse2 mount /rmanbackup --config-file=/home/<USER>/azure.yaml

# Switch to oracle user and change directory to /rmanbackup
sudo -u oracle bash <<EOF
cd /rmanbackup
EOF