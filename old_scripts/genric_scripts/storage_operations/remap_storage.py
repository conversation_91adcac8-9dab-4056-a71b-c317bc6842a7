import base64, json, requests, pprint, sys, copy

LOG_SWEEP_CONFIG_FILE = "/opt/tessell-base/.tessell/log_sweep/logSweepConfig.json"
tenant_id = service_id = ""
rpc_url = "http://localhost:8081/rpc"
rpc_port = 8080
page_size = 100
engine_type = ""


def do_pprint(data):
    pprint.pprint(data)
    print("\n\n")


def read_log_sweep_config():
    f = open(LOG_SWEEP_CONFIG_FILE, )
    logSweepConfig = json.load(f)
    logSweepConfig = logSweepConfig["log_sweep_config"]
    if 'serviceId' in logSweepConfig:
        service_id = logSweepConfig["serviceId"]
    else:
        print("Failed to get services from logSweepConfig")
        exit(1)
    tenant_id = logSweepConfig['tenantId']
    return service_id, tenant_id


class RPCRequest:

    def __init__(self, service_id, tenant_id):
        self.service_id = service_id
        self.tenant_id = tenant_id

    def get_rpc_data_payload(self, service, port, get_endpoint, timeout=900):
        print("End Point Triggered: {}".format(get_endpoint))
        modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}
        rpc_payload = {
            "rpcName": "get_data",
            "service": service,
            "port": port,
            "method": "GET",
            "endpoint": get_endpoint,
            "headers": modified_headers,
            "timeout": timeout,
        }
        response_from_request = requests.post(
            "http://localhost:8081/rpc",
            data=json.dumps(rpc_payload),
            headers={"Content-type": "application/json"},
            verify=False,
            timeout=900,
        )
        if response_from_request.status_code:
            print("get metadata response status code: " + str(response_from_request.status_code))

        response_json = json.loads(response_from_request.content.decode())
        response_json = base64.b64decode(response_json['payload']).decode('utf-8')
        response_json = json.loads(response_json)
        print(f"response_json is:")
        pprint.pprint(response_json)
        print("\n\n")
        return response_json, response_from_request.status_code

    def post_rpc_data_payload(self, service, data, port, endpoint, request="POST", timeout=900, expect_response=True):
        print("End Point Triggered: {}".format(endpoint))

        data = json.dumps(data, ensure_ascii=False)
        data = data.encode()
        modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}

        rpc_payload = {
            "rpcName": "post_or_update_tessell_service",
            "service": service,
            "port": port,
            "method": request,
            "endpoint": endpoint,
            "payload": base64.b64encode(data).decode("utf-8"),
            "headers": modified_headers,
            "timeout": timeout,
        }
        response_from_request = requests.post(
            "http://localhost:8081/rpc",
            data=json.dumps(rpc_payload),
            headers={"Content-type": "application/json"},
            verify=False,
            timeout=900,
        )
        if response_from_request.status_code:
            print(f"{request} metadata response status code: " + str(response_from_request.status_code))

        if not expect_response:
            print(f"Expecting no response from the request {request}")
            return response_from_request.status_code

        response_json = json.loads(response_from_request.content.decode())
        response_json = base64.b64decode(response_json['payload']).decode('utf-8')
        response_json = json.loads(response_json)
        print(f"response_json is:")
        pprint.pprint(response_json)
        print("\n\n")
        return response_json


class OpsOperations:
    def __init__(self, engine_type):
        self.tessell_instance_metadata = None
        self.tessell_instance_id = None
        if engine_type.lower() == "oracle":
            self.service_id = self._get_input_service_id()
            self.tenant_id = self._get_input_tenant_id()
        else:
            print("Automated discovery of service_id and tenant_id")
            self.service_id, self.tenant_id = read_log_sweep_config()
        self.rpc = RPCRequest(service_id=service_id, tenant_id=tenant_id)
        self.storage_list = []

    def _validate_service_instance_id(self, service_instance_id):
        service = "tessell-database-system"
        get_endpoint = f"/tessell-ops/services/{self.service_id}/service-instances/{service_instance_id}"
        response, response_code = self.rpc.get_rpc_data_payload(service, rpc_port, get_endpoint)
        if response_code != 200:
            print("ERROR: Invalid service instance id {}".format(service_instance_id))
            return False
        else:
            return True

    def _get_serive_instance_metadata_latest(self, service_instance_id):
        service = "tessell-database-system"
        get_endpoint = f"/tessell-ops/services/{self.service_id}/service-instances/{service_instance_id}/metadata/version/latest"
        response, response_code = self.rpc.get_rpc_data_payload(service, rpc_port, get_endpoint)
        return response

    def _get_input_service_instance_id_using_cli(self):
        service_instance_id = input("Enter the service instance id:\n")
        if self._validate_service_instance_id(service_instance_id):
            return service_instance_id
        else:
            raise ValueError("Invalid service instance id")

    def _get_input_service_id(self):
        service_id = input("Enter the service Id:\n")
        return service_id

    def _get_input_tenant_id(self):
        tenant_id = input("Enter the tenant Id:\n")
        return tenant_id

    def get_storage_ids_for_the_service(self):
        self.tessell_instance_id = self._get_input_service_instance_id_using_cli()
        print("Getting tessell_service_instance_metadata \n")
        self.tessell_instance_metadata = self._get_serive_instance_metadata_latest(self.tessell_instance_id)
        storage_info = self.tessell_instance_metadata["metadata"]["data"]["volumes"]
        for key,value in storage_info.items():
            print(f"Storage ID: {key} \n")
            self.storage_list.append(key)
            do_pprint(value)
        return

    def get_storage(self,storage_id):
        service = "tessell-governance"
        get_endpoint = f"/tessell-ops/storages/{storage_id}"
        response, response_code = self.rpc.get_rpc_data_payload(service, rpc_port, get_endpoint)
        if response_code != 200:
            print(f"=============================================ERROR: Invalid storage id {storage_id}"
                  f"=============================================")
        return response
    def get_storage_mapping(self,storage_id):
        print(f"Storage ID: {storage_id}")
        service = "tessell-database-system"
        get_endpoint = f"/tessell-ops/services/{self.service_id}/storages/{storage_id}"
        response, response_code = self.rpc.get_rpc_data_payload(service, rpc_port, get_endpoint)
        if response_code != 200:
            print(f"=============================================ERROR: Invalid storage id {storage_id}"
                  f"=============================================")
        return response

    def patch_storage_mapping(self,storage_mapping,new_volume_id):
        service = "tessell-database-system"
        post_endpoint = f"/tessell-ops/services/{self.service_id}/storages"
        new_copy_storage_mapping = storage_mapping.copy()
        new_copy_storage_mapping["cloudLocation"] = new_volume_id
        self.validate_database_provide_is_correct_using_input(storage_mapping,new_copy_storage_mapping)
        try:
            # IT is a POST call it will PATCH/update the storage mapping
            response_json = self.rpc.post_rpc_data_payload(service=service, data=new_copy_storage_mapping, port=rpc_port,
                                                           endpoint=post_endpoint, request="POST",expect_response=False)
            print("Updating tessell_storages table done\n")
            return True
        except Exception as e:
            print(f"Failed to update storage mapping {new_copy_storage_mapping} with new volume id {new_volume_id}. Error: {e}")
            raise Exception(f"Failed to update storage mapping {new_copy_storage_mapping} with new volume id {new_volume_id}. Error: {e}")
        pass


    def input_new_volume_id(self, old_volume_id):
        new_volume_id = input(f"Enter new volume id for {old_volume_id}:\n")
        if new_volume_id == "" or new_volume_id == None:
            print("Invalid volume id")
            raise Exception("Invalid volume id {}".format(new_volume_id))
        return new_volume_id

    def input_storage_key_to_update(self):
        storage_key = input("Enter the storage key to update:\n")
        if storage_key == "" or storage_key == None:
            print("Invalid storage key")
            raise Exception("Invalid storage key {}".format(storage_key))
        return storage_key

    def validate_database_provide_is_correct_using_input(self, old_storage_info,new_storage_info):
        print("\nOld Storage Info:")
        pprint.pprint(old_storage_info)
        print("\nNew Storage Info:")
        pprint.pprint(new_storage_info)
        input_yes_or_no = input(f"Is the new storage info correct? (yes/no)\n"
                                f"================"
                                f"WARNING THIS WILL UPDATE THE STORAGE MAPPING and Could cause data issue"
                                f"================\n")
        if input_yes_or_no.lower() == "yes":
            return True
        else:
            raise Exception("Invalid storage info failing to update storage mapping")
    def check_and_update_storage(self,storage_id, storage_info, new_volume_id):
        print(f"Storage ID: {storage_id}")
        print(f"Storage Info: {storage_info}")
        service = "tessell-governance"
        post_endpoint = f"/tessell-ops/storages/{storage_id}"
        new_copy_storage_info = storage_info.copy()
        new_copy_storage_info["cloudResourceId"] = new_volume_id
        self.validate_database_provide_is_correct_using_input(storage_info,new_copy_storage_info)
        try:
            response_json = self.rpc.post_rpc_data_payload(service=service, data=new_copy_storage_info, port=rpc_port,
                                                           endpoint=post_endpoint, request="PATCH")
            print("Updating tessell_storages table done")
            return True
        except Exception as e:
            print(f"Failing to update storage {storage_id} with new volume id {new_volume_id}. Error: {e}")
            raise Exception(f"Failing to update storage {storage_id} with new volume id {new_volume_id}. Error: {e}")
        pass

    def check_and_update_storage_mapping(self,storage_id,new_volume_id):
        # get and update storage_mapping_now
        storage_mapping = self.get_storage_mapping(storage_id=storage_id)
        self.patch_storage_mapping(storage_mapping=storage_mapping,new_volume_id=new_volume_id)
        print("===================UPDATED_STORAGE_MAPPING===================")
        self.get_storage_mapping(storage_id=storage_id)

    def check_and_update_tessell_service_instance_metadata(self,storage_id,new_volume_id):
        # duplicate the metadata and update the storage mapping
        new_copy_tessell_instance_metadata = copy.deepcopy(self.tessell_instance_metadata)

        new_copy_tessell_instance_metadata["metadata"]["data"]["volumes"][storage_id]["cloud_resource_id"] = new_volume_id
        self.validate_database_provide_is_correct_using_input(self.tessell_instance_metadata, new_copy_tessell_instance_metadata)
        try:
            service = "tessell-database-system"
            post_endpoint = f"/tessell-ops/services/{self.service_id}/service-instances/{self.tessell_instance_id}/metadata"
            # IT is a POST call it will PATCH/update the storage mapping
            response_json = self.rpc.post_rpc_data_payload(service=service, data=new_copy_tessell_instance_metadata,
                                                           port=rpc_port,
                                                           endpoint=post_endpoint, request="POST")
            print("Updating tessell_service_instance_metadata done")
        except Exception as e:
            print(f"Failed to updated tessell_service_instance_metadata {new_copy_tessell_instance_metadata} "
                  f"with new volume id {new_volume_id}. Error: {e}")
            raise Exception(f"Failed to updated tessell_service_instance_metadata {new_copy_tessell_instance_metadata} "
                            f"with new volume id {new_volume_id}. Error: {e}")

    def update_storage_mapping(self):
        self.get_storage_ids_for_the_service()
        storage_key = self.input_storage_key_to_update()
        storage_info = self.get_storage(storage_id=storage_key)
        new_volume_id = self.input_new_volume_id(storage_info["cloudResourceId"])

        self.check_and_update_storage(storage_id=storage_key, storage_info=storage_info, new_volume_id=new_volume_id)
        print("---------------------------------TESSELL_STORAGE TABLE UPDATE DONE---------------------------------")

        self.check_and_update_storage_mapping(storage_id=storage_key,new_volume_id=new_volume_id)
        print("-----------------------TESSELL_SERVICE_STORAGE_MAPPING TABLE UPDATE DONE--------------------")

        self.check_and_update_tessell_service_instance_metadata(storage_id=storage_key,new_volume_id=new_volume_id)
        print("-----------------------TESSELL_SERVICE_INSTANCE_METADATA TABLE UPDATE DONE--------------------")




if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 <script_name> <engine_type>")
        exit(1)
    engine_type = sys.argv[1].lower()
    ops = OpsOperations(engine_type=engine_type)
    ops.update_storage_mapping()
