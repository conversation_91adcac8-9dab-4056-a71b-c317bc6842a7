import requests,base64,sys,json,logging

logging.getLogger().addH<PERSON><PERSON>(logging.StreamHandler(sys.stdout))
logging.getLogger().setLevel(logging.INFO)

LOG_SWEEP_CONFIG_FILE = "/opt/tessell-base/.tessell/log_sweep/logSweepConfig.json"
tenant_id=service_id=service_instance_id=compute_resource_id=""

def read_log_sweep_config():
    f = open(LOG_SWEEP_CONFIG_FILE,)
    logSweepConfig = json.load(f)
    logSweepConfig = logSweepConfig["log_sweep_config"]
    tenant_id = logSweepConfig['tenantId']
    compute_resource_id = logSweepConfig['computeResourceId'] 
    return tenant_id,compute_resource_id

def get_metadata(get_endpoint, service="tessell-database-system"):
    modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}
    # Get data from Tessell Agent (/rpc endpoint)
    rpc_data = {
        "rpcName": "get_data",
        "service": service,
        "port": 8080,
        "method": "GET",
        "endpoint": get_endpoint,
        "headers": modified_headers,
        "timeout": 900,
    }

    response_from_request = requests.post(
        "http://localhost:8081/rpc",
        data=json.dumps(rpc_data),
        headers = {"Content-type": "application/json"},
        verify=False,
        timeout=900,
    )

    if response_from_request.status_code:
        print("get metadata response status code: " + str(response_from_request.status_code))

    response_json = json.loads(response_from_request.content.decode())
    response_json = base64.b64decode(response_json['payload']).decode('utf-8')
    response_json = json.loads(response_json)
    print(f"response_json is {json.dumps(response_json)}")
    return response_json

def update_metadata(update_endpoint, data, request="POST"):
    data = json.dumps(data, ensure_ascii=False)
    data = data.encode()
    modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}
    rpc_data = {
        "rpcName": "update_tessell_service",
        "service": "tessell-database-system",
        "port": 8080,
        "method": request,
        "endpoint": update_endpoint,
        "payload": base64.b64encode(data).decode("utf-8"),
        "headers": modified_headers,
        "timeout": 900,
    }

    response_from_request = requests.post(
        "http://localhost:8081/rpc",
        data=json.dumps(rpc_data),
        headers = {"Content-type": "application/json"},
        verify=False,
        timeout=900,
    )

    if response_from_request.status_code:
        print(f"update metadata response status code: " +str(response_from_request.status_code))
    return response_from_request

def update_service(new_status):
    get_endpoint = f"/tessell-ops/services/{service_id}"
    print(f"Getting service info ...")
    db_service = get_metadata(get_endpoint)
    db_service['status'] = new_status
    db_service['contextInfo'] = None
    update_endpoint=f"/tessell-ops/services/{service_id}"
    print(f"Updating service info with {db_service}...")
    update_metadata(update_endpoint, db_service, request="PATCH")
    
def update_service_instance(new_status):
    get_endpoint = f"/tessell-ops/services/{service_id}/service-instances/{service_instance_id}"
    print(f"Getting service instance info ...")
    dbservice_instance = get_metadata(get_endpoint)
    print(f"service instance info is {dbservice_instance}")
    exit(0)
    dbservice_instance['status'] = 'UP' if new_status == 'READY' else new_status
    update_endpoint=f"/tessell-ops/services/{service_id}/service-instances/{service_instance_id}"
    print(f"Updating service instance info with {dbservice_instance}...")
    update_metadata(update_endpoint, dbservice_instance, request="PATCH")

if __name__ == "__main__":
    service_id = sys.argv[1]
    service_instance_id = sys.argv[2]
    new_status = sys.argv[3]
    tenant_id,compute_resource_id = read_log_sweep_config()

    update_service(new_status)
    update_service_instance(new_status)