Guide:
================

https://tessell.atlassian.net/wiki/spaces/~7120209565d9f4ffe6459f9c90d245819f65bb/pages/842596380/Automation+of+gathering+RDS+Aurora+Metrics

Pre-requisites:
================
getops

awscli

unzip

AWS Access keys with read-only cloudwatch access

Usage:
================
	
	
	tessell_fetch_rds_aurora_metrics.sh --type <type> --file <input-file> --period <period> --days <number of days>
 	where,

	--help | -h		: Display this help.
	--type | -t		: Please provide db type either RDS or AURORA to collect metrics[Mandatory].
	--file | -f		: Please provide file containing db identifier list separated by new lines[Mandatory].
	--days | -d		: Please provide number of days to determine first data point and last data point for metrics.
	--start_date | -s	: Please provide start date to determine first data point in the format 'YYYY-MM-DDTHH:MM:SS'.
	--end_date | -e		: Please provide end date to determine last data point in the format 'YYYY-MM-DDTHH:MM:SS'.
	--period | -p		: The granularity, in seconds, of the returned data points. Default value is 86400 seconds.

	Note: The options -d and -s/-e are mutually exclusive and you can provide either one of them.

	Ex:

        tessell_fetch_rds_aurora_metrics.sh --type <type> --file /path/to/db_list.txt --period <period> --days <number of days>
        tessell_fetch_rds_aurora_metrics.sh --type <type> --file /path/to/db_list.txt --period <period> --start_date '2020-12-10T00:00:00' --end_date '2020-12-10T23:59:59'
        

Steps to be followed:
=====================
- Setup aws access keys and region in jump host.
  > aws configure
  
- Copy our automation code to jump host.

- Create a text file with list of db instance identifier.

  Ex:
   
      cat db_list.txt
      db1
      db2  
      db3
      
- Run the automation with number of days to be analysied along with cloudwatch metric sampling interval.  This will take few minutes which depend upon number of db instances to be analysed.
  > time bash tessell_fetch_rds_aurora_metrics.sh --type aurora --days 7 --period 86400 --file /tmp/db_list.txt

- Compress the generated reports and share it with Tessell DBA.
  > zip /tmp/get_metrics*.zip
   
