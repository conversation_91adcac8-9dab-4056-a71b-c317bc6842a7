#!/bin/bash

fn_initialize()
{

    #awsbin="/usr/local/bin/aws"
    script_folder="/tmp/get_metrics_`date +%Y%m%d_%H%M%S`"
    stdout="${script_folder}/get_metrics_stdout.log"
    stderr="${script_folder}/get_metrics_stderr.err"
    d_period=86400
    scriptlog="${script_folder}/${type}_get_metrics_script_`date +%Y%m%d_%H%M%S`.log"
    csvfile="${script_folder}/${type}_metrics_`date +%Y%m%d_%H%M%S`.csv"
    rm -f ${csvfile}
    rm -f /tmp/rds_list.txt
}

fn_check_package()
{
  if [[ ! `which getopt 2> /dev/null` ]]; then
    echo "ERROR: Package getopt is not installed. You can install by running below command based on its OS."
    echo "yum install gnu-getopt --- for CentOS"
    echo "brew install gnu-getopt --- for MacOS"
    echo "apt-get install util-linux --- for Ubuntu"
    exit 1
  fi;

  if [[ ! `which aws 2> /dev/null` ]]; then
    echo "ERROR: Package aws is not installed. Kindly install aws cli and configure aws credentials "
    exit 1
  else
    awsbin=`which aws`
  fi;


}

fn_get_metrics()
{

    gm_start_date=$1
    gm_end_date=$2
    gm_resource_name=$3
    gm_metric_name=$4
    gm_stats=$5
    gm_namespace=""
    gm_dimention=""
    percentile=0

    if [ "${type}" = "rds" -o "${type}" = "RDS" ]; then
        gm_namespace="RDS"
        gm_dimention="DBInstanceIdentifier"

    elif [ "${type}" = "aurora" -o "${type}" = "AURORA" ]; then
        gm_namespace="RDS"
        gm_dimention="DBInstanceIdentifier"

    else
        echo -e "Invalid resource type - ${type}\n"
        exit 1
    fi;

    if [ "${gm_stats}" = "Maximum" -o "${gm_stats}" = "Average" -o "${gm_stats}" = "Minimum" -o "${gm_stats}" = "SampleCount" -o "${gm_stats}" = "Sum"  ]; then

        gm_stats_option=`echo "--statistics=${gm_stats}"`

    elif [ `echo "${gm_stats}" | grep -i '^p'` ]; then
        gm_stats_option=`echo "--extended-statistics=${gm_stats}"`
        percentile=1
    else
        echo -e "Invalid option for statistics - ${gm_stats}\n" | tee -a ${scriptlog} > /dev/null
        exit 1
    fi;

    #echo "start time: ${gm_start_date}, end time: ${gm_end_date}"

	#${awsbin} cloudwatch get-metric-statistics --namespace AWS/${gm_namespace} --metric-name  ${gm_metric_name}  --dimensions Name=${gm_dimention},Value=${gm_resource_name} --statistics=${gm_stats} --start-time ${gm_start_date} --end-time ${gm_end_date} --period=${period} --output text 1> ${stdout} 2> ${stderr}

    ${awsbin} cloudwatch get-metric-statistics --namespace AWS/${gm_namespace} --metric-name  ${gm_metric_name}  --dimensions Name=${gm_dimention},Value=${gm_resource_name} ${gm_stats_option} --start-time ${gm_start_date} --end-time ${gm_end_date} --period=${period} --output text 1> ${stdout} 2> ${stderr}

	if [ $? -ne 0 ]; then
        rate_exceeded_check=`cat ${stderr} | grep -i "reached max retries" | wc -l`
        if [ ${rate_exceeded_check} -gt 0 ]; then
            echo -e "Rate exceeded error occurred for ${gm_metric_name} Cloudwatch metric statistics(${gm_stats}) for ${type} ${gm_resource_name} \n" | tee -a ${scriptlog} > /dev/null
            echo -e "Re-try for table ${gm_resource_name}\n" | tee -a ${scriptlog} > /dev/null
            #fn_get_metrics $1 $2 $3 $4 $5
        else
            echo -e "Failed to get ${gm_metric_name} Cloudwatch metric statistics(${gm_stats}) for ${type} ${gm_resource_name} with below error.\n" | tee -a ${stderr} > /dev/null

            cat ${stderr} >> ${scriptlog}
            rm -f ${stderr}

            metric_value="-999"
            exit 1
        fi;

    else
        if [ ${percentile} -eq 0 ]; then
            metric_value=`cat ${stdout} | grep -i 'DATAPOINTS'| sort -g -k2 | tail -n 1 | awk '{print $2}'`
        elif [ ${percentile} -eq 1 ]; then
            metric_value=`cat ${stdout} | grep 'EXTENDEDSTATISTICS' | sort -g -k2 | tail -n 1 | awk '{print $2}'`
        fi;
        rm -f ${stdout}

    fi;

    echo "${metric_value}"

}

fn_rds()
{

    rm -f ${csvfile}
    if [ -s ${input_file} ]; then
        echo "Start Time : ${start_date}" > ${csvfile}
        echo "End Time : ${end_date}" >> ${csvfile}
        echo "DB,Type,Total Memory,Max Conn Limit,DBConn Max,DBConn Avg,DBConn p95,CPU Max,CPU Avg,CPU p95,FreeMem Min,FreeMem PCT,SwapUsage Max,SwapUsage Pct,ReadIOPS Max,ReadIOPS Avg,ReadIOPS p95,WriteIOPS Max,WriteIOPS Avg,WriteIOPS p95,Read Throughput Max,Read Throughput Avg,Read Throughput p95,Write Throughput Max,Write Throughput Avg,Write Throughput p95" > ${csvfile}

        while read -r line
        do
            rds_id=`echo ${line} | cut -d',' -f1`
            db_type=""
            #total_memory=`grep ${db_type} /dba/dhivya/scripts/instance_class.txt |grep -v "${db_type}\." | cut -d',' -f3`
            echo -e "RDS name\t: $rds_id" | tee -a ${scriptlog}
            Max_DBConnection=""
            DatabaseConnections_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DatabaseConnections" "Maximum"`
            DatabaseConnections_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DatabaseConnections" "Average"`
            DatabaseConnections_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DatabaseConnections" "p95"`
            CPUUtilization_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CPUUtilization" "Maximum"`
            CPUUtilization_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CPUUtilization" "Average"`
            CPUUtilization_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CPUUtilization" "p95"`
            FreeableMemory_Min=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "FreeableMemory" "Minimum"`
            #FreeableMemory_Pct=`echo "$FreeableMemory_Min / ($total_memory*1024*1024*1024) * 100" | bc`
            FreeableMemory_Pct=""
            SwapUsage_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "SwapUsage" "Maximum"`
            #SwapUsage_Pct=`echo "$SwapUsage_Max / ($total_memory*1024*1024*1024) * 100" | bc`
            SwapUsage_Pct=""
            ReadIOPS_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadIOPS" "Maximum"`
            ReadIOPS_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadIOPS" "Average"`
            ReadIOPS_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadIOPS" "p95"`
            WriteIOPS_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteIOPS" "Maximum"`
            WriteIOPS_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteIOPS" "Average"`
            WriteIOPS_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteIOPS" "p95"`
            ReadThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadThroughput" "Maximum"`
            ReadThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadThroughput" "Average"`
            ReadThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadThroughput" "p95"`
            WriteThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteThroughput" "Maximum"`
            WriteThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteThroughput" "Average"`
            WriteThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteThroughput" "p95"`
            echo "${rds_id},${db_type},${total_memory},${Max_DBConnection},${DatabaseConnections_Max},${DatabaseConnections_Avg},${DatabaseConnections_p95},${CPUUtilization_Max},${CPUUtilization_Avg},${CPUUtilization_p95},${FreeableMemory_Min},${FreeableMemory_Pct},${SwapUsage_Max},${SwapUsage_Pct},${ReadIOPS_Max},${ReadIOPS_Avg},${ReadIOPS_p95},${WriteIOPS_Max},${WriteIOPS_Avg},${WriteIOPS_p95},${ReadThroughput_Max},${ReadThroughput_Avg},${ReadThroughput_p95},${WriteThroughput_Max},${WriteThroughput_Avg},${WriteThroughput_p95}" >> ${csvfile}

        done < ${input_file}
        #rm -f /tmp/rds_list.txt
        echo -e "\nOutfile\t: ${csvfile}"
        echo -e "\nLog\t: ${scriptlog}"
        echo -e "\nError\t: `grep '^Failed to get' ${scriptlog} | wc -l`"
        echo -e "\nWarning\t: `grep '^Rate exceeded error' ${scriptlog} | wc -l`\n"

    else
        echo -e "Failed to get list of RDS Instance details with below error.\n" | tee -a  ${stderr} > /dev/null
        cat ${stderr} >> ${scriptlog}
        rm -f ${stderr}
        exit 1
    fi;

}



fn_aurora()
{

    rm -f ${csvfile}
    if [ -s ${input_file} ]; then
        echo "Start Time : ${start_date}" > ${csvfile}
        echo "End Time : ${end_date}" >> ${csvfile}
        echo "DB,Type,Total Memory,Max Conn Limit,DBConn Max,DBConn Avg,DBConn p95,CPU Max,CPU Avg,CPU p95,FreeMem Min,FreeMem PCT,SwapUsage Max,SwapUsage Pct,ReadIOPS Max,ReadIOPS Avg,ReadIOPS p95,WriteIOPS Max,WriteIOPS Avg,WriteIOPS p95,ReadLatency Max,ReadLatency Avg,ReadLatency p95,WriteLatency Max,WriteLatency Avg,WriteLatency p95,Read Throughput Max,Read Throughput Avg,Read Throughput p95,Write Throughput Max,Write Throughput Avg,Write Throughput p95,DiskQueueDepth Max,ConnAttempts Max,ConnAttempts Avg,ConnAttempts p95,Active Transactions Max,Active Transactions Avg,Active Transactions p95,LoginFailures Max,LoginFailures Avg,LoginFailures p95,NetworkThroughput Max,NetworkThroughput Avg,NetworkThroughput p95,NetworkReceiveThroughput Max,NetworkReceiveThroughput Avg,NetworkReceiveThroughput p95,NetworkTransmitThroughput Max,NetworkTransmitThroughput Avg,NetworkTransmitThroughput p95,NumBinaryLogFiles Max,AuroraBinlogReplicaLag Max,AuroraBinlogReplicaLag Min,AuroraReplicaLag Max,AuroraReplicaLagMaximum Max,AuroraReplicaLagMinimum Max,BufferCacheHitRatio Max,CommitLatency Max,CommitLatency Avg,CommitLatency p95,CommitThroughput Max,CommitThroughput Avg,CommitThroughput p95,DDLLatency Max,DDLLatency Avg,DDLLatency p95,DDLThroughput Max,DDLThroughput Avg,DDLThroughput p95,Deadlocks Max,DeleteLatency Max,DeleteLatency Avg,DeleteLatency p95,DeleteThroughput Max,DeleteThroughput Avg,DeleteThroughput p95,DMLLatency Max,DMLLatency Avg,DMLLatency p95,DMLThroughput Max,DMLThroughput Avg,DMLThroughput p95,InsertLatency Max,InsertLatency Avg,InsertLatency p95,InsertThroughput Max,InsertThroughput Avg,InsertThroughput p95,SelectLatency Max,SelectLatency Avg,SelectLatency p95,SelectThroughput Max,SelectThroughput Avg,SelectThroughput p95,UpdateLatency Max,UpdateLatency Avg,UpdateLatency p95,UpdateThroughput Max,UpdateThroughput Avg,UpdateThroughput p95,Queries Max,Queries Avg,Queries p95" >> ${csvfile}
        #echo "DB,Type,Total Memory,Max Conn Limit,DBConn Max,DBConn Avg,DBConn p95,CPU Max,CPU Avg,CPU p95,FreeMem Min,FreeMem PCT,SwapUsage Max,SwapUsage Pct,ReadIOPS Max,ReadIOPS Avg,ReadIOPS p95,WriteIOPS Max,WriteIOPS Avg,WriteIOPS p95,Read Throughput Max,Read Throughput Avg,Read Throughput p95,Write Throughput Max,Write Throughput Avg,Write Throughput p95" >> ${csvfile}
        echo "Fetching Aurora Metrics!!!" | tee -a ${scriptlog}
        count=0
        while read -r line
        do
            count=`expr ${count} + 1 `
            rds_id=`echo ${line} | cut -d',' -f1`
            db_type=""
            #total_memory=`grep ${db_type} /dba/dhivya/scripts/instance_class.txt |grep -v "${db_type}\." | cut -d',' -f3`
            echo "${count}: $rds_id" | tee -a ${scriptlog}
            Max_DBConnection=""
            DatabaseConnections_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DatabaseConnections" "Maximum"`
            DatabaseConnections_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DatabaseConnections" "Average"`
            DatabaseConnections_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DatabaseConnections" "p95"`
            ConnectionAttempts_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ConnectionAttempts" "Maximum"`
            ConnectionAttempts_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ConnectionAttempts" "Average"`
            ConnectionAttempts_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ConnectionAttempts" "p95"`
            ActiveTransactions_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ActiveTransactions" "Maximum"`
            ActiveTransactions_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ActiveTransactions" "Average"`
            ActiveTransactions_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ActiveTransactions" "p95"`
            LoginFailures_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "LoginFailures" "Maximum"`
            LoginFailures_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "LoginFailures" "Average"`
            LoginFailures_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "LoginFailures" "p95"`
            CPUUtilization_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CPUUtilization" "Maximum"`
            CPUUtilization_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CPUUtilization" "Average"`
            CPUUtilization_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CPUUtilization" "p95"`
            FreeableMemory_Min=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "FreeableMemory" "Minimum"`
            #FreeableMemory_Pct=`echo "$FreeableMemory_Min / ($total_memory*1024*1024*1024) * 100" | bc`
            FreeableMemory_Pct=""
            SwapUsage_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "SwapUsage" "Maximum"`
            #SwapUsage_Pct=`echo "$SwapUsage_Max / ($total_memory*1024*1024*1024) * 100" | bc`
            SwapUsage_Pct=""
            ReadIOPS_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadIOPS" "Maximum"`
            ReadIOPS_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadIOPS" "Average"`
            ReadIOPS_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadIOPS" "p95"`
            ReadLatency_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadLatency" "Maximum"`
            ReadLatency_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadLatency" "Average"`
            ReadLatency_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadLatency" "p95"`
            ReadThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadThroughput" "Maximum"`
            ReadThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadThroughput" "Average"`
            ReadThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "ReadThroughput" "p95"`
            WriteIOPS_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteIOPS" "Maximum"`
            WriteIOPS_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteIOPS" "Average"`
            WriteIOPS_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteIOPS" "p95"`
            WriteLatency_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteLatency" "Maximum"`
            WriteLatency_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteLatency" "Average"`
            WriteLatency_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteLatency" "p95"`
            WriteThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteThroughput" "Maximum"`
            WriteThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteThroughput" "Average"`
            WriteThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "WriteThroughput" "p95"`
            DiskQueueDepth_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DiskQueueDepth" "Maximum"`
            NetworkThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "NetworkThroughput" "Maximum"`
            NetworkThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "NetworkThroughput" "Average"`
            NetworkThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "NetworkThroughput" "p95"`
            NetworkReceiveThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "NetworkReceiveThroughput" "Maximum"`
            NetworkReceiveThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "NetworkReceiveThroughput" "Average"`
            NetworkReceiveThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "NetworkReceiveThroughput" "p95"`
            NetworkTransmitThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "NetworkTransmitThroughput" "Maximum"`
            NetworkTransmitThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "NetworkTransmitThroughput" "Average"`
            NetworkTransmitThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "NetworkTransmitThroughput" "p95"`

            NumBinaryLogFiles_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "NumBinaryLogFiles" "Maximum"`
            AuroraBinlogReplicaLag_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "AuroraBinlogReplicaLag" "Maximum"`
            AuroraBinlogReplicaLag_Min=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "AuroraBinlogReplicaLag" "Minimum"`
            AuroraReplicaLag_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "AuroraReplicaLag" "Maximum"`
            AuroraReplicaLagMaximum_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "AuroraReplicaLagMaximum" "Maximum"`
            AuroraReplicaLagMinimum_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "AuroraReplicaLagMinimum" "Minimum"`
            BufferCacheHitRatio_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "BufferCacheHitRatio" "Maximum"`
            CommitLatency_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CommitLatency" "Maximum"`
            CommitLatency_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CommitLatency" "Average"`
            CommitLatency_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CommitLatency" "p95"`
            CommitThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CommitThroughput" "Maximum"`
            CommitThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CommitThroughput" "Average"`
            CommitThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "CommitThroughput" "p95"`

            DDLLatency_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DDLLatency" "Maximum"`
            DDLLatency_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DDLLatency" "Average"`
            DDLLatency_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DDLLatency" "p95"`
            DDLThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DDLThroughput" "Maximum"`
            DDLThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DDLThroughput" "Average"`
            DDLThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DDLThroughput" "p95"`
            Deadlocks_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "Deadlocks" "Maximum"`
            DeleteLatency_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DeleteLatency" "Maximum"`
            DeleteLatency_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DeleteLatency" "Average"`
            DeleteLatency_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DeleteLatency" "p95"`
            DeleteThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DeleteThroughput" "Maximum"`
            DeleteThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DeleteThroughput" "Average"`
            DeleteThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DeleteThroughput" "p95"`
            DMLLatency_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DMLLatency" "Maximum"`
            DMLLatency_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DMLLatency" "Average"`
            DMLLatency_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DMLLatency" "p95"`
            DMLThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DMLThroughput" "Maximum"`
            DMLThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DMLThroughput" "Average"`
            DMLThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "DMLThroughput" "p95"`
            InsertLatency_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "InsertLatency" "Maximum"`
            InsertLatency_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "InsertLatency" "Average"`
            InsertLatency_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "InsertLatency" "p95"`
            InsertThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "InsertThroughput" "Maximum"`
            InsertThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "InsertThroughput" "Average"`
            InsertThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "InsertThroughput" "p95"`
            SelectLatency_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "SelectLatency" "Maximum"`
            SelectLatency_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "SelectLatency" "Average"`
            SelectLatency_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "SelectLatency" "p95"`
            SelectThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "SelectThroughput" "Maximum"`
            SelectThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "SelectThroughput" "Average"`
            SelectThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "SelectThroughput" "p95"`
            UpdateLatency_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "UpdateLatency" "Maximum"`
            UpdateLatency_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "UpdateLatency" "Average"`
            UpdateLatency_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "UpdateLatency" "p95"`
            UpdateThroughput_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "UpdateThroughput" "Maximum"`
            UpdateThroughput_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "UpdateThroughput" "Average"`
            UpdateThroughput_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "UpdateThroughput" "p95"`
            Queries_Max=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "Queries" "Maximum"`
            Queries_Avg=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "Queries" "Average"`
            Queries_p95=`fn_get_metrics "${start_date}" "${end_date}" "${rds_id}" "Queries" "p95"`

            #echo "${rds_id},${db_type},${total_memory},${Max_DBConnection},${DatabaseConnections_Max},${DatabaseConnections_Avg},${DatabaseConnections_p95},${CPUUtilization_Max},${CPUUtilization_Avg},${CPUUtilization_p95},${FreeableMemory_Min},${FreeableMemory_Pct},${SwapUsage_Max},${SwapUsage_Pct},${ReadIOPS_Max},${ReadIOPS_Avg},${ReadIOPS_p95},${WriteIOPS_Max},${WriteIOPS_Avg},${WriteIOPS_p95},${ReadThroughput_Max},${ReadThroughput_Avg},${ReadThroughput_p95},${WriteThroughput_Max},${WriteThroughput_Avg},${WriteThroughput_p95}" >> ${csvfile}
            echo "${rds_id},${db_type},${total_memory},${Max_DBConnection},${DatabaseConnections_Max},${DatabaseConnections_Avg},${DatabaseConnections_p95},${CPUUtilization_Max},${CPUUtilization_Avg},${CPUUtilization_p95},${FreeableMemory_Min},${FreeableMemory_Pct},${SwapUsage_Max},${SwapUsage_Pct},${ReadIOPS_Max},${ReadIOPS_Avg},${ReadIOPS_p95},${WriteIOPS_Max},${WriteIOPS_Avg},${WriteIOPS_p95},${ReadLatency_Max},${ReadLatency_Avg},${ReadLatency_p95},${WriteLatency_Max},${WriteLatency_Avg},${WriteLatency_p95},${ReadThroughput_Max},${ReadThroughput_Avg},${ReadThroughput_p95},${WriteThroughput_Max},${WriteThroughput_Avg},${WriteThroughput_p95},${DiskQueueDepth_Max},${ConnectionAttempts_Max},${ConnectionAttempts_Avg},${ConnectionAttempts_p95},${ActiveTransactions_Max},${ActiveTransactions_Avg},${ActiveTransactions_p95},${LoginFailures_Max},${LoginFailures_Avg},${LoginFailures_p95},${NetworkThroughput_Max},${NetworkThroughput_Avg},${NetworkThroughput_p95},${NetworkReceiveThroughput_Max},${NetworkReceiveThroughput_Avg},${NetworkReceiveThroughput_p95},${NetworkTransmitThroughput_Max},${NetworkTransmitThroughput_Avg},${NetworkTransmitThroughput_p95},${NumBinaryLogFiles_Max},${AuroraBinlogReplicaLag_Max},${AuroraBinlogReplicaLag_Min},${AuroraReplicaLag_Max},${AuroraReplicaLagMaximum_Max},${AuroraReplicaLagMinimum_Max},${BufferCacheHitRatio_Max},${CommitLatency_Max},${CommitLatency_Avg},${CommitLatency_p95},${CommitThroughput_Max},${CommitThroughput_Avg},${CommitThroughput_p95},${DDLLatency_Max},${DDLLatency_Avg},${DDLLatency_p95},${DDLThroughput_Max},${DDLThroughput_Avg},${DDLThroughput_p95},${Deadlocks_Max},${DeleteLatency_Max},${DeleteLatency_Avg},${DeleteLatency_p95},${DeleteThroughput_Max},${DeleteThroughput_Avg},${DeleteThroughput_p95},${DMLLatency_Max},${DMLLatency_Avg},${DMLLatency_p95},${DMLThroughput_Max},${DMLThroughput_Avg},${DMLThroughput_p95},${InsertLatency_Max},${InsertLatency_Avg},${InsertLatency_p95},${InsertThroughput_Max},${InsertThroughput_Avg},${InsertThroughput_p95},${SelectLatency_Max},${SelectLatency_Avg},${SelectLatency_p95},${SelectThroughput_Max},${SelectThroughput_Avg},${SelectThroughput_p95},${UpdateLatency_Max},${UpdateLatency_Avg},${UpdateLatency_p95},${UpdateThroughput_Max},${UpdateThroughput_Avg},${UpdateThroughput_p95},${Queries_Max},${Queries_Avg},${Queries_p95}" >> ${csvfile}
        done < ${input_file}
        #rm -f /tmp/rds_list.txt
        echo -e "\nOutfile\t: ${csvfile}"
        echo -e "\nLog\t: ${scriptlog}"
        echo -e "\nError\t: `grep '^Failed to get' ${scriptlog} | wc -l`"
        echo -e "\nWarning\t: `grep '^Rate exceeded error' ${scriptlog} | wc -l`\n"

    else
        echo -e "Failed to get list of Aurora Instance details with below error.\n" | tee -a  ${stderr} > /dev/null
        cat ${stderr} >> ${scriptlog}
        rm -f ${stderr}
        exit 1
    fi;

}

fn_help()
{
    	printf "\n"
        printf "
        Usage:

        $0 --type <type> --file <input-file> --period <period> --days <number of days>

        where,\n\n"

        printf "\t%s\t\t%s\n" '--help | -h' ': Display this help.'
        printf "\t%s\t\t%s\n" '--type | -t' ': Please provide db type either RDS or AURORA to collect metrics[Mandatory].'
        printf "\t%s\t\t%s\n" '--file | -f' ': Please provide file containing db identifier list separated by new lines[Mandatory].'
        printf "\t%s\t\t%s\n" '--days | -d' ': Please provide number of days to determine first data point and last data point for metrics.'
        printf "\t%s\t%s\n" "--start_date | -s" ": Please provide start date to determine first data point in the format 'YYYY-MM-DDTHH:MM:SS'."
        printf "\t%s\t\t%s\n" "--end_date | -e" ": Please provide end date to determine last data point in the format 'YYYY-MM-DDTHH:MM:SS'."
        printf "\t%s\t\t%s\n\n" '--period | -p' ": The granularity, in seconds, of the returned data points. Default value is ${d_period} seconds."

        printf "\t%s\n\n" "Note: The options -d and -s/-e are mutually exclusive and you can provide either one of them."

        printf "\t%s\n" "Ex:"
        printf "
        $0 --type <type> --file /path/to/db_list.txt --period <period> --days <number of days>
        $0 --type <type> --file /path/to/db_list.txt --period <period> --start_date '2020-12-10T00:00:00' --end_date '2020-12-10T23:59:59'

\n"
}

fn_integer_check()
{

	V_INTEGER=""
	V_INTEGER="$1"

	RESULT=""
	if [ ${V_INTEGER} -eq ${V_INTEGER} ] 2>/dev/null
	then
		return 0
	else
		echo "`date +"%Y%m%d"` `date +"%H:%M:%S"` dbstats: ERROR: Option requires an integer argument -- $1"
		exit 1
	fi


}

NUM_OF_ARGUMENTS=$#

ARGUMENTS_PASSED=$@

if [ ${NUM_OF_ARGUMENTS} = 0 ];then
	echo "ERROR: Mandatory argument(s) is not passed to the script, Please use -h|--help for usage."
	exit 1
fi



if ! options=$(getopt -o p:d:t:s:e:hf: --long period:,days:,type:,start_date:,end_date:,help,file: -- "$@")
then

       # something went wrong, getopt will put out an error message for us
	exit 1
fi

set -- $options


while [ ${NUM_OF_ARGUMENTS} -gt 0 ]
do
	case $1 in
      -p|--period)
        period=`echo $2|tr -d "[']"`
			  if [ ! -z ${period} ]; then
                fn_integer_check ${period}
        fi
        shift
        ;;
		-d|--days)
			days=`echo $2|tr -d "[']"`

			if [ ! -z ${days} ]; then
          fn_integer_check ${days}
			fi
			shift
			;;
		-f|--file)
			input_file=`echo $2|tr -d "[']"`

			if [ ! -f ${input_file} ]; then
           echo "ERROR: Input file does not exist. Please provide valid file!!!"
           exit 1
      elif [ ! -s ${input_file}  ]; then
          echo "ERROR: Please provide valid file containing database identifier list to the Option -f/--file"
          exit 1
			fi
			shift
			;;
		-t|--type)
			type=`echo $2|tr -d "[']"`

			if [ -z ${type} ]; then

				echo "ERROR: Invalid argument -- -t|--type"
                exit 1
            else

                if [ "${type}" != "rds" -a "${type}" != "RDS" -a "${type}" != "aurora" -a "${type}" != "AURORA"  -a "${type}" != "ddb" -a "${type}" != "DDB" -a "${type}" != "dax" -a "${type}" != "DAX" ]; then
                    echo "ERROR: Invalid argument -- -t|--type"
                    exit 1
                fi;
			fi
			shift
			;;
		-s|--start_date)
			v_start_time=`echo $2|tr -d "[']"`

			if [ -z "${v_start_time}" ]; then
                echo "ERROR: Invalid date value -- -s|--start_date"
			fi
			shift
			;;
		-e|--end_date)
			v_end_time=`echo $2|tr -d "[']"`

			if [ -z "${v_end_time}" ]; then
                echo "ERROR: Invalid date value -- -e|--end_date"
			fi
			shift
			;;
    -h|--help)

        fn_initialize
        fn_help
        exit
        ;;
		(--)
			shift;
			break;;

		(*)
			#break;;
			echo "$0: error - unrecognized option/argument" 1>&2;
			exit 1
			;;

	esac
    	shift
done

if [[ ! -z ${days} ]] && [[ ! -z  "${v_start_time}" || ! -z "${v_end_time}" ]]; then
    echo "ERROR: Option -d and -s/-e are mutually exclusive. You can pass either -d or -s/-e at a time."
    exit 1
elif [[ -z ${days} ]] && [[ -z  "${v_start_time}" ||  -z "${v_end_time}" ]]; then
    echo "ERROR: Option -d and -s/-e are mutually exclusive. You must pass either -d or -s/-e to execute."
    exit 1
elif [[ -z ${days} ]] && [[ -z  "${v_start_time}" && ! -z "${v_end_time}" ]]; then
    echo "ERROR: Option -s/--start_date must be passed."
    exit 1
elif [[ -z ${days} ]] && [[ ! -z  "${v_start_time}" &&  -z "${v_end_time}" ]]; then
    echo "ERROR: Option -e/--end_date must be passed."
    exit 1
fi;


if [[ -z ${type} ]]; then
  echo "ERROR: Option -t/--type is mandatory"
  exit 1;
elif [[ -z ${input_file} ]]; then
  echo "ERROR: Option -f/--file is mandatory"
  exit 1;
fi;



if [ ! -z ${days} ]; then
    if [[ $(uname) == "Darwin" ]]; then
      echo "ERROR: Option -d/--days is not supported for MacOS. Kindly use -s/-e options to pass timeline"
      exit 1
    else
      current_date=`date --utc`
      end_date=`date --utc -d "${current_date}" +%Y-%m-%dT%H:%M:%S`
      start_date=`date --utc -d "${current_date} -${days} days" +%Y-%m-%dT%H:%M:%S`
    fi;
    #current_date=`date --utc`
    #end_date=`date --utc -d "${current_date}" +%Y-%m-%dT%H:%M:%S`
    #start_date=`date --utc -d "${current_date} -${days} days" +%Y-%m-%dT%H:%M:%S`

fi;

if [ ! -z  "${v_start_time}" ] && [ ! -z "${v_end_time}"  ]; then
    start_date=${v_start_time}
    end_date=${v_end_time}

    #start_date=`date --utc -d "$(TZ=Asia/Calcutta date -d ${v_start_time})" "+%Y-%m-%dT%H:%M:%S"`
    #end_date=`date --utc -d "$(TZ=Asia/Calcutta date -d ${v_end_time})" "+%Y-%m-%dT%H:%M:%S"`

fi;

if [ -z  "${start_date}" ] || [ -z "${end_date}"  ]; then
  echo "ERROR: Invalid start time and end time."
  exit 1
fi;


fn_initialize
fn_check_package
#echo "Creating log directory!!!"
if [ ! -d ${script_folder} ]; then
  echo -e "Creating log directory!!!\n"
  mkdir -p ${script_folder}
else
  echo -e "Log directory already exists!!!\n"
fi;

if [ -z ${period} ]; then

    period=${d_period}

fi;

if [ "${type}" = "rds" -o "${type}" = "RDS" ]; then

    fn_rds

elif [ "${type}" = "aurora" -o "${type}" = "AURORA" ]; then

    fn_aurora

fi;



