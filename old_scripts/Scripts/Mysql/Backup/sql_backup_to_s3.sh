#sql_backup_to_s3.sh
#<AUTHOR>

#!/bin/bash


# Database credentials
DB_USER="tessell_backup"
export MYSQL_PWD="Tessell123$"

# AWS S3 Bucket and Directory
S3_BUCKET=$(jq -r '.[] | .bucketName' /opt/tessell-base/.tessell/log_sweep/logSweepConfig.json)
S3_FOLDER=$(jq -r '.[] | .folderName' /opt/tessell-base/.tessell/log_sweep/logSweepConfig.json)
S3_SUB_FOLDER=$(date +"%Y%m%d")


#Backup, Compress And Copy To S3
for DBNAME in $(/mysqlbin/bin/mysql --socket=/var/lib/mysql/mysql.sock -u$DB_USER -s --skip-column-names -e 'SELECT SCHEMA_NAME FROM information_schema.schemata WHERE SCHEMA_NAME NOT IN ("mysql", "tmp", "information_schema", "sys", "performance_schema", "mysql_innodb_cluster_metadata", "db1");' );
do
echo "`date` Starting backup for $DBNAME"
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
FILENAME="${DBNAME}_${TIMESTAMP}.sql.gz"
/mysqlbin/bin/mysqldump --socket=/var/lib/mysql/mysql.sock -u$DB_USER -B --events --routines --triggers --set-gtid-purged=OFF --single-transaction --column-statistics=0 --skip-lock-tables --databases $DBNAME | gzip | $(/usr/local/bin/aws s3 cp - s3://$S3_BUCKET/$S3_FOLDER/SQL_BACKUPS/$S3_SUB_FOLDER/$FILENAME)
BACKUP_COMPLETED=$?
if [[ $BACKUP_COMPLETED != 0 ]]; then
    echo "`date` Backup failed for Database $DBNAME"
    exit 1
else
    echo "`date` Backup completed to S3: ../SQL_BACKUPS/$S3_SUB_FOLDER/$FILENAME"

    #Remove Folder Older than 180 Days.
    # S3_FOLDER_TO_BE_DELETED=$(date -d "$date -180 days" +"%Y%m%d")
    # echo "Deleting the backup older than 180 days"
    # /usr/local/bin/aws s3 rm --recursive --quiet s3://$S3_BUCKET/$S3_FOLDER/SQL_BACKUPS/$S3_FOLDER_TO_BE_DELETED/
    # if [[ $? != 0 ]]; then
    #     echo "Deleting from S3 failed for directory: ../SQL_BACKUPS/$S3_FOLDER_TO_BE_DELETED/*.*"
    #     exit 1
    # else
    #     echo "Deleted backup ../SQL_BACKUPS/$S3_FOLDER_TO_BE_DELETED/*.* successfully"
    # fi
fi
done
