#!/bin/bash

# Get the current date and time
current_datetime=$(date +"%Y%m%d%H%M%S")
current_date=$(date +"%Y%m%d")
current_time=$(date +"%H:%M:%S")

# Directory to store output
output_dir="/tmp"

# Create directory if it doesn't exist
mkdir -p "$output_dir"

# Log file to capture output
log_file="${output_dir}/slobsetup-${current_datetime}.log"

echo "Log file: $log_file"
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

echo "Log file: $log_file" >> "$log_file" 2>&1


# Echo statement to indicate downloading and installing SLOB software
echo "Downloading the SLOB Software and installing..." >> "$log_file" 2>&1

# Change directory to /u02
cd /u02 || { echo "Error: Failed to change directory to /u02." >> "$log_file" 2>&1; exit 1; }

# Remove the main.zip file from /u02
rm -rf /u02/main* >> "$log_file" 2>&1 || { echo "Error: Failed to remove main.zip." >> "$log_file" 2>&1; exit 1; }

# Download the ZIP file and capture output to log file
echo "Downloading the ZIP file..." >> "$log_file" 2>&1
wget 'https://github.com/therealkevinc/SLOB_2.5.4/archive/refs/heads/main.zip'  >> "$log_file" 2>&1 || { echo "Error: Failed to download the ZIP file." >> "$log_file" 2>&1; exit 1; }

# Unzip the downloaded file and capture output to log file
echo "Unzipping the downloaded file..." >> "$log_file" 2>&1
unzip -o main.zip >> "$log_file" 2>&1 || { echo "Error: Failed to unzip the downloaded file." >> "$log_file" 2>&1; exit 1; }

# Change directory to the extracted directory and capture output to log file
echo "Changing directory to the extracted directory..." >> "$log_file" 2>&1
cd /u02/SLOB_2.5.4-main/ >> "$log_file" 2>&1 || { echo "Error: Failed to change directory to the extracted directory." >> "$log_file" 2>&1; exit 1; }

# Extract the .tar.gz file and capture output to log file
echo "Extracting the .tar.gz file..." >> "$log_file" 2>&1
tar -zxvf 2021.05.12.slob_2.5.4.0.tar.gz --overwrite >> "$log_file" 2>&1 || { echo "Error: Failed to extract the .tar.gz file." >> "$log_file" 2>&1; exit 1; }

# Copy SLOB directory to /u02 and capture output to log file
echo "Copying SLOB directory to /u02..." >> "$log_file" 2>&1
cp -rf SLOB/ /u02 >> "$log_file" 2>&1 || { echo "Error: Failed to copy SLOB directory." >> "$log_file" 2>&1; exit 1; }

# Change directory to /u02/SLOB/wait_kit and capture output to log file
echo "Changing directory to /u02/SLOB/wait_kit..." >> "$log_file" 2>&1
cd /u02/SLOB/wait_kit >> "$log_file" 2>&1 || { echo "Error: Failed to change directory to /u02/SLOB/wait_kit." >> "$log_file" 2>&1; exit 1; }

# Make all and capture output to log file
echo "Making all..." >> "$log_file" 2>&1
make all >> "$log_file" 2>&1 || { echo "Error: Failed to make all." >> "$log_file" 2>&1; exit 1; }

echo "SLOB installation completed successfully." >> "$log_file" 2>&1
echo "SLOB installed at: /u02/SLOB" >> "$log_file" 2>&1
echo "Wait kit compiled successfully." >> "$log_file" 2>&1
echo "##############################****END****###########################################" >> "$log_file" 2>&1

#######Enter Oracle SID ##########################
attempts=0
while [ $attempts -lt 3 ]; do
    read -p "Enter Oracle SID: " SID
    read -p "Confirm Oracle SID '$SID'? (yes/no): " confirm_sid
    case $confirm_sid in
        [yY]|[yY][eE][sS])
            ORATAB="/etc/oratab"
            ORACLE_HOME=$(awk -F: "/^$SID:/ {print \$2}" $ORATAB)
            if [ -z "$ORACLE_HOME" ]; then
                ((attempts++))
                if [ $attempts -eq 3 ]; then
                    echo "Reached maximum attempts. Exiting..."
                    exit 1
                else
                    echo "Error: Oracle SID '$SID' not found in $ORATAB. Please try again."
                fi
            else
                break
            fi ;;
        [nN]|[nN][oO])
            continue ;;
        *)
            echo "Invalid input. Please enter 'yes' or 'no'." ;;
    esac
done



# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
# Prompt function for user input
prompt_input() {
  read -p "$1" input
  echo $input
}

# Prompt user for service name
SERVICE_NAME=$(prompt_input "Enter Target Service Name (DB name or PDB name): ")


# Log input values
echo "Service name: $SERVICE_NAME" >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
# Prompt user for port

# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"


echo "Oracle SID : $SID " >> "$log_file" 2>&1
echo "Oracle HOME : $ORACLE_HOME " >>  "$log_file" 2>&1

is_cdb=$($ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF; 2>&1
set heading off
set feedback off
SET PAGES 0
select cdb from v\$database;
exit;
EOF
)

echo "CDB : $is_cdb " >>  "$log_file" 2>&1
if [ $is_cdb == 'YES' ]; then
# Construct the entry to be added

PORT=$(prompt_input "Enter Port Number: ")

# Log input values
echo "Service name: $SERVICE_NAME" >> "$log_file" 2>&1
echo "Port: ${PORT}" >> "$log_file" 2>&1
fi
# Log input values
# Get the IP address using ifconfig (assuming eth0 interface, adjust as necessary)
IP_ADDRESS=$(ifconfig eth0 | grep 'inet ' | awk '{print $2}')

echo "Hostname: $IP_ADDRESS" >> "$log_file" 2>&1
echo "Port: $PORT" >> "$log_file" 2>&1
echo "Service name: $SERVICE_NAME" >> "$log_file" 2>&1
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

echo "Update the Slob.conf file " >> "$log_file" 2>&1

# Set variables
UPDATE_PCT=15
RUN_TIME=3600
SCALE=8G
WORK_UNIT=256
LOAD_PARALLEL_DEGREE=16
DATABASE_STATISTICS_TYPE=awr

# Prompt user for runtime values
# Prompt user for runtime values
INPUT_DBA_PRIV_USER=$(prompt_input "Enter DBA_PRIV_USER (default: master): ")
INPUT_USER_PASSWD=$(prompt_input "Enter USER_PASSWD: ")



# Use default values if input is empty
DBA_PRIV_USER=${INPUT_DBA_PRIV_USER:-master}
USER_PASSWD=${INPUT_USER_PASSWD}


# Use default values if input is empty
ADMIN_SQLNET_SERVICE=$SERVICE_NAME
SQLNET_SERVICE_BASE=$SERVICE_NAME

# Update slob.conf file
CONF_FILE="/u02/SLOB/slob.conf"
sed -i "s/^UPDATE_PCT=.*/UPDATE_PCT=$UPDATE_PCT/" "$CONF_FILE"
sed -i "s/^RUN_TIME=.*/RUN_TIME=$RUN_TIME/" "$CONF_FILE"
sed -i "s/^SCALE=.*/SCALE=$SCALE/" "$CONF_FILE"
sed -i "s/^WORK_UNIT=.*/WORK_UNIT=$WORK_UNIT/" "$CONF_FILE"
sed -i "s/^LOAD_PARALLEL_DEGREE=.*/LOAD_PARALLEL_DEGREE=$LOAD_PARALLEL_DEGREE/" "$CONF_FILE"
sed -i "s/^DATABASE_STATISTICS_TYPE=.*/DATABASE_STATISTICS_TYPE=$DATABASE_STATISTICS_TYPE/" "$CONF_FILE"
sed -i "s/^#ADMIN_SQLNET_SERVICE=.*/ADMIN_SQLNET_SERVICE=$ADMIN_SQLNET_SERVICE/" "$CONF_FILE"
sed -i "s/^#SQLNET_SERVICE_BASE=.*/SQLNET_SERVICE_BASE=$SQLNET_SERVICE_BASE/" "$CONF_FILE"
sed -i "s/^#DBA_PRIV_USER=.*/DBA_PRIV_USER=\"$DBA_PRIV_USER\"/" "$CONF_FILE"
sed -i "s/^#SYSDBA_PASSWD=.*/SYSDBA_PASSWD=\"$SYSDBA_PASSWD\"/" "$CONF_FILE"
sed -i "s/^ADMIN_SQLNET_SERVICE=.*/ADMIN_SQLNET_SERVICE=$ADMIN_SQLNET_SERVICE/" "$CONF_FILE"
sed -i "s/^SQLNET_SERVICE_BASE=.*/SQLNET_SERVICE_BASE=$SQLNET_SERVICE_BASE/" "$CONF_FILE"
sed -i "s/^DBA_PRIV_USER=.*/DBA_PRIV_USER=\"$DBA_PRIV_USER\"/" "$CONF_FILE"
sed -i "s/^SYSDBA_PASSWD=.*/SYSDBA_PASSWD=\"$USER_PASSWD\"/" "$CONF_FILE"
# Display confirmation
echo "Updated slob.conf with the following values:"
echo "UPDATE_PCT: $UPDATE_PCT"
echo "RUN_TIME: $RUN_TIME"
echo "SCALE: $SCALE"
echo "WORK_UNIT: $WORK_UNIT"
echo "LOAD_PARALLEL_DEGREE: $LOAD_PARALLEL_DEGREE"
echo "DATABASE_STATISTICS_TYPE: $DATABASE_STATISTICS_TYPE"
echo "ADMIN_SQLNET_SERVICE: $ADMIN_SQLNET_SERVICE"
echo "SQLNET_SERVICE_BASE: $SQLNET_SERVICE_BASE"
echo "DBA_PRIV_USER: $DBA_PRIV_USER"
echo "SYSDBA_PASSWD: ********"

echo "Updated slob.conf with the following values:" >> "$log_file" 2>&1
echo "UPDATE_PCT: $UPDATE_PCT" >> "$log_file" 2>&1
echo "RUN_TIME: $RUN_TIME" >> "$log_file" 2>&1
echo "SCALE: $SCALE" >> "$log_file" 2>&1
echo "WORK_UNIT: $WORK_UNIT" >> "$log_file" 2>&1
echo "LOAD_PARALLEL_DEGREE: $LOAD_PARALLEL_DEGREE" >> "$log_file" 2>&1
echo "DATABASE_STATISTICS_TYPE: $DATABASE_STATISTICS_TYPE" >> "$log_file" 2>&1
echo "ADMIN_SQLNET_SERVICE: $ADMIN_SQLNET_SERVICE" >> "$log_file" 2>&1
echo "SQLNET_SERVICE_BASE: $SQLNET_SERVICE_BASE" >> "$log_file" 2>&1
echo "DBA_PRIV_USER: $DBA_PRIV_USER" >> "$log_file" 2>&1
echo "SYSDBA_PASSWD: ********" >> "$log_file" 2>&1


# Echo statement to print separator in the log file
echo "################################***END***#########################################" >> "$log_file" 2>&1


# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

echo "Oracle SID : $SID " >> "$log_file" 2>&1
echo "Oracle HOME : $ORACLE_HOME " >>  "$log_file" 2>&1

is_cdb=$($ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF; 2>&1
set heading off
set feedback off
SET PAGES 0
select cdb from v\$database;
exit;
EOF
)
echo "CDB : $is_cdb " >>  "$log_file" 2>&1
if [ $is_cdb == 'YES' ]; then
TABLESPACE_NAME=$(sqlplus -s /nolog <<EOF
connect / as sysdba
set heading off feedback off verify off
alter session set container=$SERVICE_NAME;
select default_tablespace from dba_users where username = upper('$DBA_PRIV_USER');
exit;
EOF
)
else
# Fetch the default tablespace for the given username
TABLESPACE_NAME=$(sqlplus -s /nolog <<EOF
connect / as sysdba
set heading off feedback off verify off
select default_tablespace from dba_users where username = upper('$DBA_PRIV_USER');
exit;
EOF
)
fi
# Remove leading/trailing whitespace from the tablespace name
TABLESPACE_NAME=$(echo $TABLESPACE_NAME | xargs)
echo "TABLESPACE_NAME : $TABLESPACE_NAME " >>  "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1


# Path to the tnsnames.ora file
TNSNAMES_FILE="$ORACLE_HOME/network/admin/tnsnames.ora"

# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"


echo "Oracle SID : $SID " >> "$log_file" 2>&1
echo "Oracle HOME : $ORACLE_HOME " >>  "$log_file" 2>&1

is_cdb=$($ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF; 2>&1
set heading off
set feedback off
SET PAGES 0
select cdb from v\$database;
exit;
EOF
)

echo "CDB : $is_cdb " >>  "$log_file" 2>&1
if [ $is_cdb == 'YES' ]; then
# Construct the entry to be added
ENTRY="
$SERVICE_NAME =
   (DESCRIPTION =
      (ADDRESS = (PROTOCOL = TCP)(HOST = $HOSTNAME)(PORT = $PORT))
      (CONNECT_DATA =
        (SERVER = DEDICATED)
        (SERVICE_NAME = $SERVICE_NAME)
      )
   )
"

# Add the entry to tnsnames.ora file
echo "$ENTRY" >> "$TNSNAMES_FILE"
cat "$TNSNAMES_FILE"  >> "$log_file" 2>&1
echo "Entry added to $TNSNAMES_FILE successfully."

echo "Entry added to $TNSNAMES_FILE successfully." >> "$log_file" 2>&1
fi

tnsping $SERVICE_NAME >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

echo "Choose the database size" >> "$log_file" 2>&1

echo "Choose the database size:"
echo "1. 50GB"
echo "2. 100GB"
echo "3. 200GB"
echo "4. 300GB"
echo "5. 400GB"
echo "6. 500GB"
echo "7. 600GB"
echo "8. 700GB"
echo "9. 800GB"
read -p "Enter your choice (1-9): " choice

case $choice in
    1)
        echo "Running setup for 50GB database..."
	echo "Running setup for 50GB database..." >>  "$log_file" 2>&1
        cd /u02/SLOB
        echo "nohup ./setup.sh $TABLESPACE_NAME 7 &"
	read -p "Press enter to continue or Ctrl+C to cancel." confirm
	echo "nohup ./setup.sh $TABLESPACE_NAME 7 & " >> "$log_file" 2>&1
        nohup ./setup.sh $TABLESPACE_NAME 7  >> "$log_file" 2>&1 &
        ;;
    2)
        echo "Running setup for 100GB database..."
	echo "Running setup for 100GB database..." >>  "$log_file" 2>&1
        cd /u02/SLOB
        echo "nohup ./setup.sh $TABLESPACE_NAME 13 &"
	read -p "Press enter to continue or Ctrl+C to cancel." confirm
	echo "nohup ./setup.sh $TABLESPACE_NAME 13 & " >> "$log_file" 2>&1
        nohup ./setup.sh $TABLESPACE_NAME 13  >> "$log_file" 2>&1 &
        ;;
    3)
        echo "Running setup for 200GB database..."
	echo "Running setup for 200GB database..." >>  "$log_file" 2>&1
        cd /u02/SLOB
        echo "nohup ./setup.sh $TABLESPACE_NAME 25 &"
	read -p "Press enter to continue or Ctrl+C to cancel." confirm
	echo "nohup ./setup.sh $TABLESPACE_NAME 25 & " >> "$log_file" 2>&1
        nohup ./setup.sh $TABLESPACE_NAME 25  >> "$log_file" 2>&1 &
        ;;
    4)
        echo "Running setup for 300GB database..."
	echo "Running setup for 300GB database..." >>  "$log_file" 2>&1
        cd /u02/SLOB
        echo "nohup ./setup.sh $TABLESPACE_NAME 38 &"
	read -p "Press enter to continue or Ctrl+C to cancel." confirm
	echo "nohup ./setup.sh $TABLESPACE_NAME 38 & " >> "$log_file" 2>&1
        nohup ./setup.sh $TABLESPACE_NAME 38 >>  "$log_file" 2>&1 &
        ;;
    5)
        echo "Running setup for 400GB database..."
	echo "Running setup for 400GB database..." >>  "$log_file" 2>&1
        cd /u02/SLOB
        echo "nohup ./setup.sh $TABLESPACE_NAME 50 &"
	read -p "Press enter to continue or Ctrl+C to cancel." confirm
	echo "nohup ./setup.sh $TABLESPACE_NAME 50 & " >> "$log_file" 2>&1
        nohup ./setup.sh $TABLESPACE_NAME 50  >> "$log_file" 2>&1 &
        ;;
    6)
        echo "Running setup for 500GB database..."
	echo "Running setup for 500GB database..." >>  "$log_file" 2>&1
        cd /u02/SLOB
        echo "nohup ./setup.sh $TABLESPACE_NAME 63 &"
	read -p "Press enter to continue or Ctrl+C to cancel." confirm
	echo "nohup ./setup.sh $TABLESPACE_NAME 63 & " >> "$log_file" 2>&1
        nohup ./setup.sh $TABLESPACE_NAME 63  >> "$log_file" 2>&1 &
        ;;
    7)
        echo "Running setup for 600GB database..."
	echo "Running setup for 600GB database..." >>  "$log_file" 2>&1
        cd /u02/SLOB
        echo "nohup ./setup.sh $TABLESPACE_NAME 75 &"
	read -p "Press enter to continue or Ctrl+C to cancel." confirm
	echo "nohup ./setup.sh $TABLESPACE_NAME 75 & " >> "$log_file" 2>&1
        nohup ./setup.sh $TABLESPACE_NAME 75  >> "$log_file" 2>&1 &
        ;;
    8)
        echo "Running setup for 700GB database..."
	echo "Running setup for 700GB database..." >>  "$log_file" 2>&1
        cd /u02/SLOB
        echo "nohup ./setup.sh $TABLESPACE_NAME 88 &"
	read -p "Press enter to continue or Ctrl+C to cancel." confirm
	echo "nohup ./setup.sh $TABLESPACE_NAME 88 & " >> "$log_file" 2>&1
        nohup ./setup.sh $TABLESPACE_NAME 88  >> "$log_file" 2>&1 &
        ;;
    9)
        echo "Running setup for 800GB database..."
	echo "Running setup for 800GB database..." >>  "$log_file" 2>&1
        cd /u02/SLOB
        echo "nohup ./setup.sh $TABLESPACE_NAME 100 &"
	read -p "Press enter to continue or Ctrl+C to cancel." confirm
	echo "nohup ./setup.sh $TABLESPACE_NAME 100 & " >> "$log_file" 2>&1
        nohup ./setup.sh $TABLESPACE_NAME 100  >> "$log_file" 2>&1 &
        ;;
    *)
        echo "Invalid choice. Please enter a number between 1 and 9."
	echo "Invalid choice. Please enter a number between 1 and 9." >> "$log_file" 2>&1 &
        ;;
esac
echo "#################################***END***########################################" >> "$log_file" 2>&1