#!/bin/bash
#######Enter Oracle SID ##########################
attempts=0
while [ $attempts -lt 3 ]; do
    read -p "Enter Oracle SID: " SID
    read -p "Confirm Oracle SID '$SID'? (yes/no): " confirm_sid
    case $confirm_sid in
        [yY]|[yY][eE][sS])
            ORATAB="/etc/oratab"
            ORACLE_HOME=$(awk -F: "/^$SID:/ {print \$2}" $ORATAB)
            if [ -z "$ORACLE_HOME" ]; then
                ((attempts++))
                if [ $attempts -eq 3 ]; then
                    echo "Reached maximum attempts. Exiting..."
                    exit 1
                else
                    echo "Error: Oracle SID '$SID' not found in $ORATAB. Please try again."
                fi
            else
                break
            fi ;;
        [nN]|[nN][oO])
            continue ;;
        *)
            echo "Invalid input. Please enter 'yes' or 'no'." ;;
    esac
done

####################Enter backup location ###################################
# Function to prompt for backup directory and confirmation
confirm_backup_dir() {
    read -p "Enter backup directory: " backup_dir
    read -p "Confirm backup directory '$backup_dir'? (yes/no): " confirm_backup_dir
    case $confirm_backup_dir in
        [yY]|[yY][eE][sS])
            return 0 ;;
        [nN]|[nN][oO])
            return 1 ;;
        *)
            echo "Invalid input. Please enter 'yes' or 'no'."
            return 1 ;;
    esac
}

# Ask for backup directory with a maximum of two attempts
attempts=0
while [ $attempts -lt 2 ]; do
    if confirm_backup_dir; then
        full_backup_dir="${backup_dir}/${SID}"
        mkdir -p "$full_backup_dir"
        break
    else
        ((attempts++))
        if [ $attempts -eq 2 ]; then
            echo "Reached maximum attempts. Exiting..."
            exit 1
        fi
    fi
done

# Create backup directory if it doesn't exist
#full_backup_dir="${backup_dir}/${SID}"
#mkdir -p "$full_backup_dir"

# Create the RMAN script
rm -rf "$full_backup_dir"/*
file_path="$full_backup_dir/${SID}_backup_script.rcv"
log_path="$full_backup_dir/${SID}_backup.log"

# Echo statement to print separator in the log file
echo "################################################" >> "$log_path" 2>&1

echo "Log file: $log_path"
echo "Log file: $log_path" >> "$log_path" 2>&1

# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
echo "Oracle SID : $SID"
echo "Oracle HOME : $ORACLE_HOME"

echo "Oracle SID : $SID " >> "$file_path" 2>&1
echo "Oracle HOME : $ORACLE_HOME " >>  "$file_path" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_path" 2>&1
echo "Taking pfile Backup from spfile" >> "$log_path" 2>&1

PFILE="$full_backup_dir/init$SID.ora"

# Taking pfile Backup from spfile
echo "Creating pfile from spfile..." >> "$log_path" 2>&1
sqlplus -S / as sysdba <<EOF >> "$log_path" 2>&1
    create pfile='$PFILE' from spfile;
    exit;
EOF

echo "Pfile location : ${PFILE}"  >>  "$log_path" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_path" 2>&1
echo "Taking wallet Backup " >> "$log_path" 2>&1

# Query to check wallet status and location
wallet_query="SELECT WRL_PARAMETER FROM v\$ENCRYPTION_WALLET WHERE STATUS='OPEN' AND WALLET_TYPE='AUTOLOGIN';"
wallet_location=$(sqlplus -S / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
$wallet_query
EXIT;
EOF
)

if [ -z "$wallet_location" ]; then
    echo "Error: Wallet is not open or not configured as AUTOLOGIN." >> "$log_path" 2>&1
    echo "Proceeding without wallet backup..." >> "$log_path" 2>&1
else
    echo "Wallet location: $wallet_location" >> "$log_path" 2>&1
    # Copy wallet files to backup directory
    echo "Copying wallet files to backup directory..." >> "$log_path" 2>&1
    cp -r "$wallet_location" "$full_backup_dir"
    if [ $? -eq 0 ]; then
        echo "Wallet files copied successfully to $full_backup_dir" >> "$log_path" 2>&1
    else
        echo "Error: Failed to copy wallet files." >> "$log_path" 2>&1
        exit 1
    fi
fi
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_path" 2>&1

echo "Log file: $log_path" >> "$log_path" 2>&1
# Print the values entered by the user
echo "Backup Directory: $full_backup_dir" >> "$log_path" 2>&1
echo "SID: $SID" >> "$log_path" 2>&1
echo "ORACLE_HOME: $ORACLE_HOME" >> "$log_path" 2>&1

# Get the number of CPU cores
num_cores=$(nproc --all)
echo "Number of CPU cores: $num_cores"  >> "$log_path"

# Calculate the number of RMAN channels, limiting to a maximum of 12 and a minimum of 1
max_channels=12
min_channels=1
num_channels=$((num_cores - 2))
if [ "$num_channels" -gt "$max_channels" ]; then
    num_channels="$max_channels"
elif [ "$num_channels" -lt "$min_channels" ]; then
    num_channels="$min_channels"
fi

echo "Number of Channels: $num_channels"
echo "Number of Channels: $num_channels" >> "$log_path"

echo "run {" > "$file_path"
# Loop to allocate channels
for ((i = 1; i <= num_channels; i++)); do
  echo "  ALLOCATE CHANNEL c${i} DEVICE TYPE DISK FORMAT '${full_backup_dir}/backup_${SID}_%U' MAXPIECESIZE 30G;" >> "$file_path"
done
# Backup operations
echo "  BACKUP AS COPY CURRENT CONTROLFILE FORMAT '${full_backup_dir}/ctl_${SID}_%U';" >> "$file_path"
echo "  BACKUP SPFILE FORMAT '${full_backup_dir}/spfile_${SID}_%U';" >> "$file_path"
echo "  BACKUP AS COMPRESSED BACKUPSET INCREMENTAL LEVEL 0 CHECK LOGICAL DATABASE PLUS ARCHIVELOG;" >> "$file_path"


# Loop to release channels
for ((i = 1; i <= num_channels; i++)); do
  echo "  RELEASE CHANNEL c${i};" >> "$file_path"
done
echo "}" >> "$file_path"
echo "exit;" >> "$file_path"

echo "RMAN script created at: $file_path"
# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

echo "export ORACLE_SID="$SID" " >> "$log_path"
echo "export ORACLE_HOME="$ORACLE_HOME" " >> "$log_path"


# Run the RMAN backup script in nohup
nohup rman target / nocatalog cmdfile="$file_path" >> "$log_path" 2>&1 &
echo "RMAN ${SID} database backup log file created at: $log_path"