#!/bin/sh

total_memory_size=0
dbs=`awk -F'[:]' 'BEGIN { ORS=" " }; NF && $1!~/^#/ {new_var=$1","$2; print new_var}' /etc/oratab`
IFS=' ' read -r -a databases <<< "$dbs"
for index in "${!databases[@]}"
do
	database=${databases[$index]};
	IFS=',' read -r -a database_parts <<< "$database"
    echo "Oracle SID is ${database_parts[0]}"
    echo "Oracle Home is ${database_parts[1]}"
    export ORACLE_SID=${database_parts[0]}
	export ORACLE_HOME=${database_parts[1]}

	sga_max_size=`echo "select value from v\\$parameter where name='sga_max_size';" | $ORACLE_HOME/bin/sqlplus -S / as sysdba`
	sga_max_size=${sga_max_size##*$'\n'}
	echo "sga_max_size found from database is $sga_max_size"
if [[ $sga_max_size != "" ]]; then
	sga_max_memory_size=`echo "$(($sga_max_size/1024 ))"`
	echo "Total memory for Hugepages for $ORACLE_SID in KB is $sga_max_memory_size"
	sga_max_memory_size=`echo "$(($sga_max_memory_size + ${sga_max_memory_size} * 15/100 ))"`
	echo "Total memory with 15% Buffer for Hugepages for $ORACLE_SID in KB is $sga_max_memory_size"

	total_memory_size=`echo "$(($total_memory_size + $sga_max_memory_size ))"`
else
	echo "Could not retrive sga_max_size for $ORACLE_SID...skipping database"	
fi
    
done

echo "Total memory for Hugepages in KB is $total_memory_size"
#Find size of each hugepage
hugepage_size=`grep Hugepagesize /proc/meminfo | awk {'print $2'}`
#Number of huge pages would be the DB memory / page size
nr_huge_pages=`echo $(( total_memory_size / $hugepage_size ))`
echo "Total No of Hugepages needed is $nr_huge_pages"

echo "Configuring HugePages in server..."

sudo sysctl -w vm.nr_hugepages=$nr_huge_pages
sudo sed -i "/^vm.nr_hugepages*/d" /etc/sysctl.conf
sudo echo "vm.nr_hugepages=$nr_huge_pages" | sudo tee -a /etc/sysctl.conf

mem_lock=`echo "$nr_huge_pages*2048" | bc | awk -F"." '{print $1}'`
sudo echo "oracle           soft    memlock         $mem_lock" | sudo tee -a /etc/security/limits.conf
sudo echo "oracle           hard    memlock         $mem_lock" | sudo tee -a /etc/security/limits.conf
sudo /sbin/sysctl -p

echo "Configured HugePages in server"
exit 0