#!/bin/bash

# Define log file
LOG_FILE="db_link_check.log"
INVALID_DB_LINKS="invalid_db_links.txt"

# Function to check database links
check_db_links() {
    while IFS= read -r db_link; do
        echo "Checking database link: $db_link"
        result=$(timeout 5s sqlplus -s / as sysdba <<EOF
        SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
        select * from dual@$db_link;
        EXIT;
EOF
)
        if [[ $? -eq 0 ]]; then
            if [[ $result == "X" ]]; then
                echo "Database link $db_link is valid." >> "$LOG_FILE"
            else
                echo "Database link $db_link is not valid." >> "$LOG_FILE"
                echo "$db_link" >> "$INVALID_DB_LINKS"
            fi
        else
            echo "Timeout: Database link $db_link not responsive." >> "$LOG_FILE"
        fi
    done < <(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
select db_link from dba_db_links;
EXIT;
EOF
)
}

# Log in to SQL*Plus
echo "Checking database links..."
check_db_links
echo "Database link check complete."