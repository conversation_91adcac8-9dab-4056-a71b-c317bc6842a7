import requests,base64,sys,json,logging,copy,os,subprocess
from tessell.plugin.database.oracle.tasks.discover.DiscoverOracleDB import DiscoverDatabase
from tessell.plugin.database.oracle.tasks.connection.OracleConnection import OracleConnection

logging.getLogger().addHandler(logging.StreamHandler(sys.stdout))
logging.getLogger().setLevel(logging.INFO)

LOG_SWEEP_CONFIG_FILE = "/opt/tessell-base/.tessell/log_sweep/logSweepConfig.json"
tenant_id=service_id=service_instance_id=compute_resource_id=""

def read_log_sweep_config():
    f = open(LOG_SWEEP_CONFIG_FILE,)
    logSweepConfig = json.load(f)
    logSweepConfig = logSweepConfig["log_sweep_config"]
    services = logSweepConfig["services"] if 'services' in logSweepConfig else None
    if services:
        oracle_home = services[service_id]['softwareHome']
        oracle_port = services[service_id]['service_port']
        oracle_sid = services[service_id]['dbName']
    else:
        oracle_home = logSweepConfig['softwareHome']
        oracle_port = logSweepConfig['service_port']
        oracle_sid = logSweepConfig['dbName']
    tenant_id = logSweepConfig['tenantId']
    compute_resource_id = logSweepConfig['computeResourceId']
    return oracle_home,oracle_port,tenant_id,oracle_sid,compute_resource_id

def get_metadata(get_endpoint, service="tessell-database-system"):
    modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}
    # Get data from Tessell Agent (/rpc endpoint)
    rpc_data = {
        "rpcName": "get_data",
        "service": service,
        "port": 8080,
        "method": "GET",
        "endpoint": get_endpoint,
        "headers": modified_headers,
        "timeout": 900,
    }

    response_from_request = requests.post(
        "http://localhost:8081/rpc",
        data=json.dumps(rpc_data),
        headers = {"Content-type": "application/json"},
        verify=False,
        timeout=900,
    )

    if response_from_request.status_code:
        print("get metadata response status code: " + str(response_from_request.status_code))

    response_json = json.loads(response_from_request.content.decode())
    response_json = base64.b64decode(response_json['payload']).decode('utf-8')
    response_json = json.loads(response_json)
    print(f"response_json is {json.dumps(response_json)}")
    return response_json

def update_metadata(update_endpoint, data, request="POST"):
    data = json.dumps(data, ensure_ascii=False)
    data = data.encode()
    modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}
    rpc_data = {
        "rpcName": "update_tessell_service",
        "service": "tessell-database-system",
        "port": 8080,
        "method": request,
        "endpoint": update_endpoint,
        "payload": base64.b64encode(data).decode("utf-8"),
        "headers": modified_headers,
        "timeout": 900,
    }
    response_from_request = None
    response_from_request = requests.post(
        "http://localhost:8081/rpc",
        data=json.dumps(rpc_data),
        headers = {"Content-type": "application/json"},
        verify=False,
        timeout=900,
    )
    
    if response_from_request.status_code:
        print(f"update metadata response status code: " +str(response_from_request.status_code))
    return response_from_request

def update_service(database_info_json, old_oracle_sid, master_user):
    get_endpoint = f"/tessell-ops/services/{service_id}"
    print(f"Getting service info ...")
    db_service = get_metadata(get_endpoint)
    service_connect_descriptor = db_service['connectivityInfo']['userView']['connectStrings'][0]['connectDescriptor']
    db_service['connectivityInfo']['userView']['connectStrings'][0]['connectDescriptor'] = service_connect_descriptor.replace(old_oracle_sid,database_info_json['oracle_sid'])
    db_service['connectivityInfo']['userView']['connectStrings'][0]['masterUser'] = master_user
    update_endpoint=f"/tessell-ops/services/{service_id}"
    print(f"Updating service info with {db_service}...")
    update_metadata(update_endpoint, db_service, request="PATCH")
    
def update_service_instance(database_info_json, old_oracle_sid, master_user):
    get_endpoint = f"/tessell-ops/services/{service_id}/service-instances/{service_instance_id}"
    print(f"Getting service instance info ...")
    dbservice_instance = get_metadata(get_endpoint)
    service_instance_connect_descriptor = dbservice_instance['connectionInfo']['connectString']['connectDescriptor']
    dbservice_instance['connectionInfo']['connectString']['connectDescriptor'] = service_instance_connect_descriptor.replace(old_oracle_sid,database_info_json['oracle_sid'])
    dbservice_instance['connectionInfo']['connectString']['masterUser'] = master_user
    update_endpoint=f"/tessell-ops/services/{service_id}/service-instances/{service_instance_id}"
    print(f"Updating service instance info with {dbservice_instance}...")
    update_metadata(update_endpoint, dbservice_instance, request="PATCH")
    
def update_service_instance_metadata(database_info_json, data_volume_id, archive_volume_id, data_disk_mount_point):
    get_endpoint = f"/tessell-ops/services/{service_id}/service-instances/{service_instance_id}/metadata/version/latest"
    print(f"Getting service instance metadata info ...")
    service_instance_metadata_json = get_metadata(get_endpoint)
    service_instance_metadata_json['metadata']['data']['database_parameters']['database_metadata'] = database_info_json['database_metadata']
    service_instance_metadata_json['metadata']['data']['database_parameters']['db_size'] = database_info_json['db_size']
    service_instance_metadata_json['metadata']['data']['database_parameters']['oracle_sid'] = database_info_json['oracle_sid']
    volumes = service_instance_metadata_json['metadata']['data']['volumes']
    for volume in volumes:
        if data_volume_id and volumes[volume]['type'] == 'DB_DATA':
            service_instance_metadata_json['metadata']['data']['volumes'][volume]['cloud_resource_id'] = data_volume_id
        if archive_volume_id and volumes[volume]['type'] == 'ARCHIVE_DATA':
            service_instance_metadata_json['metadata']['data']['volumes'][volume]['cloud_resource_id'] = archive_volume_id
        if data_disk_mount_point and volumes[volume]['type'] == 'DB_DATA':
            service_instance_metadata_json['metadata']['data']['volumes'][volume]['mount_point'] = data_disk_mount_point
    service_instance_metadata_json['version'] = service_instance_metadata_json['version']+1
    update_endpoint=f"/tessell-ops/services/{service_id}/service-instances/{service_instance_id}/metadata"
    print(f"Updating service instance metadata with {json.dumps(service_instance_metadata_json)}...")
    update_metadata(update_endpoint, service_instance_metadata_json)
    
def update_databases(database_info_json, master_user):
    if database_info_json['database_metadata']['database_parameters']['basic_info']['is_cdb']:
        #update all pdbs to tessell_databases
        get_endpoint = f"/tessell-ops/services/{service_id}/databases"
        print(f"Getting databases info ...")
        dbservice_databases = get_metadata(get_endpoint)['response']
        pdb_base = dbservice_databases[0]
        existing_pdb_list={}
        existing_pdb_set = set()
        vm_pdb_set = set()
        for pdb in dbservice_databases:
            if pdb['status'] not in ['CREATING','DELETING','DELETED']:
                existing_pdb_list[pdb['name']] = pdb
                existing_pdb_set.update((pdb['name'],))
        for pdb in database_info_json['database_metadata']['database_parameters']['cdb_info']['pdb_list']:
            if '$' not in pdb['name']:
                vm_pdb_set.update((pdb['name'],))
        create_pdbs = set()
        delete_pdbs = set()
        create_pdbs = vm_pdb_set - existing_pdb_set
        delete_pdbs = existing_pdb_set - vm_pdb_set
        for delete_pdb in delete_pdbs:
            delete_endpoint=f"/tessell-ops/services/{service_id}/databases/{existing_pdb_list[delete_pdb]['id']}"
            print(f"Deleting PDB {delete_pdb}...")
            delete_metadata(delete_endpoint)

        for create_pdb in create_pdbs:
            new_pdb = copy.deepcopy(pdb_base)
            new_pdb.pop('id')
            new_pdb['name'] = create_pdb
            new_pdb['status'] = 'READY'
            new_pdb['tessellCreated'] = False
            connect_desc = database_info_json['database_metadata']['database_parameters']['db_connect_params']['pdb_connect_desc']
            connect_desc = connect_desc.replace("PDB"+database_info_json['oracle_sid'],create_pdb)
            new_pdb['driverInfo']['connectString']['connectDescriptor'] = connect_desc
            new_pdb['driverInfo']['connectString']['masterUser'] = master_user
            update_endpoint=f"/tessell-ops/services/{service_id}/databases"
            print(f"Adding new PDB with data {new_pdb}...")
            update_metadata(update_endpoint, new_pdb, request="POST")                        
    else:
        get_endpoint = f"/tessell-ops/services/{service_id}/databases"
        print(f"Getting databases info ...")
        dbservice_databases = get_metadata(get_endpoint)['response']
        dbservice_databases[0]['name'] = database_info_json['oracle_sid']
        update_endpoint=f"/tessell-ops/services/{service_id}/databases/{dbservice_databases[0]['id']}"
        print(f"Updating service database info with {dbservice_databases[0]}...")
        update_metadata(update_endpoint, dbservice_databases[0],request="PATCH")
    
def update_logsweep(old_oracle_sid,database_info_json):
    print("Updating SID in logsweep config file")
    f = open(LOG_SWEEP_CONFIG_FILE,)
    logSweepConfig = json.load(f)
    f.close()
    services = logSweepConfig["log_sweep_config"]["services"] if 'services' in logSweepConfig["log_sweep_config"] else None
    if services:
        logSweepConfig["log_sweep_config"]['services'][service_id]['dbName'] = database_info_json['oracle_sid']
    else:
        logSweepConfig["log_sweep_config"]['dbName'] = database_info_json['oracle_sid']
    print(logSweepConfig)        
    with open(LOG_SWEEP_CONFIG_FILE, "w") as jsonFile:
        json.dump(logSweepConfig, jsonFile)
    jsonFile.close()

    print("Updating Logsweep is_primary config file")
    cmd=f'sudo mv /usr/bin/is_primary_{old_oracle_sid} /usr/bin/is_primary_{database_info_json["oracle_sid"]}'
    print("Please run below command as user who has sudo access...")
    print(cmd)    

def update_db_exporter(old_oracle_sid, database_info_json):
    print("Updating SID & DBSNMP password in DB Exporter config file")    
    with open('/opt/tessell/db-exporter/sql_exporter.yml') as f:
        lines = f.read().splitlines()
    newfilecontents = []
    for line in lines:
        if service_instance_id in line:
            # line = re.sub("dbsnmp:*([\S]+)@localhost", 'dbsnmp:'+dbsnmp_user_pass+'@localhost', line)
            line = line.replace(old_oracle_sid,database_info_json['oracle_sid'])
        newfilecontents.append(line)
    print(newfilecontents)
    cmd=f'sudo mv /opt/tessell/db-exporter/sql_exporter.yml /opt/tessell/db-exporter/sql_exporter.yml.bkp'
    print("Please run below command as user who has sudo access...")
    print(cmd)    
    for line in newfilecontents:
        cmd=f'sudo echo "{line}" | sudo tee -a /opt/tessell/db-exporter/sql_exporter.yml'
    print("Please run below command as user who has sudo access...")
    print(cmd)        
    print("Please restart db-exporter for configuration change to take effect...")
    
def delete_metadata(update_endpoint):
    modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}
    rpc_data = {
        "rpcName": "update_tessell_service",
        "service": "tessell-database-system",
        "port": 8080,
        "method": "DELETE",
        "endpoint": update_endpoint,
        "payload": None,
        "headers": modified_headers,
        "timeout": 900,
    }
    response_from_request = None
    response_from_request = requests.post(
        "http://localhost:8081/rpc",
        data=json.dumps(rpc_data),
        headers = {"Content-type": "application/json"},
        verify=False,
        timeout=900,
    )
    
    if response_from_request.status_code:
        print(f"update metadata response status code: " +str(response_from_request.status_code))
    return response_from_request

def update_dbsnmp_password(database_info_json,dbsnmp_user_pass):
    print("Resetting DBSNMP password in database")
    query = f"alter user dbsnmp identified by \"{dbsnmp_user_pass}\" account unlock"
    oracle_db_connection = OracleConnection(connect_params=database_info_json['database_metadata']['database_parameters']['db_connect_params'])
    oracle_db_connection.execute(query, fetch_res=False)

def update_wallet_location(database_info_json):
    query = f"select WRL_PARAMETER from  v$ENCRYPTION_WALLET where status='OPEN' and WALLET_TYPE='AUTOLOGIN'"
    oracle_db_connection = OracleConnection(connect_params=database_info_json['database_metadata']['database_parameters']['db_connect_params'])
    result = oracle_db_connection.execute(query)
    if len(result) and len(result[0]):
        result_tuple = result[0]
        wallet_path = result_tuple[0]
        if wallet_path:
            database_info_json['database_metadata']['database_parameters']['db_connect_params']['wallet_path'] = wallet_path[:-1]

def update_storage_mapping(data_volume_id, archive_volume_id):
    get_endpoint = f"/tessell-ops/services/{service_id}/storages?page-size=1000"
    print(f"Getting service mapping info ...")
    db_storage_mapping = get_metadata(get_endpoint)['response']
    update_endpoint=f"/tessell-ops/services/{service_id}/storages"
    for storage in db_storage_mapping:
        if storage['storagePurpose'] == 'DB_DATA' and data_volume_id:
            storage['cloudLocation'] = data_volume_id
            update_storage_data(storage['tessellStorageId'], data_volume_id)
            update_metadata(update_endpoint, storage, request="POST")
        if storage['storagePurpose'] == 'ARCHIVE_DATA' and archive_volume_id:
            storage['cloudLocation'] = archive_volume_id
            update_storage_data(storage['tessellStorageId'], archive_volume_id)
            update_metadata(update_endpoint, storage, request="POST")

def update_storage_data(storage_id, disk_volume_id):
    get_endpoint = f"/tessell-ops/storages/{storage_id}"
    print(f"Getting storage info ...")
    storage_metadata = get_metadata(get_endpoint)
    storage_metadata["cloudResourceId"] = disk_volume_id    
    update_endpoint=f"/tessell-ops/storages/{storage_id}"
    print(f"Updating storage info with {storage_metadata}...")
    update_metadata(update_endpoint, storage_metadata, request="PATCH")

def get_mandatory_inputs(host_endpoint, monitor_endpoint, master_user, ssl_enabled):
    print(f"Getting mandatory inputs ...")
    get_endpoint = f"/tessell-ops/services/{service_id}"
    print(f"Getting service info ...")
    db_service = get_metadata(get_endpoint)    
    if not host_endpoint:
        host_endpoint = db_service['connectivityInfo']['userView']['connectStrings'][0]['endpoint']
    if not monitor_endpoint:
        get_endpoint = f"/tessell-ops/compute-resources/{compute_resource_id}"
        print(f"Getting compute resource info ...")
        compute_resource = get_metadata(get_endpoint)
        fqdns = compute_resource['machineFqdnInfo']["data"][service_id]["fqdns"]
        for fqdn in fqdns:
            fqdn_info = fqdns[fqdn]
            if fqdn_info['tag'] == 'monitor_fqdn':
                monitor_endpoint = fqdn
    if not master_user:
        master_user = db_service['connectivityInfo']['userView']['connectStrings'][0]['masterUser']
    if not ssl_enabled:
        ssl_enabled = db_service['connectivityInfo']['userView']['enableSSL']        
    return host_endpoint, monitor_endpoint, master_user, ssl_enabled

def create_master_user(database_info_json,master_user):
    print("Creating Master user in database...")
    get_endpoint = f"/tessell-ops/secret-store/secret?secret-type=TENANT_ASSET&key-name=db--oracle--masterPwd--"+service_id
    master_user_secret = get_metadata(get_endpoint,service="tessell-security")
    master_user_pass = master_user_secret['secret']['value']
    
    oracle_db_connection = OracleConnection(connect_params=database_info_json['database_metadata']['database_parameters']['db_connect_params'])
    if database_info_json['database_metadata']['database_parameters']['basic_info']['is_cdb']:
        oracle_db_connection.execute(f'alter session set "_ORACLE_SCRIPT"=true', fetch_res=False)
    try:
        oracle_db_connection.execute(f"create user {master_user} identified by \"{master_user_pass}\"", fetch_res=False)
    except Exception as e:
        print(f"Ignoring user creation error...")        
    oracle_db_connection.execute(f"GRANT AQ_ADMINISTRATOR_ROLE,AQ_USER_ROLE,CONNECT,CTXAPP,DBA,EXECUTE_CATALOG_ROLE,RECOVERY_CATALOG_OWNER,RESOURCE,SELECT_CATALOG_ROLE TO {master_user}", fetch_res=False)
    oracle_db_connection.execute(f"GRANT ALTER DATABASE LINK, ALTER PUBLIC DATABASE LINK, DROP ANY DIRECTORY, EXEMPT ACCESS POLICY, EXEMPT IDENTITY POLICY, GRANT ANY OBJECT PRIVILEGE, RESTRICTED SESSION, EXEMPT REDACTION POLICY TO {master_user}", fetch_res=False)
    if database_info_json['database_metadata']['database_parameters']['basic_info']['is_cdb']:
        for pdb in database_info_json['database_metadata']['database_parameters']['cdb_info']['pdb_list']:
            if '$' not in pdb['name']:
                print(f"Creating Master user in pluggable database {pdb['name']}...")
                oracle_db_connection.execute(f"alter session set container = {pdb['name']}", fetch_res=False)
                try:
                    oracle_db_connection.execute(f"create user {master_user} identified by \"{master_user_pass}\"", fetch_res=False)
                except Exception as e:
                    print(f"Ignoring user creation error...")        
                oracle_db_connection.execute(f"GRANT AQ_ADMINISTRATOR_ROLE,AQ_USER_ROLE,CONNECT,CTXAPP,DBA,EXECUTE_CATALOG_ROLE,RECOVERY_CATALOG_OWNER,RESOURCE,SELECT_CATALOG_ROLE TO {master_user}", fetch_res=False)
                oracle_db_connection.execute(f"GRANT ALTER DATABASE LINK, ALTER PUBLIC DATABASE LINK, DROP ANY DIRECTORY, EXEMPT ACCESS POLICY, EXEMPT IDENTITY POLICY, GRANT ANY OBJECT PRIVILEGE, RESTRICTED SESSION, EXEMPT REDACTION POLICY TO {master_user}", fetch_res=False)
    
if __name__ == "__main__":
    service_id = sys.argv[1]
    service_instance_id = sys.argv[2]
    old_oracle_sid = sys.argv[3] if len(sys.argv) > 3 else None
    new_oracle_sid = sys.argv[4] if len(sys.argv) > 4 else None
    data_disk_mount_point = sys.argv[5] if len(sys.argv) > 5 else None    
    host_endpoint = sys.argv[6] if len(sys.argv) > 6 else None
    monitor_endpoint = sys.argv[7] if len(sys.argv) > 7 else None
    master_user = sys.argv[8] if len(sys.argv) > 8 else None
    ssl_enabled = sys.argv[9] if len(sys.argv) > 9 else None
    data_disk_volume_id = sys.argv[10] if len(sys.argv) > 10 else None
    archive_disk_volume_id = sys.argv[11] if len(sys.argv) > 11 else None

    sid_change = False
    oracle_home,oracle_port,tenant_id,oracle_dbsid,compute_resource_id = read_log_sweep_config()
    if not old_oracle_sid and not new_oracle_sid:
        oracle_sid = oracle_dbsid
    else:
        if old_oracle_sid != new_oracle_sid:
            oracle_sid = new_oracle_sid
            sid_change = True
        else:
            oracle_sid = oracle_dbsid

    host_endpoint,monitor_endpoint,master_user,ssl_enabled = get_mandatory_inputs(host_endpoint, monitor_endpoint, master_user, ssl_enabled)
    get_endpoint = f"/tessell-ops/secret-store/secret?secret-type=TENANT_ASSET&key-name=db--oracle--dbsnmpPwd--"+service_id
    dbsnmp_user_secret = get_metadata(get_endpoint,service="tessell-security")
    dbsnmp_user_pass = dbsnmp_user_secret['secret']['value']

    discover_db = DiscoverDatabase()
    kwargs = {}
    kwargs['oracle_home'] = oracle_home
    kwargs['oracle_sid'] = oracle_sid
    kwargs['db_user'] = 'sys'
    kwargs['oracle_user'] = 'oracle'
    kwargs['sysdba'] = 'true'
    kwargs['listener_port'] = str(oracle_port)
    kwargs['host_ip'] = host_endpoint
    kwargs['monitor_endpoint'] = monitor_endpoint
    kwargs['connect_user'] = master_user
    kwargs['ssl_enabled'] = True if ssl_enabled == 'True' else False
    if ssl_enabled:
        kwargs['non_ssl_monitor_port'] = '1599'
    kwargs['connect_pass'] = dbsnmp_user_pass
    kwargs['work_directory'] = "/tmp/tessell-logs/discover"
    database_info_json = discover_db.execute(**kwargs)
    print(f"json extracted from database is {json.dumps(database_info_json)}...")

    update_wallet_location(database_info_json)
    if database_info_json['database_metadata']['database_parameters']['basic_info']['database_role'] == "PRIMARY":
        update_dbsnmp_password(database_info_json,dbsnmp_user_pass)
    if sid_change:
        update_service(database_info_json, old_oracle_sid, master_user)
        update_service_instance(database_info_json, old_oracle_sid, master_user)
    update_service_instance_metadata(database_info_json, data_disk_volume_id, archive_disk_volume_id, data_disk_mount_point)
    if data_disk_volume_id or archive_disk_volume_id:
        update_storage_mapping(data_disk_volume_id, archive_disk_volume_id)
    update_databases(database_info_json, master_user)
    if sid_change:
        update_logsweep(old_oracle_sid,database_info_json)
        update_db_exporter(old_oracle_sid, database_info_json)
    create_master_user(database_info_json,master_user)
