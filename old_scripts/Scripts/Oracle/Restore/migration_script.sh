#!/bin/bash

export SID=$1
export BACKUP_LOCATION=$2
export PFILE=$3
export CONTROLFILE=$4
export PORT=$5
export log_file=$6
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

echo "#### Check if the Oracle database is up and running ####" >> "$log_file" 2>&1
# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
echo "Oracle SID : $SID"
echo "Oracle HOME : $ORACLE_HOME"

echo "Oracle SID : $SID " >> "$log_file" 2>&1
echo "Oracle HOME : $ORACLE_HOME " >>  "$log_file" 2>&1

if ! "$ORACLE_HOME/bin/sqlplus" -s "/ as sysdba" <<EOF
exit
EOF
then
    echo "Error: Unable to connect to the database. Make sure the Oracle database is running." >> "$log_file" 2>&1
    exit 1
fi

# Drop the Oracle database
echo "#### Drop the Oracle database ####" >> "$log_file" 2>&1
echo "Dropping the Oracle database..." 
# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
"$ORACLE_HOME/bin/sqlplus" -S "/ as sysdba" << EOF >> "$log_file" 2>&1
shutdown immediate;
startup mount exclusive restrict;
drop database;
exit;
EOF

echo "Oracle database dropped successfully...." >> "$log_file" 2>&1
echo "Oracle database dropped successfully...." 

# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
"$ORACLE_HOME/bin/sqlplus" -S "/ as sysdba" << EOF >> "$log_file" 2>&1
shutdown abort;
exit;
EOF

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

# Convert SID to uppercase
SID1=$(echo "$SID" | tr '[:lower:]' '[:upper:]')

# Location for Datafile files Location
DATA_DIR="/u02/app/oracle/oradata_${SID}/data/${SID1}"
export DATA_DIR="/u02/app/oracle/oradata_${SID}/data/${SID1}"
# Location for Archive file Location
ARCH_DIR="/u02/app/oracle/oraarchive/${SID1}"
export ARCH_DIR="/u02/app/oracle/oraarchive/${SID1}"
# Location for FRA Location
FRA_DIR="/u02/app/oracle/oradata_${SID}/fra/${SID1}"
export FRA_DIR="/u02/app/oracle/oradata_${SID}/fra/${SID1}"
# Location for redo log files
REDO_DIR="/u02/app/oracle/oradata_${SID}/data/${SID1}"
export REDO_DIR="/u02/app/oracle/oradata_${SID}/data/${SID1}"


echo "Datafile Directory : $DATA_DIR " >> "$log_file" 2>&1
echo "Archive Directory : $ARCH_DIR " >>  "$log_file" 2>&1
echo "Fra Directory : $FRA_DIR " >> "$log_file" 2>&1
echo "Redo Directory : $REDO_DIR " >>  "$log_file" 2>&1




# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

# Remove all old files from DATA_DIR
echo "#### Remove all old files from DATA_DIR ####" >> "$log_file" 2>&1
rm -rf "$DATA_DIR"/*
echo "rm -rf $DATA_DIR/*"  >> "$log_file" 2>&1
echo "All files removed from $DATA_DIR" >> "$log_file" 2>&1

# Remove all old files from FRA_DIR
echo "#### Remove all old files from FRA_DIR ####" >> "$log_file" 2>&1
rm -rf "$FRA_DIR"/*
echo "rm -rf $FRA_DIR/*"  >> "$log_file" 2>&1
echo "All files removed from $FRA_DIR" >> "$log_file" 2>&1

# Remove all old files from ARCH_DIR
echo "#### Remove all archive files from ARCH_DIR ####" >> "$log_file" 2>&1
rm -rf "$ARCH_DIR"/*
echo "rm -rf $ARCH_DIR/*"  >> "$log_file" 2>&1
echo "All files removed from $ARCH_DIR" >> "$log_file" 2>&1


# Create directories if they don't exist
echo "############### Creating necessary directories ##################" >> "$log_file" 2>&1
mkdir -p "$DATA_DIR" >> "$log_file" 2>&1
mkdir -p "$ARCH_DIR" >> "$log_file" 2>&1
mkdir -p "$FRA_DIR" >> "$log_file" 2>&1

echo "Created necessary directories" >> "$log_file" 2>&1
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1


# Additional steps
echo "Oracle SID : $SID " >> "$log_file" 2>&1
echo "Oracle HOME : $ORACLE_HOME " >>  "$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

# Path to the pfile
echo "Pfile Location : $PFILE " >>  "$log_file" 2>&1

export ORACLE_BASE="/u02/app/oracle" 
export FRA_DIR="/u02/app/oracle/oradata_${SID}/fra/${SID1}"

# Create adump directory if it doesn't exist
echo "################## Creating adump directory ###############################" >> "$log_file" 2>&1
mkdir -p "$ORACLE_BASE/admin/${SID}/adump" >> "$log_file" 2>&1
echo "Created audit_file_dest: $ORACLE_BASE/admin/$SID/adump" >> "$log_file" 2>&1

export FRA_DIR="/u02/app/oracle/oradata_${SID}/fra/${SID1}" 
# Replace *.audit_file_dest lines with desired format
echo "################# Updating pfile ###############################" >> "$log_file" 2>&1


sed -i -E "
           s|^(\*\.audit_file_dest=).*|\1'$ORACLE_BASE/admin/$SID/adump'|;
           s|^(\*\.diagnostic_dest=).*|\1'$ORACLE_BASE'|;
           s|^(\*\.db_recovery_file_dest=).*|\1'$FRA_DIR'|;
           s|^${SID}1\.undo_tablespace=(.*)|\*\.undo_tablespace=\1|;
           /^\*\.log_file_name_convert/d;
           /^\*\.db_file_name_convert/d;
           /^\*\.cluster_database/d;
           /^\*\.control_files/d;
           /^\*\.remote_listener/d;
           /^$SID/d;
          " "$PFILE" >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

# Start Oracle database in nomount mode
echo "############ Start Oracle database in nomount mode from pfile ###########" >> "$log_file" 2>&1
echo "Starting the Oracle database in nomount mode from pfile: $PFILE" >> "$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
$ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF >> "$log_file" 2>&1
startup nomount pfile='$PFILE';
exit;
EOF

echo "start_database_nomount" >>"$log_file" 2>&1

# Echo statement to print separator in the log file
echo "######### Creating spfile from pfile #######################" >> "$log_file" 2>&1

# Create spfile from pfile
echo "Creating spfile from pfile..."  >>"$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
$ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF  >>"$log_file" 2>&1
create spfile from pfile='$PFILE';
exit;
EOF

# Echo statement to print separator in the log file
echo "########## Stopping the oracle database ##################" >> "$log_file" 2>&1

echo "Stopping Oracle database..." >> "$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
# Use Oracle SID variable
$ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF >> "$log_file" 2>&1
shutdown immediate;
exit;
EOF

echo "stop_database..." >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "############ Starting the database from spfile in nomount ##############" >> "$log_file" 2>&1
# Start Oracle database in nomount mode
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
echo "Starting the Oracle database in nomount mode from spfile " >> "$log_file" 2>&1
$ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF >> "$log_file" 2>&1
startup nomount;
exit;
EOF

echo "started database in nomount" >>"$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
# Function to calculate db_recovery_file_dest_size
calculate_db_recovery_file_dest_size() {
    # Get the available space in DATA_DIR
    DATA_DIR_SIZE=$(df -kh "${DATA_DIR}" | awk 'NR==2{print $4}')

    # Calculate half of DATA_DIR size
    DB_RECOVERY_FILE_DEST_SIZE=$(echo "${DATA_DIR_SIZE}" | awk '{ sub(/[A-Za-z]/, "", $1); printf "%.0f", $1 / 2 }')

    echo "DB_RECOVERY_FILE_DEST_SIZE: ${DB_RECOVERY_FILE_DEST_SIZE}"
}

# Function to get host from server
get_host() {
    HOST=$(hostname)
    echo "Host: ${HOST}"
}


# Function to calculate db_recovery_file_dest_size
calculate_db_recovery_file_dest_size() {
    # Get the available space in DATA_DIR
    DATA_DIR_SIZE=$(df -k "$DATA_DIR" | awk 'NR==2{print $4}')

    # Calculate half of DATA_DIR size
    DB_RECOVERY_FILE_DEST_SIZE=$(echo "$DATA_DIR_SIZE" | awk '{ sub(/[A-Za-z]/, "", $1); printf "%.0f", $1 / 2 }')

    echo "DB_RECOVERY_FILE_DEST_SIZE: $DB_RECOVERY_FILE_DEST_SIZE"
}

# Function to get host from server
get_host() {
    HOST=$(hostname)
    echo "Host: $HOST"
}

# Call the functions
calculate_db_recovery_file_dest_size
get_host


# Echo statement to print separator in the log file
echo "############Updating the spfile #######################" >> "$log_file" 2>&1

# Echo SQL commands to log file before execution
echo "alter system set control_files='${DATA_DIR}/control01.ctl','${FRA_DIR}/control02.ctl','${ARCH_DIR}/control03.ctl' scope=spfile;" >> "$log_file" 2>&1
echo "alter system set db_recovery_file_dest='${FRA_DIR}' scope=spfile;" >> "$log_file" 2>&1
echo "alter system set db_recovery_file_dest_size=${DB_RECOVERY_FILE_DEST_SIZE}K scope=spfile;" >> "$log_file" 2>&1
echo "alter system set dg_broker_start=false scope=spfile;" >> "$log_file" 2>&1
echo "alter system set local_listener='(address=(protocol=tcp)(host=${HOST})(port=$PORT))' SCOPE=spfile;" >> "$log_file" 2>&1
echo "ALTER SYSTEM SET log_archive_dest_1='location=${ARCH_DIR}' SCOPE=spfile;" >> "$log_file" 2>&1
echo "alter system set standby_file_management=manual scope=spfile;" >> "$log_file" 2>&1
echo "alter system set cluster_database=FALSE scope=spfile;" >> "$log_file" 2>&1
echo "alter system set diagnostic_dest='/u02/app/oracle' scope=spfile;" >> "$log_file" 2>&1
echo "alter system set audit_file_dest='/u02/app/oracle/admin/${SID}/adump' scope=spfile;" >> "$log_file" 2>&1
echo "alter system set filesystemio_options=none scope=spfile;" >> "$log_file" 2>&1

echo "Executing SQL*Plus commands..." >> "$log_file" 2>&1

export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
sqlplus -S / as sysdba <<EOF >> "$log_file" 2>&1
    alter system set control_files='${DATA_DIR}/control01.ctl','${FRA_DIR}/control02.ctl','${ARCH_DIR}/control03.ctl' scope=spfile;
    alter system set db_recovery_file_dest='${FRA_DIR}' scope=spfile;
    alter system set db_recovery_file_dest_size=${DB_RECOVERY_FILE_DEST_SIZE}K scope=spfile;
    alter system set dg_broker_start=false scope=spfile;
    alter system set local_listener='(address=(protocol=tcp)(host=${HOST})(port=$PORT))' SCOPE=spfile;
    ALTER SYSTEM SET log_archive_dest_1='location=${ARCH_DIR}' SCOPE=spfile;
    alter system set standby_file_management=manual scope=spfile;
    alter system set cluster_database=FALSE scope=spfile;
    alter system set diagnostic_dest='/u02/app/oracle' scope=spfile;
    alter system set audit_file_dest='/u02/app/oracle/admin/${SID}/adump' scope=spfile;
    alter system set filesystemio_options=none scope=spfile;
    exit;
EOF

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
echo "Stopping Oracle database..." >> "$log_file" 2>&1
# Use Oracle SID variable
"$ORACLE_HOME"/bin/sqlplus -S / as sysdba << EOF >> "$log_file" 2>&1
shutdown immediate;
exit;
EOF


echo "stop_database..." >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
# Start Oracle database in nomount mode
echo "Starting the Oracle database in nomount mode from spfile " >> "$log_file" 2>&1
$ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF >> "$log_file" 2>&1
startup nomount;
exit;
EOF

echo "started database in nomount" >>"$log_file" 2>&1


# Echo statement to print separator in the log file
echo "###################copy the wallate######################################################" >> "$log_file" 2>&1


# Create directory in /home/<USER>/tessell/ using Oracle SID
mkdir -p "/home/<USER>/tessell/$SID/"

# Copy the 'wallate' directory from the backup location to the newly created directory
cp -r "$BACKUP_LOCATION/wallet" "/home/<USER>/tessell/$SID/"
cp -r "$BACKUP_LOCATION/wallet" "$DATA_DIR/"

echo "cp -r "$BACKUP_LOCATION/wallet" "/home/<USER>/tessell/$SID/" " >> "$log_file" 2>&1
echo "cp -r "$BACKUP_LOCATION/wallet" "$DATA_DIR/" " >> "$log_file" 2>&1
# Path to the tnsnames.ora file
SQLNET_FILE="$ORACLE_HOME/network/admin/sqlnet.ora"
# Construct the entry to be added
ENTRY="
ENCRYPTION_WALLET_LOCATION =
 (SOURCE =
   (METHOD = FILE)
     (METHOD_DATA =
       (DIRECTORY = /home/<USER>/tessell/\$ORACLE_SID/wallet)
    )
 )

"

# Add the entry to tnsnames.ora file
echo "$ENTRY" >> "$SQLNET_FILE"
cat "$SQLNET_FILE"  >> "$log_file" 2>&1
echo "Entry added to $SQLNET_FILE successfully."

echo "Entry added to $SQLNET_FILE successfully." >> "$log_file" 2>&1



echo "#########################################################################" >> "$log_file" 2>&1
backup_dir="${BACKUP_LOCATION}" 
echo "backup_dir : "$backup_dir" " >> "$log_file" 2>&1
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

echo "Restoring the controlfile........ " >> "$log_file" 2>&1
echo "Restoring the controlfile........ "
# Create the RMAN restore  script

rm -rf "$backup_dir/${SID}_control_script.rcv"
control_path="$backup_dir/${SID}_control_script.rcv"

echo "run {" > "$control_path"
echo "restore controlfile from '$CONTROLFILE';"  >> "$control_path"
echo "}" >> "$control_path"
echo "exit;" >> "$control_path"

echo "RMAN  restore script created at: $control_path" >> "$log_file"  2>&1

# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

echo "export ORACLE_SID="$SID" " >> "$log_file"  2>&1
echo "export ORACLE_HOME="$ORACLE_HOME" " >> "$log_file"  2>&1
# Run the RMAN backup script in nohup
nohup rman target / nocatalog cmdfile="$control_path"  >> "$log_file" 2>&1 & 

# Wait for the RMAN command to complete
PID=$!
wait $PID



echo "Control file restore completed ........ " >> "$log_file" 2>&1
echo "Control file restore completed........ "

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

# Start Oracle database in mount mode 
# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
echo "Starting the Oracle database in nomount to mount mode  " >> "$log_file" 2>&1
$ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF >> "$log_file" 2>&1
alter database mount;
exit;
EOF

echo "started database in mount" >>"$log_file" 2>&1


# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "########## checking status of wallet ##################" >> "$log_file" 2>&1

echo "checking status of wallet..." >> "$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
# Use Oracle SID variable
$ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF >> "$log_file" 2>&1
select WRL_PARAMETER from v\$ENCRYPTION_WALLET where status='OPEN' and WALLET_TYPE='AUTOLOGIN';
select wrl_type wallet,status,wrl_parameter wallet_location from v\$encryption_wallet;
exit;
EOF

echo "checking status of wallet..." >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
# Oracle Database Backup directory

export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

echo "Deleting  expired backup and archivelog and CATALOG Database........ " >> "$log_file" 2>&1
echo "Deleting  expired backup and archivelog and CATALOG Database...... "
# Create the RMAN restore  script
rm -rf "$backup_dir/${SID}_p_script.rcv"
p_path="$backup_dir/${SID}_p_script.rcv"

echo "run {" > "$p_path"
echo "CONFIGURE ARCHIVELOG DELETION POLICY TO NONE;" >> "$p_path"
echo "CONFIGURE CHANNEL DEVICE TYPE DISK CLEAR;" >> "$p_path"
echo "CONFIGURE SNAPSHOT CONTROLFILE NAME CLEAR;" >> "$p_path"
echo "CONFIGURE CHANNEL DEVICE TYPE DISK CLEAR;" >> "$p_path"
echo "CONFIGURE CHANNEL DEVICE TYPE 'SBT_TAPE' CLEAR;" >> "$p_path"
echo "show DEVICE TYPE;" >> "$p_path"
echo "CONFIGURE DEVICE TYPE 'SBT_TAPE' CLEAR;" >> "$p_path"
echo "CONFIGURE CHANNEL DEVICE TYPE DISK CLEAR;" >> "$p_path"
echo "CONFIGURE CONTROLFILE AUTOBACKUP FORMAT FOR DEVICE TYPE DISK TO '%F';" >> "$p_path"
echo "configure retention policy clear;" >> "$p_path"
echo "CONFIGURE ARCHIVELOG DELETION POLICY clear;" >> "$p_path"
echo "CONFIGURE DEFAULT DEVICE TYPE TO DISK;" >> "$p_path"
echo "CONFIGURE DEVICE TYPE DISK PARALLELISM 2;"  >> "$p_path"
echo "crosscheck backup of database;" >> "$p_path"
echo "delete noprompt expired backup;" >> "$p_path"
echo "delete OBSOLETE;" >> "$p_path"
echo "delete noprompt expired archivelog all;" >> "$p_path"
echo "CATALOG START WITH '$backup_dir' noprompt;" >> "$p_path"
echo "}" >> "$p_path"
echo "exit;" >> "$p_path"

echo "RMAN  restore script created at: $p_path"  >> "$log_file"  2>&1
# Export ORACLE_SID and ORACLE_HOME

export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

echo "export ORACLE_SID="$SID" " >> "$log_file"  2>&1
echo "export ORACLE_HOME="$ORACLE_HOME" " >> "$log_file"  2>&1
# Run the RMAN backup script in nohup
nohup rman target / nocatalog cmdfile="$p_path"  >> "$log_file" 2>&1 & 

# Wait for the RMAN command to complete
PID=$!
wait $PID

echo "Delete completed  expired backup and archivelog and CATALOG Database done ........ " >> "$log_file" 2>&1
echo "Delete completed  expired backup and archivelog and CATALOG Database done...... "

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

# Export ORACLE_SID and ORACLE_HOME
# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
# Set new datafile location

echo "export ORACLE_SID="$SID" " >> "$log_file"  2>&1
echo "export ORACLE_HOME="$ORACLE_HOME" " >> "$log_file"  2>&1


# Database connection details

NEW_DATAFILE_LOCATION="$DATA_DIR"

echo " $NEW_DATAFILE_LOCATION " >> "$log_file"  2>&1

# Database connection details

# Function to execute SQL query
execute_sql() {
    sqlplus -S / as sysdba <<EOF  
SET LONG 999999
SET ECHO OFF
SET PAGESIZE 0
SET LINESIZE 5000
SET HEADING OFF
SET FEEDBACK OFF
SET VERIFY OFF
SET TRIMSPOOL ON
$1
    exit;
EOF
}

# Set new datafile location

mkdir -p "$NEW_DATAFILE_LOCATION/pdbseed"
# Get PDB names from the database
pdb_names=$(execute_sql "SELECT name FROM v\$pdbs;")
echo "$pdb_names"

# Create directory for each PDB
echo "Creating directories for PDBs..."
while IFS= read -r pdb_name; do
    pdb_name=$(echo "$pdb_name" | tr -d '[:space:]' | tr '[:upper:]' '[:lower:]') # Remove any whitespace characters and convert to lowercase
    # Skip creating directory for PDB$SEED
    if [ "$pdb_name" = "pdb\$seed" ]; then
        echo "Skipping directory creation for PDB$SEED"
        continue
    fi

    mkdir -p "${NEW_DATAFILE_LOCATION}/${pdb_name}"
    echo "Created directory for PDB: ${NEW_DATAFILE_LOCATION}/${pdb_name}"
done <<< "$pdb_names"

echo "Directory creation completed."

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
# Set new datafile location

echo "export ORACLE_SID="$SID" " >> "$log_file"  2>&1
echo "export ORACLE_HOME="$ORACLE_HOME" " >> "$log_file"  2>&1


# Database connection details

NEW_DATAFILE_LOCATION="$DATA_DIR"

echo " $NEW_DATAFILE_LOCATION " >> "$log_file"  2>&1


# Function to execute SQL query
execute_sql() {
    sqlplus -S / as sysdba <<EOF
    $1
    exit;
EOF
}

# Redirect output to output.sql file

exec 3>&1
exec > output.sql

# Set SQL*Plus settings
echo "SET LONG 999999"
echo "SET ECHO OFF"
echo "SET PAGESIZE 0"
echo "SET LINESIZE 5000"
echo "SET HEADING OFF"
echo "SET FEEDBACK OFF"
echo "SET VERIFY OFF"
echo "SET TRIMSPOOL ON"

# Start the SQL command
echo "SELECT"
echo "    CASE"
# Execute SQL query to get PDB names
execute_sql "SELECT name AS pdb_name FROM v\$pdbs;" | while IFS= read -r PDB_NAME; do
    # Remove $ character from PDB_NAME
    PDB_NAME=$(echo "$PDB_NAME" | tr -d '$')
    # Skip if PDB_NAME is empty or contains only spaces
    if [ -z "$(echo -e "${PDB_NAME}" | tr -d '[:space:]')" ]; then
        continue
    fi
     # Convert PDB_NAME to lowercase
    PDB_NAME_LOWER=$(echo "$PDB_NAME" | tr '[:upper:]' '[:lower:]')
    # Convert PDB_NAME to uppercase
    PDB_NAME_UPPER=$(echo "$PDB_NAME" | tr '[:lower:]' '[:upper:]')
    # Extract the first three characters of PDB_NAME in uppercase
    PDB_NAME_PREFIX=$(echo "$PDB_NAME" | cut -c1-3 | tr '[:lower:]' '[:upper:]')
    # Extract the rest of PDB_NAME in lowercase
    PDB_NAME_SUFFIX=$(echo "$PDB_NAME" | cut -c4- | tr '[:upper:]' '[:lower:]')
    # Combine PDB_NAME_PREFIX and PDB_NAME_SUFFIX
    PDB_NAME_COMBINED="${PDB_NAME_PREFIX}${PDB_NAME_SUFFIX}"
    # Construct the SQL command for datafiles
    SQL_COMMAND_DATAFILE=$(cat <<EOF
        WHEN name LIKE '%${PDB_NAME}%'  OR name LIKE '%${PDB_NAME_LOWER}%' OR name LIKE '%${PDB_NAME_COMBINED}%' THEN 'SET NEWNAME FOR DATAFILE ' || FILE# || ' to ''' || '${NEW_DATAFILE_LOCATION}/${PDB_NAME_LOWER}' || substr(name, instr(name, '/', -1)) || ''';'
EOF
)
    # Print the SQL command for datafiles
    echo "$SQL_COMMAND_DATAFILE"
done
# Print the remaining SQL command for datafiles
echo "    ELSE 'SET NEWNAME FOR DATAFILE ' || FILE# || ' to ''' || '${NEW_DATAFILE_LOCATION}' || substr(name, instr(name, '/', -1)) || ''';'"
echo "    END"
echo "FROM v\$datafile;"

# Start the SQL command for tempfiles
echo "SELECT"
echo "    CASE"
# Execute SQL query to get PDB names
execute_sql "SELECT name AS pdb_name FROM v\$pdbs;" | while IFS= read -r PDB_NAME; do
    # Remove $ character from PDB_NAME
    PDB_NAME=$(echo "$PDB_NAME" | tr -d '$')
    # Skip if PDB_NAME is empty or contains only spaces
    if [ -z "$(echo -e "${PDB_NAME}" | tr -d '[:space:]')" ]; then
        continue
    fi
     # Convert PDB_NAME to lowercase
    PDB_NAME_LOWER=$(echo "$PDB_NAME" | tr '[:upper:]' '[:lower:]')
    # Convert PDB_NAME to uppercase
    PDB_NAME_UPPER=$(echo "$PDB_NAME" | tr '[:lower:]' '[:upper:]')
    # Extract the first three characters of PDB_NAME in uppercase
    PDB_NAME_PREFIX=$(echo "$PDB_NAME" | cut -c1-3 | tr '[:lower:]' '[:upper:]')
    # Extract the rest of PDB_NAME in lowercase
    PDB_NAME_SUFFIX=$(echo "$PDB_NAME" | cut -c4- | tr '[:upper:]' '[:lower:]')
    # Combine PDB_NAME_PREFIX and PDB_NAME_SUFFIX
    PDB_NAME_COMBINED="${PDB_NAME_PREFIX}${PDB_NAME_SUFFIX}"
    # Construct the SQL command for tempfiles
    SQL_COMMAND_TEMPFILE=$(cat <<EOF
        WHEN name LIKE '%${PDB_NAME}%'  OR name LIKE '%${PDB_NAME_LOWER}%' OR name LIKE '%${PDB_NAME_COMBINED}%' THEN 'SET NEWNAME FOR TEMPFILE ' || FILE# || ' to ''' || '${NEW_DATAFILE_LOCATION}/${PDB_NAME_LOWER}' || substr(name, instr(name, '/', -1)) || ''';'
EOF
)
    # Print the SQL command for tempfiles
    echo "$SQL_COMMAND_TEMPFILE"
done

# Print the remaining SQL command for tempfiles
echo "    ELSE 'SET NEWNAME FOR TEMPFILE ' || FILE# || ' to ''' || '${NEW_DATAFILE_LOCATION}' || substr(name, instr(name, '/', -1)) || ''';'"
echo "    END"
echo "FROM v\$tempfile;"

# Add  EXIT command
echo "EXIT;"
exec 1>&3
exec 3>&-
#exec >&-
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
rm -rf "$backup_dir/${SID}_restore_script.rcv"
r_path="$backup_dir/${SID}_restore_script.rcv"

# Get the number of CPU cores
num_cores=$(nproc --all)
#echo "Number of CPU cores: $num_cores"
echo "Number of CPU cores: $num_cores"  >> "$log_file" 2>&1
# Calculate the number of RMAN channels, limiting to a maximum of 12 and a minimum of 1
max_channels=12
min_channels=1
num_channels=$((num_cores - 2))
if [ "$num_channels" -gt "$max_channels" ]; then
    num_channels="$max_channels"
elif [ "$num_channels" -lt "$min_channels" ]; then
    num_channels="$min_channels"
fi

#exec 1>&1  # Reopen stdout
echo "Number of Channels: $num_channels"
echo "Number of Channels: $num_channels"    >> "$log_file" 2>&1

echo "run {" > "$r_path"
# Loop to allocate channels
for ((i = 1; i <= num_channels; i++)); do
  echo "  ALLOCATE CHANNEL c${i} DEVICE TYPE DISK FORMAT '${backup_dir}/backup_${SID}_%U';" >> "$r_path"
done
# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
"$ORACLE_HOME/bin/sqlplus" -S "/ as sysdba" @output.sql  >> "$r_path"  2>&1
# Backup operations
echo " CONFIGURE DEVICE TYPE DISK BACKUP TYPE TO COMPRESSED BACKUPSET; " >> "$r_path"
echo " show all; " >> "$r_path"
echo "restore database; " >> "$r_path"
echo " SWITCH DATAFILE ALL;" >> "$r_path"
echo " SWITCH TEMPFILE ALL; " >> "$r_path"
# Loop to release channels
for ((i = 1; i <= num_channels; i++)); do
  echo "  RELEASE CHANNEL c${i};" >> "$r_path"
done
echo "}" >> "$r_path"
echo "exit;" >> "$r_path"

echo "RMAN  restore script created at: $r_path"

echo "RMAN  restore script created at: $r_path" >> "$log_file"  2>&1
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

echo "Restore ${SID} the Oracle database..." 
# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

echo "export ORACLE_SID="$SID" " >> "$log_file" 2>&1
echo "export ORACLE_HOME="$ORACLE_HOME" " >> "$log_file" 2>&1
# Run the RMAN backup script in nohup
nohup rman target / nocatalog cmdfile="$r_path"  >> "$log_file" 2>&1 &
PID=$!
wait $PID
echo "RMAN ${SID} database restore progress....... " >> "$log_file" 2>&1
echo "RMAN ${SID} database restore progress ...... "

# Echo statement to print separator in the log file
echo "#############Rename the redo logfile#####################" >> "$log_file" 2>&1
# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
# Set new datafile location

echo "export ORACLE_SID="$SID" " >> "$log_file"  2>&1
echo "export ORACLE_HOME="$ORACLE_HOME" " >> "$log_file"  2>&1


# Database connection details

NEW_DATAFILE_LOCATION="$DATA_DIR"

echo " $NEW_DATAFILE_LOCATION "
echo " $NEW_DATAFILE_LOCATION " >> "$log_file"  2>&1
# Redirect output to output.sql file
exec 3>&1
exec > redo.sql


# Execute SQL query to generate the rename commands
sqlplus -S / as sysdba <<EOF
SET LONG 999999
SET ECHO OFF
SET PAGESIZE 0
SET LINESIZE 5000
SET HEADING OFF
SET FEEDBACK OFF
SET VERIFY OFF
SET TRIMSPOOL ON
SET LINESIZE 1400
SET PAGESIZE 20000
SET HEADING OFF
-- Generate rename commands starting from 10
SELECT 'ALTER DATABASE RENAME FILE ''' || MEMBER || ''' TO '''
  || '${NEW_DATAFILE_LOCATION}/redo.' || (10 + ROWNUM - 1) || '.log'';'
FROM V\$LOGFILE;
EXIT;
EOF

# exit print to redo.sql
echo "exit;" >> "redo.sql"
# Reset the output redirection
exec 1>&3
exec 3>&-

export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
"$ORACLE_HOME/bin/sqlplus" -S "/ as sysdba" @redo.sql  >> "$log_file" 2>&1
# Print a completion message to log file
echo "Redo log file rename commands have been generated successfully."

echo "Redo log file rename commands have been generated successfully. " >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
rm -rf "$backup_dir/${SID}_recover_script.rcv"
recover_path="$backup_dir/${SID}_recover_script.rcv"

# Get the number of CPU cores
num_cores=$(nproc --all)
#echo "Number of CPU cores: $num_cores"
echo "Number of CPU cores: $num_cores"  >> "$log_file" 2>&1
# Calculate the number of RMAN channels, limiting to a maximum of 12 and a minimum of 1
max_channels=12
min_channels=1
num_channels=$((num_cores - 2))
if [ "$num_channels" -gt "$max_channels" ]; then
    num_channels="$max_channels"
elif [ "$num_channels" -lt "$min_channels" ]; then
    num_channels="$min_channels"
fi

#exec 1>&1  # Reopen stdout
#echo "Number of Channels: $num_channels"
echo "Number of Channels: $num_channels"    >> "$log_file" 2>&1

echo "run {" > "$recover_path"
# Loop to allocate channels
for ((i = 1; i <= num_channels; i++)); do
  echo "  ALLOCATE CHANNEL c${i} DEVICE TYPE DISK FORMAT '${backup_dir}/backup_${SID}_%U';" >> "$recover_path"
done

echo "recover database; " >> "$recover_path"
# Loop to release channels
for ((i = 1; i <= num_channels; i++)); do
  echo "  RELEASE CHANNEL c${i};" >> "$recover_path"
done
echo "}" >> "$recover_path"
echo "exit;" >> "$recover_path"

echo "RMAN  recover script created at: $recover_path"

echo "RMAN  recover script created at: $recover_path" >> "$log_file"  2>&1
# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1


echo "Recover ${SID} the Oracle database..." 
# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

echo "export ORACLE_SID="$SID" " >> "$log_file" 2>&1
echo "export ORACLE_HOME="$ORACLE_HOME" " >> "$log_file"  2>&1
# Run the RMAN backup script in nohup
nohup rman target / nocatalog cmdfile="$recover_path"  >> "$log_file" 2>&1 &
PID=$!
wait $PID
echo "RMAN ${SID} database recovery completed....... " >> "$log_file" 2>&1
echo "RMAN ${SID} database recovery completed...... "

# Echo statement to print separator in the log file
echo "##############Starting the database in read write mode ######################" >> "$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

# Start Oracle database in nomount mode
echo "Starting the Oracle database in open mode from spfile " >> "$log_file" 2>&1
$ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF >> "$log_file" 2>&1
 set pagesize 300
  set linesize 300
  col FORCE_LOGGING for a16
  alter database open resetlogs;
  ALTER PLUGGABLE DATABASE ALL SAVE STATE;
  shut immediate;
  startup;
   set pagesize 300
  set linesize 300
  col FORCE_LOGGING for a16
  select NAME,LOG_MODE,OPEN_MODE,PROTECTION_MODE,DATABASE_ROLE,FORCE_LOGGING,FLASHBACK_ON,DB_UNIQUE_NAME from v\$database;
  show pdbs;
exit;
EOF

# Echo statement to print separator in the log file
echo "##################Starting the LISTENER #####################" >> "$log_file" 2>&1


# Echo statement to print separator in the log file
echo "################### Adding Redo Log Files #############################" >> "$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
# Function to get the size of a redo log file
get_redo_log_size() {
  sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT TO_CHAR(BYTES/1024/1024) FROM V\$LOG WHERE ROWNUM = 1;
EXIT;
EOF
}

# Function to count the number of redo log files where THREAD#=1
count_redo_logs_thread1() {
  sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT COUNT(*) FROM V\$LOG WHERE THREAD#=1;
EXIT;
EOF
}

# Execute the functions and store the outputs
REDO_LOG_SIZE=$(get_redo_log_size)
REDO_LOG_SIZE=$(echo $REDO_LOG_SIZE | xargs) # Remove any leading/trailing spaces

REDO_LOG_COUNT=$(count_redo_logs_thread1)
REDO_LOG_COUNT=$(echo $REDO_LOG_COUNT | xargs) # Remove any leading/trailing spaces

# Check if the outputs were retrieved successfully
if [ -z "$REDO_LOG_SIZE" ]; then
  echo "Failed to retrieve redo log size." >> "$log_file" 2>&1
  exit 1
fi

if [ -z "$REDO_LOG_COUNT" ]; then
  echo "Failed to retrieve redo log count." >> "$log_file" 2>&1
  exit 1
fi

# Start adding new redo log files from GROUP# 100
START_GROUP_NUMBER=100

# Add new redo log files
for ((i=0; i<$REDO_LOG_COUNT; i++))
do
  NEW_GROUP_NUMBER=$((START_GROUP_NUMBER + i))
  ADD_REDO_LOG_SQL="ALTER DATABASE ADD LOGFILE GROUP $NEW_GROUP_NUMBER ('$DATA_DIR/redo${NEW_GROUP_NUMBER}.log', '$ARCH_DIR/redo${NEW_GROUP_NUMBER}.log') SIZE ${REDO_LOG_SIZE}M;"

  # Execute the SQL command to add the redo log group and log the output
  echo "Executing: $ADD_REDO_LOG_SQL" >> "$log_file" 2>&1
  sqlplus -s / as sysdba <<EOF >> "$log_file" 2>&1
  $ADD_REDO_LOG_SQL
  EXIT;
EOF

  # Check if the SQL command was executed successfully
  if [ $? -eq 0 ]; then
    echo "Redo log group $NEW_GROUP_NUMBER added successfully with size ${REDO_LOG_SIZE}M." >> "$log_file" 2>&1
  else
    echo "Failed to add redo log group $NEW_GROUP_NUMBER." >> "$log_file" 2>&1
    exit 1
  fi
done

# Echo statement to print separator in the log file
echo "#####################################################################" >> "$log_file" 2>&1


# Echo statement to print separator in the log file
echo "################Drop the Redo log file#############################" >> "$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"
# Function to disable threads 2 to 6 and perform a checkpoint
disable_threads() {
  echo "Disabling threads 2 to 6 and performing checkpoint..." >> "$log_file" 2>&1
  sqlplus -s / as sysdba <<EOF >> "$log_file" 2>&1
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF
ALTER DATABASE DISABLE THREAD 2;
ALTER DATABASE DISABLE THREAD 3;
ALTER DATABASE DISABLE THREAD 4;
ALTER DATABASE DISABLE THREAD 5;
ALTER DATABASE DISABLE THREAD 6;
ALTER SYSTEM CHECKPOINT;
EXIT;
EOF
  echo "Threads disabled and checkpoint performed." >> "$log_file" 2>&1
}

# Function to switch log file and perform checkpoint
switch_log_and_checkpoint() {
  echo "Switching log file and performing checkpoint..." >> "$log_file" 2>&1
  sqlplus -s / as sysdba <<EOF >> "$log_file" 2>&1
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF
ALTER SYSTEM SWITCH LOGFILE;
ALTER SYSTEM CHECKPOINT;
EXIT;
EOF
  echo "Log file switched and checkpoint performed." >> "$log_file" 2>&1
}

# Function to drop redo log files where GROUP# < 100
drop_redo_logs_less_than_100() {
  echo "Dropping redo log files where GROUP# < 100..." >> "$log_file" 2>&1
  sqlplus -s / as sysdba <<EOF >> "$log_file" 2>&1
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF
DECLARE
  v_group_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO v_group_count FROM V\$LOG WHERE GROUP# < 100;

  IF v_group_count > 0 THEN
    FOR i IN (SELECT GROUP# FROM V\$LOG WHERE GROUP# < 100) LOOP
      BEGIN
        EXECUTE IMMEDIATE 'ALTER DATABASE DROP LOGFILE GROUP ' || i.GROUP#;
        DBMS_OUTPUT.PUT_LINE('Redo log group ' || i.GROUP# || ' dropped successfully.');
      EXCEPTION
        WHEN OTHERS THEN
          DBMS_OUTPUT.PUT_LINE('Failed to drop redo log group ' || i.GROUP# || ': ' || SQLERRM);
      END;
    END LOOP;
  ELSE
    DBMS_OUTPUT.PUT_LINE('No redo log groups to drop.');
  END IF;
END;
/
EXIT;
EOF
  echo "Redo log files dropped where GROUP# < 100." >> "$log_file" 2>&1
}

# Execute the functions sequentially
disable_threads
switch_log_and_checkpoint
drop_redo_logs_less_than_100

# Check if any redo log groups are left and retry dropping if necessary
v_group_count=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF
SELECT COUNT(*) FROM V\$LOG WHERE GROUP# < 100;
EXIT;
EOF
)

v_group_count=$(echo $v_group_count | xargs) # Remove any leading/trailing spaces

while [[ $v_group_count -gt 0 ]]; do
  echo "Retry dropping redo log groups..." >> "$log_file" 2>&1
  switch_log_and_checkpoint
  drop_redo_logs_less_than_100
  v_group_count=$(sqlplus -s / as sysdba <<EOF
  SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF
  SELECT COUNT(*) FROM V\$LOG WHERE GROUP# < 100;
  EXIT;
EOF
  )
  v_group_count=$(echo $v_group_count | xargs) # Remove any leading/trailing spaces
done

# Echo statement to print separator in the log file
echo "################################################" >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "##############Starting the database in read write mode ######################" >> "$log_file" 2>&1
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

# Start Oracle database in nomount mode
echo "Starting the Oracle database in open mode from spfile " >> "$log_file" 2>&1
$ORACLE_HOME/bin/sqlplus -S / as sysdba <<EOF >> "$log_file" 2>&1
 set pagesize 300
 set linesize 300
 col FORCE_LOGGING for a16
 select NAME,LOG_MODE,OPEN_MODE,PROTECTION_MODE,DATABASE_ROLE,FORCE_LOGGING,FLASHBACK_ON,DB_UNIQUE_NAME from v\$database;
 show pdbs;
 set linesize 300
 set pagesize 300
 col MEMBER for a75
 select v.group#,thread#,bytes/1024/1024,l.type,l.member,v.status from v\$log v ,v\$logfile l where v.group#=l.group#;
set linesize 300
set pagesize 300
col MEMBER for a75
select v.group#,thread#,bytes/1024/1024,l.type,l.member,v.status from v\$standby_log v ,v\$logfile l where v.group#=l.group#;

exit;
EOF

# Echo statement to print separator in the log file
echo "##################################################" >> "$log_file" 2>&1
