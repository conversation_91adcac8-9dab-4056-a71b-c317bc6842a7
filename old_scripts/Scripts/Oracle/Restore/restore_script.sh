#!/bin/bash

# Get the current date and time
current_datetime=$(date +"%Y%m%d%H%M%S")
current_date=$(date +"%Y%m%d")
current_time=$(date +"%H:%M:%S")

# Directory to store output
output_dir="/tmp"

# Create directory if it doesn't exist
mkdir -p "$output_dir"

attempts=0
while [ $attempts -lt 3 ]; do
    read -p "Enter Oracle SID: " SID
    read -p "Confirm Oracle SID '$SID'? (yes/no): " confirm_sid
    case $confirm_sid in
        [yY]|[yY][eE][sS])
            ORATAB="/etc/oratab"
            ORACLE_HOME=$(awk -F: "/^$SID:/ {print \$2}" $ORATAB)
            if [ -z "$ORACLE_HOME" ]; then
                ((attempts++))
                if [ $attempts -eq 3 ]; then
                    echo "Reached maximum attempts. Exiting..."
                    exit 1
                else
                    echo "Error: Oracle SID '$SID' not found in $ORATAB. Please try again."
                fi
            else
                break
            fi ;;
        [nN]|[nN][oO])
            continue ;;
        *)
            echo "Invalid input. Please enter 'yes' or 'no'." ;;
    esac
done

# Log file to capture output
log_file="${output_dir}/rman-${SID}-${current_datetime}.log"

echo "Log file: $log_file"
# Echo statement to print separator in the log file
echo "################################################" >> "$log_file" 2>&1

echo "Log file: $log_file" >> "$log_file" 2>&1
echo "#### Please Enter the Oracle Database SID  ##########" >> "$log_file" 2>&1

# Export ORACLE_SID and ORACLE_HOME
export ORACLE_SID="$SID"
export ORACLE_HOME="$ORACLE_HOME"

echo "Oracle SID : $SID"
echo "Oracle HOME : $ORACLE_HOME"

echo "Oracle SID : $SID " >> "$log_file" 2>&1
echo "Oracle HOME : $ORACLE_HOME " >>  "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

echo "#### Please Enter the Backup Location ##########" >> "$log_file" 2>&1

attempts=0
while [ $attempts -lt 3 ]; do
    read -p "Enter backup location: " BACKUP_LOCATION
    if [ ! -d "$BACKUP_LOCATION" ]; then
        echo "Error: The specified directory '$BACKUP_LOCATION' does not exist. Please try again."
        ((attempts++))
        continue
    fi
    read -p "Confirm backup location '$BACKUP_LOCATION'? (yes/no): " confirm_backup
    case $confirm_backup in
        [yY]|[yY][eE][sS])
            break ;;
        [nN]|[nN][oO])
            ((attempts++))
            if [ $attempts -eq 3 ]; then
                echo "Reached maximum attempts. Exiting..."
                exit 1
            else
                echo "Please re-enter backup location."
            fi ;;
        *)
            echo "Invalid input. Please enter 'yes' or 'no'." ;;
    esac
done

echo "Backup Location : $BACKUP_LOCATION " >>  "$log_file" 2>&1
echo "Backup Location : $BACKUP_LOCATION "

chown -R oracle:oinstall $BACKUP_LOCATION
chmod -R 775 $BACKUP_LOCATION


# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

echo "#### Please Enter the Pfile location ##########" >> "$log_file" 2>&1

attempts=0
while [ $attempts -lt 3 ]; do
    read -p "Enter pfile location: " PFILE
    if [ ! -f "$PFILE" ]; then
        echo "Error: The specified file '$PFILE' does not exist. Please try again."
        ((attempts++))
        continue
    fi
    read -p "Confirm pfile location '$PFILE'? (yes/no): " confirm_pfile
    case $confirm_pfile in
        [yY]|[yY][eE][sS])
            break ;;
        [nN]|[nN][oO])
            ((attempts++))
            if [ $attempts -eq 3 ]; then
                echo "Reached maximum attempts. Exiting..."
                exit 1
            else
                echo "Please re-enter pfile location."
            fi ;;
        *)
            echo "Invalid input. Please enter 'yes' or 'no'." ;;
    esac
done

echo "Pfile Location : $PFILE " >>  "$log_file" 2>&1
echo "Pfile Location : $PFILE "

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

# Copy the pfile to the same location with the current date and time appended to its name
new_pfile_location="${PFILE}-${current_datetime}"

if cp "$PFILE" "$new_pfile_location"; then
    echo "Successfully copied pfile to $new_pfile_location"
    echo "Successfully copied pfile to $new_pfile_location" >> "$log_file" 2>&1
else
    echo "Failed to copy pfile."
    echo "Failed to copy pfile." >> "$log_file" 2>&1
    exit 1
fi

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

echo "Copy pfile completed successfully." >> "$log_file" 2>&1

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1
echo "#### Please Enter the Control File location ##########" >> "$log_file" 2>&1

attempts=0
while [ $attempts -lt 3 ]; do
    read -p "Enter control file location: " CONTROLFILE
    if [ ! -f "$CONTROLFILE" ]; then
        echo "Error: The specified file '$CONTROLFILE' does not exist. Please try again."
        ((attempts++))
        continue
    fi
    read -p "Confirm control file location '$CONTROLFILE'? (yes/no): " confirm_controlfile
    case $confirm_controlfile in
        [yY]|[yY][eE][sS])
            break ;;
        [nN]|[nN][oO])
            ((attempts++))
            if [ $attempts -eq 3 ]; then
                echo "Reached maximum attempts. Exiting..."
                exit 1
            else
                echo "Please re-enter control file location."
            fi ;;
        *)
            echo "Invalid input. Please enter 'yes' or 'no'." ;;
    esac
done

echo "Control File Location : $CONTROLFILE " >>  "$log_file" 2>&1
echo "Control File Location : $CONTROLFILE "

# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

echo "#### Please Enter the Port Number ##########" >> "$log_file" 2>&1

while true; do
    read -p "Enter port number: " PORT
    if [[ $PORT =~ ^[0-9]+$ ]]; then
        break
    else
        echo "Error: Port number must be a positive integer. Please try again."
    fi
done

echo "Port number : $PORT " >>  "$log_file" 2>&1
echo "Port number : $PORT "


# Echo statement to print separator in the log file
echo "#########################################################################" >> "$log_file" 2>&1

nohup bash ./migration_script.sh $SID $BACKUP_LOCATION $PFILE $CONTROLFILE $PORT $log_file &

disown;