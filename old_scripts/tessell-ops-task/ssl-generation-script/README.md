# New SSL Generation Toolkit

This toolkit allows you to:
- Export secrets from AWS or Azure Key Vault
- Write them into local PEM/CRT/KEY files
- Map secret keys to required output filenames
- Validate and regenerate TLS certificates using a private CA

---

## 🔍 How to Get `infra-secret-id` and `dp-secret-id`

### 🛡️ For `infra-secret-id`
Run the following query:
```sql
SELECT * FROM tessell_ssl_creds WHERE tenant_id='{{tenant-id}}' AND type='CA';
```
You can find `{{tenant-id}}` from the Tessell Ops Portal.

### 🔐 For `dp-secret-id`
1. Run:
```sql
SELECT generic_info FROM tessell_service_instances WHERE tessell_service_id='{{tessell_service_id}}';
```
2. Extract the `serverCertId` from the resulting JSON.
3. Then run:
```sql
SELECT * FROM tessell_ssl_creds WHERE id='{{serverCertId}}' AND type='SERVER';
```

---

## 📦 Setup Using Poetry

This project uses [Poetry](https://python-poetry.org/) for dependency management.

### ✅ Install Poetry (if not already installed)
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

### ✅ Install Dependencies
```bash
poetry install
```

To activate the virtual environment:
```bash
poetry shell
```

---

## 🛠 Configuration File Format

Save this as `secret_config.ini`:

```ini
[aws_infra]
profile = xxxxx
region = xxxxx

[aws_dp]
profile = xxxxx
region = xxxxx

[azure_infra]
tenant_id = xxxxx
client_id = xxxxx
client_secret = xxxxx

[azure_dp]
tenant_id = xxxxx
client_id = xxxxx
client_secret = xxxxx
```

---

## ✅ Step 1: Export Secrets to Files

Use `generate_keys_from_cloud_secrets.py` to export keys and certs:

```bash
python generate_keys_from_cloud_secrets.py \
  --mode aws-azure \
  --output-dir /your/output/path \
  --config-file ./secret_config.ini \
  --infra-secret-id arn:aws:secretsmanager:ap-south-1:xxxxx \
  --dp-secret-id https://your-keyvault.vault.azure.net/secrets/server-ssl-credentials--uuid
```

**Modes:**
- `aws-aws`
- `aws-azure`
- `azure-aws`
- `azure-azure`

This script will:
- Validate cloud access
- Retrieve secrets
- Write them to `.pem` files
- Map and rename key files

**Key File Mapping:**

| Original File             | Final File Name             |
|--------------------------|-----------------------------|
| `dp_privateKey.pem`      | `TessellServerKeyfile.key`  |
| `dp_certificate.pem`     | `TessellServerCertificate.crt` |
| `infra_certificate.pem`  | `TessellCACertificate.crt`  |
| `infra_privateKey.pem`   | `root_ca_private.key`       |

---

## 🔒 Step 2: Generate & Sign New Certificate

First, prepare a config file for the hostname:

### 📄 `cert_config.ini`
```ini
# This is a sample hostname.
# Please get the config file approved in CCB before use.

[certificate]
hostname = vpce-03695560f7094947d-mcoutjmi.vpce-svc-0cbedc157183c1d9a.ap-south-1.vpce.amazonaws.com
```

Then run:
```bash
python generate_and_verify_cert.py \
  --dir /your/output/path \
  --config-file ./cert_config.ini
```

This script will:
- Copy `TessellServerCertificate.crt` to `TessellServerCertificate_old.crt`
- Verify existing certificate using CA
- Generate CSR
- Sign new certificate with private CA
- Display new certificate info

---

## 🧪 Optional Manual Validation

```bash
openssl verify -CAfile TessellCACertificate.crt TessellServerCertificate.crt
```

You should see: `TessellServerCertificate.crt: OK`

---

## 📂 Output Files

- `TessellServerCertificate_old.crt` — backed up certificate
- `TessellServerCertificate.crt` — newly signed certificate
- `TessellServerKeyfile.key` — server private key
- `TessellCACertificate.crt` — root CA certificate
- `root_ca_private.key` — root CA private key
- `server.csr` — generated certificate signing request
- `test.cnf` — OpenSSL-style config used for CSR generation

---

## 🔧 Prerequisites

- Python 3.8+
- Poetry
- AWS CLI or Azure credentials (based on your mode)
- OpenSSL (optional, for manual verification)

---

## ⚠️ CCB Approval Required

Please ensure all config files (like `cert_config.ini`) with production hostnames are **reviewed and approved via the CCB** before use.

---

## 👤 Author

Amit Sharma