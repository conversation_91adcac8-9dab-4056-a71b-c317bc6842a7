import argparse
import configparser
import os
import shutil
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional

from cryptography import x509
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.x509.oid import ExtendedKeyUsageOID, NameOID


def delete_if_exists(path: Path):
    if path.exists():
        path.unlink()
        print(f"[INFO] Deleted existing file: {path.name}")

def prompt_hostname(hostname: str) -> str:
    if len(hostname) > 63:
        print(f"[WARNING] Hostname '{hostname}' exceeds 63 characters.")
        confirm = input("This may fail in Oracle. Do you want to continue? (yes/no): ").strip().lower()
        if confirm not in ['yes', 'y']:
            print("[INFO] Aborted by user.")
            sys.exit(0)
    return hostname

def create_test_cnf(path: Path, hostname: str):
    template = f"""
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
CN = {hostname}

[v3_req]
keyUsage = digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = {hostname}
""".strip()

    with open(path, 'w') as f:
        f.write(template + "\n")
    print(f"[INFO] Created test.cnf at {path.name}")

def load_certificate(path: Path) -> x509.Certificate:
    return x509.load_pem_x509_certificate(path.read_bytes(), default_backend())

def load_private_key(path: Path):
    return serialization.load_pem_private_key(path.read_bytes(), password=None, backend=default_backend())

def verify_certificate_chain(cert: x509.Certificate, ca_cert: x509.Certificate, label: Optional[str] = None) -> bool:
    try:
        ca_cert.public_key().verify(
            cert.signature,
            cert.tbs_certificate_bytes,
            padding.PKCS1v15(),
            cert.signature_hash_algorithm
        )
        print(f"[INFO] {label or 'Certificate'} verified successfully.")
        return True
    except Exception as e:
        print(f"[ERROR] {label or 'Certificate'} verification failed: {e}")
        return False

def generate_csr(key_path: Path, hostname: str, csr_path: Path):
    key = load_private_key(key_path)

    csr = x509.CertificateSigningRequestBuilder().subject_name(x509.Name([
        x509.NameAttribute(NameOID.COMMON_NAME, hostname)
    ])).add_extension(
        x509.SubjectAlternativeName([
            x509.DNSName("localhost"),
            x509.DNSName(hostname),
        ]),
        critical=False
    ).add_extension(
        x509.ExtendedKeyUsage([ExtendedKeyUsageOID.SERVER_AUTH]),
        critical=False
    ).sign(key, hashes.SHA256(), default_backend())

    with open(csr_path, "wb") as f:
        f.write(csr.public_bytes(serialization.Encoding.PEM))
    print(f"[INFO] Generated CSR at {csr_path.name}")

def sign_certificate(csr_path: Path, ca_cert_path: Path, ca_key_path: Path,
                     output_path: Path, hostname: str, validity_days=1825):
    csr = x509.load_pem_x509_csr(csr_path.read_bytes(), default_backend())
    ca_cert = load_certificate(ca_cert_path)
    ca_key = load_private_key(ca_key_path)

    cert = (
        x509.CertificateBuilder()
        .subject_name(csr.subject)
        .issuer_name(ca_cert.subject)
        .public_key(csr.public_key())
        .serial_number(x509.random_serial_number())
        .not_valid_before(datetime.utcnow())
        .not_valid_after(datetime.utcnow() + timedelta(days=validity_days))
        .add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.DNSName(hostname),
            ]),
            critical=False,
        )
        .add_extension(
            x509.ExtendedKeyUsage([ExtendedKeyUsageOID.SERVER_AUTH]),
            critical=False,
        )
        .sign(private_key=ca_key, algorithm=hashes.SHA256(), backend=default_backend())
    )

    with open(output_path, "wb") as f:
        f.write(cert.public_bytes(serialization.Encoding.PEM))
    print(f"[INFO] Signed certificate created at {output_path.name}")

def display_certificate_details(cert_path: Path):
    try:
        cert = load_certificate(cert_path)
        print("\n[INFO] Certificate Details:")
        print("-" * 40)
        print(f"Issuer     : {cert.issuer.rfc4514_string()}")
        print(f"Subject    : {cert.subject.rfc4514_string()}")
        print(f"Serial No. : {cert.serial_number}")
        try:
            print(f"Valid From : {cert.not_valid_before_utc}")
            print(f"Valid To   : {cert.not_valid_after_utc}")
        except Exception as e:
            print(f"[ERROR] Failed to parse cert: {e}")
            print(f"Valid From : {cert.not_valid_before}")
            print(f"Valid To   : {cert.not_valid_after}")
        print("[Extensions]:")
        for ext in cert.extensions:
            print(f"  - {ext.oid._name}: {ext.value}")
        print("-" * 40)
    except Exception as e:
        print(f"[ERROR] Failed to parse cert: {e}")

def main():
    parser = argparse.ArgumentParser(description="Generate and verify TLS certificates using cryptography.")
    parser.add_argument('--dir', required=True, help='Directory containing certs and keys')
    parser.add_argument('--config-file', required=True, help='Path to config/ini file containing hostname')
    args = parser.parse_args()

    cert_dir = Path(args.dir).resolve()
    config_file = Path(args.config_file).resolve()

    if not config_file.exists():
        print(f"[ERROR] Config file not found: {config_file}")
        sys.exit(1)

    config = configparser.ConfigParser()
    config.read(config_file)

    if "certificate" not in config or "hostname" not in config["certificate"]:
        print(f"[ERROR] 'hostname' not found in [certificate] section of config file.")
        sys.exit(1)

    hostname = prompt_hostname(config["certificate"]["hostname"])

    cert_path = cert_dir / "TessellServerCertificate.crt"
    old_cert_path = cert_dir / "TessellServerCertificate_old.crt"
    ca_cert_path = cert_dir / "TessellCACertificate.crt"
    ca_key_path = cert_dir / "root_ca_private.key"
    keyfile_path = cert_dir / "TessellServerKeyfile.key"
    test_cnf_path = cert_dir / "test.cnf"
    csr_path = cert_dir / "server.csr"

    if old_cert_path.exists():
        delete_if_exists(cert_path)
    else:
        if cert_path.exists():
            shutil.copy2(cert_path, old_cert_path)
            print(f"[INFO] Copied {cert_path.name} → {old_cert_path.name}")
            delete_if_exists(cert_path)
        else:
            print(f"[ERROR] Missing {cert_path.name}")
            sys.exit(1)

    delete_if_exists(test_cnf_path)
    create_test_cnf(test_cnf_path, hostname)

    if not old_cert_path.exists() or not ca_cert_path.exists():
        print(f"[ERROR] Missing cert or CA file for verification.")
        sys.exit(1)

    old_cert = load_certificate(old_cert_path)
    ca_cert = load_certificate(ca_cert_path)

    if not verify_certificate_chain(old_cert, ca_cert, label="Old certificate"):
        print("[FAILURE] Certificate verification failed.")
        sys.exit(1)

    if not keyfile_path.exists():
        print(f"[ERROR] Missing server private key: {keyfile_path}")
        sys.exit(1)
    generate_csr(keyfile_path, hostname, csr_path)

    if not ca_key_path.exists():
        print(f"[ERROR] Missing CA private key: {ca_key_path}")
        sys.exit(1)
    sign_certificate(csr_path, ca_cert_path, ca_key_path, cert_path, hostname)

    new_cert = load_certificate(cert_path)
    if verify_certificate_chain(new_cert, ca_cert, label="New certificate"):
        print("[SUCCESS] New certificate is valid and trusted.")

    display_certificate_details(cert_path)

if __name__ == "__main__":
    main()