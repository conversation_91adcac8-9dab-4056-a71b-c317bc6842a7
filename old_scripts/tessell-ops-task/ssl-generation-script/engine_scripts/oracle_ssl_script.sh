openssl verify -CAfile /etc/ssl/TessellCertificates/TessellCACertificate.crt /etc/ssl/TessellCertificates/TessellServerCertificate.crt

mkdir /tmp/tessell-logs/generate_new_ssl/
chmod 777 -R /tmp/tessell-logs/generate_new_ssl/
rm -rf /tmp/tessell-logs/generate_new_ssl/*

# if $1 and $2 is empty, exit with error
if [ -z "$1" ] || [ -z "$2" ]; then
    echo "ERROR: Provide connect Oracle SID and Oracle SYS password"
    exit 1
fi

oracle_syspass=$2
ORACLE_SID=$1
pkcs12_wallet=/tmp/tessell-logs/generate_new_ssl/ewallet.p12
SSL_AUTO_LOGIN_WALLET=/home/<USER>/tessell/SSL_WALLET/$ORACLE_SID
if [ -d "$SSL_AUTO_LOGIN_WALLET" ]; then
    echo "SSL_AUTO_LOGIN_WALLET already exists"
else
    echo "ERROR: $SSL_AUTO_LOGIN_WALLET does not exists. Provide connect Oracle SID"
    exit 1
fi
rm -rf $SSL_AUTO_LOGIN_WALLET


openssl pkcs12 -export -out "${pkcs12_wallet}" -inkey "/etc/ssl/TessellCertificates/TessellServerKeyfile.key" -in "/etc/ssl/TessellCertificates/TessellServerCertificate.crt" -chain -CAfile "/etc/ssl/TessellCertificates/TessellCACertificate.crt" -passout pass:"${oracle_syspass}"


$ORACLE_HOME/bin/orapki wallet create -wallet "$SSL_AUTO_LOGIN_WALLET" -compat_v12 -auto_login_only

$ORACLE_HOME/bin/orapki wallet import_pkcs12 -wallet "$SSL_AUTO_LOGIN_WALLET" -pkcs12file "${pkcs12_wallet}" -pkcs12pwd "${oracle_syspass}" -auto_login_only

$ORACLE_HOME/bin/orapki wallet display -wallet "$SSL_AUTO_LOGIN_WALLET"

rm -rf $pkcs12_wallet