[tool.poetry]
name = "multi-cloud-secrets-toolkit"
version = "0.1.0"
description = "Export secrets from AWS/Azure, write them as PEM files, and verify certificates."
authors = ["Your Name <<EMAIL>>"]
license = "MIT"
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.8,<4.0"
boto3 = "^1.34"
azure-identity = "^1.15.0"
azure-keyvault-secrets = "^4.7.0"
cryptography = "^42.0.5"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"