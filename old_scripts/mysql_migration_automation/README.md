
# MySQL Migration Automation

## Overview
**mysql_migrator.sh** is a powerful automation script designed to streamline and secure the migration of MySQL databases. This script includes advanced functionalities for comprehensive migration workflows, addressing pre-checks, compatibility verifications, selective database migrations, and post-migration validation. With its versatility, mysql_migrator.sh simplifies the complex tasks involved in MySQL migrations, ensuring an efficient and smooth transition between database environments.


## Features
- ### Pre-requisite Checks
  - Ensures all necessary requirements for migration are in place before proceeding.

- ### Server Parameter Checks
  - Verifies the compatibility of parameters between source and target databases.

- ### Schema Compatibility Checks
  - Checks the compatibility of schemas from the source to ensure seamless integration.

- ### Database Connectivity
  - Validates connectivity to both source and target MySQL servers before migration.

- ### Configuration File Support
  - Reads and applies variables from a JSON configuration file, making it easier to maintain and customize migration settings.

- ### Detailed Logging
  - Logs migration steps to both console and file for easy debugging and auditing.

- ### Flexible Database Migration
  - Full Migration: Migrate all databases from source to target.
  - Selective Migration: Allows users to specify and migrate only the selected databases.
 
- ### Database User Migration
  - Transfers user accounts from the source to the target database.
  - Migrates user privileges to ensure consistent access control on the target.
 
- ### Customizable Migration Options
  - Supports multiple migration types for tailored scenarios:

    - Only Tables: Migrates only table structures without data.
    - Only Data: Migrates only the data without schema structure.
    - Tables with Data: Migrates both table structure and data.
    - Only Stored Programs: Migrates stored procedures, triggers, and events.
    - Only Users: Migrates only users without tables or data.
    - All (Schema + Data + Users): Comprehensive migration including schema, data, and users.
   
- ### Validation and Reporting
  - Conducts a detailed validation of the migration process and generates a report, ensuring data integrity and consistency.

- ### Data Replication
  - Enables CDC replication between source and target post-migration, supported on MySQL 8.0.23 and later versions.

- ### Slack Notifications
  - Currently not supported.

## Usage
- ### Prerequisites
  - Ensure both source and target servers are accessible.
  - Create the database user on source and target database.
  - Source DB user must have below privileges.
    
    > ```sql
    > GRANT SELECT, SHOW VIEW , TRIGGER, EVENT , LOCK TABLES , PROCESS , RELOAD , FLUSH_TABLES, REPLICATION SLAVE, REPLICATION CLIENT,SESSION_VARIABLES_ADMIN on *.* to source_user;```

  - Target DB user must have below privileges.
    
    > ```sql
    > GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, RELOAD, PROCESS, REFERENCES, INDEX, ALTER, SHOW DATABASES, CREATE TEMPORARY TABLES, LOCK TABLES, EXECUTE, REPLICATION SLAVE, REPLICATION CLIENT, CREATE VIEW, SHOW VIEW, CREATE ROUTINE, ALTER ROUTINE, CREATE USER, EVENT, TRIGGER ON *.* TO target_user; ```
    
    > ```sql
    > GRANT BACKUP_ADMIN, INNODB_REDO_LOG_ENABLE, SET_USER_ID, SESSION_VARIABLES_ADMIN, REPLICATION_SLAVE_ADMIN on *.* to target_user;```
    
  - Create a backup directory on target server, ensure sufficient storage capacity is available
  - Customize the JSON configuration file with all required parameters.
  - Increase the binlog retention on source db if you want to enable data replication post migration.

- ### Running the Script
  - Run the script from the command line with appropriate privileges.
  - Specify the path to the JSON configuration file as an argument.
  - When dealing with a large database size, it is highly recommended to run the migration process in the background (screen/nohup/etc). This allows you to continue using your terminal for other tasks and prevents the migration process from being interrupted if you accidentally close your terminal session.
    

   > `$ bash mysql_migrator.sh /path/to/config.json `


## Logs and Reports

  - ### Logs:
    - Real-time updates in the console and detailed logs in the specified log file.
      
  - ### Validation Report:
    - Summary report after migration to verify accuracy.
    - Sample output:

| DB Name | Table Name | Source Count | Target Count | Validation |
| ---------|-------------|-------------|---------------|--------------- |
| sysdb | sbtest1 | 10000 | 10000 | MATCH |
| sysdb | sbtest10 | 10000 | 10000 | MATCH |
| sysdb | sbtest2 | 10000 | 10000 | MATCH |
| sysdb | sbtest3 | 10000 | 10000 | MATCH |
| sysdb | sbtest4 | 10000 | 10000 | MATCH |
| sysdb | sbtest5 | 10000 | 10000 | MATCH |
| sysdb | sbtest6 | 10000 | 10000 | MATCH |
| sysdb | sbtest7 | 10000 | 10000 | MATCH |
| sysdb | sbtest8 | 10000 | 10000 | MATCH |
| sysdb | sbtest9 | 10000 | 10000 | MATCH |

## Handling Maintenance Activities During CDC Replication
When planning any maintenance activity on the MySQL Cluster/Target Database while Change Data Capture (CDC) replication is in progress, it is essential to follow these steps to avoid replication issues or data inconsistencies:
  - Establish a connection to the primary node of the target database where replication is active.
  - Identify the binlog file name and position for the ongoing replication process.
  - Make a note of these details for reconfiguration after the maintenance.
  - Stop the replication process and clear existing replication details to avoid conflicts during maintenance.
  - Perform the Database Maintenance Activity on target database.
  - Execute the required maintenance tasks on the target database, such as VM/DB restarts, instance resizing, or configuration updates.
  - Use the previously noted binlog file and position to set up replication again.
  - Ensure that replication resumes from the correct state to maintain data consistency.
  - Confirm that replication is running as expected.
  - Check for errors and ensure that the source and target databases are synchronized.
  
By following these steps, you can ensure a smooth and consistent replication process even during critical database maintenance activities.
    
## Limitation

   - Only MySQL 8.0.23+ support CDC replication setup post-migration.
   - Slack notifications are currently unsupported in this version.

   
## Contributions
  - For suggestions, bug reports, or feature requests, please submit an issue or pull request. Your contributions are welcome!
