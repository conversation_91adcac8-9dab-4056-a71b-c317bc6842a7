{"variables": {"SOURCE_ENDPOINT": {"description": "Provide Source database endpoint", "value": "dhivya-rds.ckto9v5eqlak.us-east-1.rds.amazonaws.com"}, "SOURCE_USERNAME": {"description": "Provide Source database username", "value": "source_user"}, "SOURCE_PASSWORD": {"description": "Provide Source database password", "value": "source_user"}, "SOURCE_PORT": {"description": "Provide Source database port number", "value": "3306"}, "TARGET_ENDPOINT": {"description": "Provide Target database endpoint", "value": "dhivyamysql80-yvuoq.cs.tessell-stage.com"}, "TARGET_USERNAME": {"description": "Provide Target database username", "value": "target_user"}, "TARGET_PASSWORD": {"description": "Provide Target database password", "value": "target_user"}, "TARGET_PORT": {"description": "Provide Target database port number", "value": "3306"}, "SKIP_DB_CONNECTION_CHECKS": {"description": "Do you want to skip source and target database connectivity checks?", "allowedValues": "Y,N", "value": "N"}, "SKIP_SCHEMA_COMPATIBILITY_CHECKS": {"description": "Do you want to skip validation of source table without primary key or unique key?", "allowedValues": "Y,N", "value": "N"}, "SKIP_BACKUP_OPERATION": {"description": "Do you want to skip taking logical backups?", "allowedValues": "Y,N", "value": "N"}, "SKIP_RESTORE_OPERATION": {"description": "Do you want to skip restoring backups to target database?", "allowedValues": "Y,N", "value": "N"}, "BACKUP_TOOL": {"description": "Provide tool name to be used for taking data backup.", "allowedValues": "mydumper,mysqldump", "value": "mydumper"}, "BACKUP_DIRECTORY": {"description": "Provide absolute backup directory path", "value": "/mysql/backup"}, "SELECT_DATABASES": {"description": "Provide comma-separated list of databases to be migrated (db1,db2,db3) and default is ALL which means all databases except system databases", "value": "all"}, "SELECT_MIGRATION_TYPE": {"description": "Provide migration type", "allowedValues": "only_tables,only_data,only_stored_programs,tables_with_data,only_user_migration,all", "value": "tables_with_data"}, "SKIP_VALIDATION_AFTER_MIGRATION": {"description": "Do you want to skip post-migration validation checks such as database count, table count, table row count and db user count?", "value": "N"}, "ENABLE_REPLICATION_AFTER_MIGRATION": {"description": "Do you want to enable CDC replication post migration?", "value": "N"}, "CONFIGURE_MYDUMPER": {"description": "Specify settings for mydumper and this is applicable when you use mydumper for backup. Notes: Use –lock-all-tables only for RDS and Use –trx-consistency-only if all your tables are InnoDB", "value": "--complete-insert --rows=10000 --verbose 3 --lock-all-tables --compress --build-empty-files --threads=4 --compress-protocol"}, "CONFIGURE_MYLOADER": {"description": "Specify settings for myloader and this is applicable when you use mydumper for backup", "value": "--verbose 3 --threads=4 --compress-protocol --enable-binlog --disable-redo-log"}, "SKIP_SLACK_NOTIFICATION": {"description": "Do you want to skip slack notification?", "value": "Y"}, "SLACK_WEBHOOK": {"description": "Provide your slack webhook url", "value": "*********************************************************************************"}}}