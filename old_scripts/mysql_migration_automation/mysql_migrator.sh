#!/bin/bash

# ====================================================================================
# Script Name: mysql_migrator.sh
# Author: <PERSON><PERSON><PERSON>a Arumugam
# Created On: 14/Nov/2024
# Version: 1.0
#
# Description:
# This script automates database migration between MySQL servers with a wide range of
# features, ensuring a seamless and efficient migration process. It is designed to
# handle various aspects of the migration lifecycle, from pre-checks to validation.
#
# Functionality:
# - **Pre-requisite Checks**: Ensures all requirements for migration are met.
# - **Server Parameter Checks**: Verifies compatibility of source and target database parameters.
# - **Schema Compatibility Checks**: Verifies schema compatibility of source tables.
# - **Database Connectivity**: Validates connectivity to source and target databases.
# - **Configuration File Support**: Automation reads and uses variables from a JSON-formatted config file.
# - **Logging**: Logs to both console and file for debugging and audit purposes.
# - **Full Database Migration**: Migrates all databases from source to target.
# - **Selective Database Migration**: Allows selecting specific databases for migration.
# - **User Migration**: Migrates database users from source to target.
# - **Grant Migration**: Transfers user privileges (grants) to the target database.
# - **Customizable Migration**:
#   - Only_tables migration
#   - Only_data migration
#   - Tables_with_data migration
#   - Only_stored_programs migration (Routines, Triggers, Events)
#   - Only_user migration
#   - ALL migration (schema + data + user)
# - **Validation**: Validates the migration and generates a detailed validation report.
# - **Replication Setup**: Configures replication post-migration (supported for MySQL 8.0.23+).
# - **Slack Notification**: Currently not supported
#
# Usage:
#   bash mysql_migrator.sh migration_config_file.json
#
# Notes:
# Execute the script with appropriate parameters or environment settings. Ensure the
# config file contains the required configurations for the migration process.
#
# ====================================================================================

fn_initialize()
{
    mysqlbin="/mysqlbin/bin"
    mydumper_bin="/usr/bin"
    script_folder="/tmp/tessell_migration_`date +%Y%m%d_%H%M%S`"
    stdout="${script_folder}/db_migration_stdout.log"
    stderr="${script_folder}/db_migration_stderr.err"
    scriptlog="${script_folder}/db_migration_`date +%Y%m%d_%H%M%S`.log"
    mydumper_version="v0.15.2-8"
    TABLE_STRUCT_VALIDATION_FILE=${script_folder}/table_structure_validation.txt
    TABLE_COUNT_VALIDATION_FILE=${script_folder}/table_count_validation.txt
    STORED_PROGRAM_VALIDATION_FILE=${script_folder}/stored_program_validation.txt
    USER_GRANTS_VALIDATION_FILE=${script_folder}/user_grants_validation.txt
    USER_COUNT_VALIDATION_FILE=${script_folder}/user_count_validation.txt
    DATA_COUNT_VALIDATION_FILE=${script_folder}/data_count_validation.txt
    repl_channel="tessell_async_repl"

}

# Function to create script log directory
fn_generate_logdir()
{
    result=0
    if [ ! -d ${script_folder} ]; then
        mkdir -p ${script_folder}
        result=$?
        if [ $result -ne 0 ]; then
            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Failed to create log directory ${script_folder}, please check the error.\n"
            exit 1
        else
            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Successfully created log directory ${script_folder}\n"
            echo -e " " > ${scriptlog}
            echo -e "\n\n###########################################################\n###             Tessell Automation                     ###\n###########################################################\n" | tee -a ${scriptlog}
            echo -e "\n\tScript Name\t: $0\n\tStart Time\t: `date +"%Y-%m-%dT%H:%M:%S"`\n" | tee -a ${scriptlog}
        fi;
    fi;
}

# Funtion to display log file and warning/error if any

fn_display_message()
{
  echo -e "\nMigration Log\t: ${scriptlog}"
  echo -e "\nError\t\t: `grep -i 'ERROR' ${scriptlog} | wc -l`\n"

}

# Function to check packages
fn_check_package()
{
    if command -v "$1" &> /dev/null
    then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Package $1 exists." | tee -a ${scriptlog}
    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Package $1 does not exist. Please install the required package and then re-try." | tee -a ${scriptlog}
        fn_display_message
        exit 1
    fi
}

# Function to check database connectivity
fn_check_db_connectivity()
{
    db_host=$1
    db_user=$2
    db_pass=$3
    db_port=$4

    MYSQL_PWD=${db_pass} ${mysqlbin}/mysql -A --host=${db_host} --user=${db_user} --port=${db_port} -e "select 1;" 1> ${stdout} 2> ${stderr}
    if [ $? -eq 0 ]; then
        #cat ${stdout} >> ${scriptlog}
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Success.\n" | tee -a ${scriptlog}
        export MYSQL_PWD=""
    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Unable to connect to the database. Please check the provided credentials, network connection and try again." | tee -a ${scriptlog}
        cat ${stderr} | tee -a ${scriptlog}
        export MYSQL_PWD=""
        fn_display_message
        exit 1
    fi
}

fn_check_definer()
{
    db_host=$1
    db_user=$2
    db_pass=$3
    db_port=$4

    v_definer_check=`MYSQL_PWD=${db_pass} ${mysqlbin}/mysql -A -Nrs --host=${db_host} --user=${db_user} --port=${db_port} -e "show grants;" | grep 'SET_USER_ID' | wc -l 2> ${stderr}`
    if [ ${v_definer_check} -ne 0 ]; then

      echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Success.\n" | tee -a ${scriptlog}
    else
      echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: The definer check for SET_USER_ID privilege for the target user \"$db_user\" has been failed!!!" | tee -a ${scriptlog}
      cat ${stderr} | tee -a ${scriptlog}
      export MYSQL_PWD=""
      fn_display_message
      exit 1
    fi;

    export MYSQL_PWD=""
}



fn_check_binlog_settings()
{
    db_host=$1
    db_user=$2
    db_pass=$3
    db_port=$4

    echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking binary logging enabled on source database!!!" | tee -a ${scriptlog}

    MYSQL_PWD=${db_pass} ${mysqlbin}/mysql -A -Nrs --host=${db_host} --user=${db_user} --port=${db_port} -e "SHOW BINARY LOGS;" 1> ${stdout} 2> ${stderr}
    if [ $? -eq 0 ]; then

      echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Success.\n" | tee -a ${scriptlog}

    else
      echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Binary logging is not enabled on source database for data replication!!!" | tee -a ${scriptlog}
      cat ${stderr} | tee -a ${scriptlog}
      fn_display_message
      exit 1
    fi;

    echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking Binlog format setting on source database!!!" | tee -a ${scriptlog}
    MYSQL_PWD=${db_pass} ${mysqlbin}/mysql -A -Nrs --host=${db_host} --user=${db_user} --port=${db_port} -e "show global variables like 'binlog_format';" | grep 'ROW' 1> ${stdout} 2> ${stderr}
    if [ $? -eq 0 ]; then

      echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Success.\n" | tee -a ${scriptlog}

    else
      echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Binlog format Check: failed.\n\nWe recommend binlog_format=ROW setting on source db for consistent data replication." | tee -a ${scriptlog}
      cat ${stderr} | tee -a ${scriptlog}
      fn_display_message
      exit 1
    fi;


    MYSQL_PWD=${db_pass} ${mysqlbin}/mysql -A -Nrs --host=${db_host} --user=${db_user} --port=${db_port} -e "show global variables like 'read_only';" | grep 'ON' 1> ${stdout} 2> ${stderr}
    if [ $? -eq 0 ]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking log_slave_updates setting on source database!!!" | tee -a ${scriptlog}
        MYSQL_PWD=${db_pass} ${mysqlbin}/mysql -A -Nrs --host=${db_host} --user=${db_user} --port=${db_port} -e "show global variables like 'log_slave_updates';" | grep 'ON' 1> ${stdout} 2> ${stderr}
        if [ $? -eq 0 ]; then
            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Success.\n" | tee -a ${scriptlog}
        else
            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: log_slave_updates Setting Check: failed.\n" | tee -a ${scriptlog}
            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: You must enable log_slave_updates on source db if you plan to replica data from a replica to target db.\n" | tee -a ${scriptlog}
            cat ${stderr} | tee -a ${scriptlog}
            fn_display_message
            exit 1
        fi;

    fi;


    export MYSQL_PWD=""
}

fn_check_privileges()
{
    db_host=$1
    db_user=$2
    db_pass=$3
    db_port=$4
    which_host=$5
    db_priv=$6

    echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking '${db_priv}' Privilege for \"$db_user\" ${which_host} db user!!!" | tee -a ${scriptlog}
    MYSQL_PWD=${db_pass} ${mysqlbin}/mysql -A -Nrs --host=${db_host} --user=${db_user} --port=${db_port} -e "SHOW GRANTS;" | grep "${db_priv}" 1> ${stdout} 2> ${stderr}
    if [ $? -eq 0 ]; then

      echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Success.\n" | tee -a ${scriptlog}

    else
      echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: '${db_priv}' Privilege Check: failed.\n\nThis privilege is required for ${which_host} db user." | tee -a ${scriptlog}
      cat ${stderr} | tee -a ${scriptlog}
      fn_display_message
      exit 1
    fi;
}


# Function to validate database list
fn_validate_database_list()
{
    db_host=$1
    db_user=$2
    db_pass=$3
    db_port=$4
    db_list=$5

    export MYSQL_PWD=${db_pass}

    IFS=',' read -ra dbs <<< "${db_list}"
    db_count=0
    for db in "${dbs[@]}"; do
        db_count=`expr ${db_count} + 1 `

        ${mysqlbin}/mysql -A --host=${db_host} --user=${db_user} -e "use $db" 1> ${stdout} 2> ${stderr}
        if [ $? -ne 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Database $db does not exist or unable to access. Please check the database list and try again." | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          export MYSQL_PWD=""
          fn_display_message
          exit 1
        else
          echo -e "${db_count}: Database '$db' Exists" | tee -a ${scriptlog}
        fi;
    done
    export MYSQL_PWD=""
}

# Function to validate tables (structure comparison)
fn_validate_table_structure()
{
    SOURCE_DB_NAME=$1

    for table in $(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -N -e "SHOW TABLES FROM $SOURCE_DB_NAME");
    do
        MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "SHOW CREATE TABLE $SOURCE_DB_NAME.$table" > ${script_folder}/source_schema.txt
        MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "SHOW CREATE TABLE $SOURCE_DB_NAME.$table" >  ${script_folder}/target_schema.txt

        if diff "${script_folder}/source_schema.txt" "${script_folder}/target_schema.txt" > /dev/null; then
            echo "$SOURCE_DB_NAME | $table | MATCH" >> $TABLE_STRUCT_VALIDATION_FILE
        else
            echo "$SOURCE_DB_NAME | $table | MISMATCH" >> $TABLE_STRUCT_VALIDATION_FILE
        fi
    done

}

# Function to validate table count
fn_validate_table_count()
{

    SOURCE_DB_NAME=$1

    source_table_count=`MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$SOURCE_DB_NAME';" `
    target_table_count=`MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$SOURCE_DB_NAME';"`

    if [[ "$source_table_count" == "$target_table_count" ]]; then

        echo "$SOURCE_DB_NAME | $source_table_count | $target_table_count | MATCH " >> $TABLE_COUNT_VALIDATION_FILE

    else

        echo "$SOURCE_DB_NAME | $source_table_count | $target_table_count | MISMATCH " >> $TABLE_COUNT_VALIDATION_FILE

    fi

}

# Function to validate table record count

fn_validate_data_count()
{
    SOURCE_DB_NAME=$1

    for table in $(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -N -e "SHOW TABLES FROM $SOURCE_DB_NAME");
    do
        source_data_count=`MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "SELECT COUNT(1) FROM $SOURCE_DB_NAME.$table"  &`
        target_data_count=`MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "SELECT COUNT(1) FROM $SOURCE_DB_NAME.$table"  &`
        wait
        if [[ "$source_data_count" == "$target_data_count" ]]; then
            echo "$SOURCE_DB_NAME | $table | $source_data_count | $target_data_count | MATCH" >> $DATA_COUNT_VALIDATION_FILE
        else
            echo "$SOURCE_DB_NAME | $table | $source_data_count | $target_data_count | MISMATCH" >> $DATA_COUNT_VALIDATION_FILE
        fi
    done

}

fn_validate_users()
{
    src_username=$1
    source_user=`echo $src_username | cut -d'@' -f1`
    source_host=`echo $src_username | cut -d'@' -f2`

    target_user=$(MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT  -Nrs -e "select concat_ws(\"'\",\" \",user,\"@\",substring_index(host,':',1),\" \") from mysql.user where user=$source_user and host=$source_host;")

    src_priv=$(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "SHOW GRANTS FOR $src_username")
    target_priv=$(MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "SHOW GRANTS FOR $src_username" 2> /dev/null)

    if [[ "$src_priv" == "$target_priv" ]]; then

        echo "$src_username | $target_user | MATCH" >> $USER_GRANTS_VALIDATION_FILE
    else

        echo "$src_username | $target_user | MISMATCH" >> $USER_GRANTS_VALIDATION_FILE
    fi
}

fn_validate_stored_program()
{
    src_db=$1
    s_view_count=$(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select count(1) from information_schema.VIEWS where TABLE_SCHEMA='$src_db'")
    t_view_count=$(MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "select count(1) from information_schema.VIEWS where TABLE_SCHEMA='$src_db'")
    s_trigger_count=$(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select count(1) from information_schema.TRIGGERS where TRIGGER_SCHEMA='$src_db'")
    t_trigger_count=$(MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "select count(1) from information_schema.TRIGGERS where TRIGGER_SCHEMA='$src_db'")
    s_routine_count=$(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select count(1) from information_schema.ROUTINES where ROUTINE_SCHEMA='$src_db'")
    t_routine_count=$(MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "select count(1) from information_schema.ROUTINES where ROUTINE_SCHEMA='$src_db'")
    s_event_count=$(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select count(1) from information_schema.EVENTS where EVENT_SCHEMA='$src_db'")
    t_event_count=$(MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "select count(1) from information_schema.EVENTS where EVENT_SCHEMA='$src_db'")

    MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select TABLE_SCHEMA,TABLE_NAME,DEFINER from information_schema.VIEWS where TABLE_SCHEMA='$src_db' order by TABLE_SCHEMA,TABLE_NAME;" > ${script_folder}/source_view_definer.txt
    MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "select TABLE_SCHEMA,TABLE_NAME,DEFINER from information_schema.VIEWS where TABLE_SCHEMA='$src_db' order by TABLE_SCHEMA,TABLE_NAME;" >  ${script_folder}/target_view_definer.txt

    MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select TRIGGER_SCHEMA,TRIGGER_NAME,DEFINER from information_schema.triggers where TRIGGER_SCHEMA='$src_db' order by TRIGGER_SCHEMA,TRIGGER_NAME;" > ${script_folder}/source_trigger_definer.txt
    MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "select TRIGGER_SCHEMA,TRIGGER_NAME,DEFINER from information_schema.triggers where TRIGGER_SCHEMA='$src_db' order by TRIGGER_SCHEMA,TRIGGER_NAME;" >  ${script_folder}/target_trigger_definer.txt

    MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select ROUTINE_SCHEMA,ROUTINE_NAME,DEFINER from information_schema.ROUTINES where ROUTINE_SCHEMA='$src_db' order by ROUTINE_SCHEMA,ROUTINE_NAME;" > ${script_folder}/source_routine_definer.txt
    MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "select ROUTINE_SCHEMA,ROUTINE_NAME,DEFINER from information_schema.ROUTINES where ROUTINE_SCHEMA='$src_db' order by ROUTINE_SCHEMA,ROUTINE_NAME;" >  ${script_folder}/target_routine_definer.txt

    MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select EVENT_SCHEMA,EVENT_NAME,DEFINER from information_schema.EVENTS where EVENT_SCHEMA='$src_db' order by EVENT_SCHEMA,EVENT_NAME;" > ${script_folder}/source_event_definer.txt
    MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "select EVENT_SCHEMA,EVENT_NAME,DEFINER from information_schema.EVENTS where EVENT_SCHEMA='$src_db' order by EVENT_SCHEMA,EVENT_NAME;" >  ${script_folder}/target_event_definer.txt

    if [[ "$s_view_count" == "$t_view_count" ]]; then
        echo "$src_db | VIEW | $s_view_count | $t_view_count | MATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    else
        echo "$src_db | VIEW | $s_view_count | $t_view_count | MISMATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    fi

    if [[ "$s_trigger_count" == "$t_trigger_count" ]]; then
        echo "$src_db | TRIGGER | $s_trigger_count | $t_trigger_count | MATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    else
        echo "$src_db | TRIGGER | $s_trigger_count | $t_trigger_count | MISMATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    fi

    if [[ "$s_routine_count" == "$t_routine_count" ]]; then
        echo "$src_db | ROUTINE | $s_routine_count | $t_routine_count | MATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    else
        echo "$src_db | ROUTINE | $s_routine_count | $t_routine_count | MISMATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    fi

    if [[ "$s_event_count" == "$t_event_count" ]]; then
        echo "$src_db | EVENT | $s_event_count | $t_event_count | MATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    else
        echo "$src_db | EVENT | $s_event_count | $t_event_count | MISMATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    fi


    if diff "${script_folder}/source_view_definer.txt" "${script_folder}/target_view_definer.txt" > /dev/null; then
        echo "$src_db | VIEW_DEFINER |  |  | MATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    else
        echo "$src_db | VIEW_DEFINER |  |  | MISMATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    fi

    if diff "${script_folder}/source_trigger_definer.txt" "${script_folder}/target_trigger_definer.txt" > /dev/null; then
        echo "$src_db | TRIGGER_DEFINER |  |  | MATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    else
        echo "$src_db | TRIGGER_DEFINER |  |  | MISMATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    fi

    if diff "${script_folder}/source_routine_definer.txt" "${script_folder}/target_routine_definer.txt" > /dev/null; then
        echo "$src_db | ROUTINE_DEFINER |  |  | MATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    else
        echo "$src_db | ROUTINE_DEFINER |  |  | MISMATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    fi

    if diff "${script_folder}/source_event_definer.txt" "${script_folder}/target_event_definer.txt" > /dev/null; then
        echo "$src_db | EVENT_DEFINER |  |  | MATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    else
        echo "$src_db | EVENT_DEFINER |  |  | MISMATCH" >> $STORED_PROGRAM_VALIDATION_FILE
    fi

}

fn_migrate_schema()
{

    if [[ "${SKIP_BACKUP_OPERATION,,}" == "n" ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the Table Schema migration!!!" | tee -a ${scriptlog}

        ${mysqlbin}/mysqldump --no-data --skip-routines --skip-events --skip-triggers  --skip-set-charset  --column-statistics=0 --set-gtid-purged=OFF --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --password="${SOURCE_PASSWORD}"  --databases ${final_db_list} > ${BACKUP_DIRECTORY}/only_tables_dump.sql  2> ${stderr}

        if [ $? -eq 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source table schema dump has been exported without trigger/event/routine to dump file.\n\nDump file : ${BACKUP_DIRECTORY}/only_tables_dump.sql\n" | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Unable to export table schema dump of source database!!!" | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          fn_display_message
          exit 1
        fi;
    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source table schema export has been skipped due to SKIP_BACKUP_OPERATION set to Y." | tee -a ${scriptlog}
    fi;

    if [[ "${SKIP_RESTORE_OPERATION,,}" == "n" ]]; then

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Importing the schema dump to target database!!!" | tee -a ${scriptlog}

        export MYSQL_PWD="$TARGET_PASSWORD"
        ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT  < ${BACKUP_DIRECTORY}/only_tables_dump.sql  1> ${stdout} 2> ${stderr}
        if [ $? -eq 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source schema has been successfully migrated to target database." | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Failed to migrate schema dump to target database. Please check the error and try again." | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          export MYSQL_PWD=""
          fn_display_message
          exit 1
        fi;
        export MYSQL_PWD=""
    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Schema migration to target database has been skipped due to SKIP_RESTORE_OPERATION set to Y." | tee -a ${scriptlog}
    fi;

    if [[ "${SKIP_VALIDATION_AFTER_MIGRATION,,}" == "n" ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the Table Schema Validation!!!" | tee -a ${scriptlog}

        echo "-----------------------------------------------------------------" > $TABLE_STRUCT_VALIDATION_FILE
        echo "DB Name | Source Table | Target Table" >> $TABLE_STRUCT_VALIDATION_FILE
        echo "-----------------------------------------------------------------" >> $TABLE_STRUCT_VALIDATION_FILE

        echo "-----------------------------------------------------------------" > $TABLE_COUNT_VALIDATION_FILE
        echo "DB Name | Source Table Count | Target Table Count | Validation" >> $TABLE_COUNT_VALIDATION_FILE
        echo "-----------------------------------------------------------------" >> $TABLE_COUNT_VALIDATION_FILE

        read -ra dbs <<< "${final_db_list}"
        db_count=0

        for db in "${dbs[@]}"; do

            db_count=`expr ${db_count} + 1 `

            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Validating table schema \"$db\" between Source and Target database..." | tee -a ${scriptlog}
            fn_validate_table_structure $db

            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Validating table count for DB \"$db\" between Source and Target database..." | tee -a ${scriptlog}
            fn_validate_table_count $db

        done
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Schema Validation Report has been generated.\n\nStructure Validation Report\t: $TABLE_STRUCT_VALIDATION_FILE\nStructure Mismatch Found\t: `grep -i MISMATCH $TABLE_STRUCT_VALIDATION_FILE | wc -l`\n" | tee -a ${scriptlog}
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Schema Validation Report has been generated.\n\nTable Count Validation Report\t: $TABLE_COUNT_VALIDATION_FILE\nTable Count Mismatch Found\t: `grep -i MISMATCH $TABLE_COUNT_VALIDATION_FILE | wc -l`\n" | tee -a ${scriptlog}
    else

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Schema validation on target database has been skipped due to SKIP_VALIDATION_AFTER_MIGRATION set to Y." | tee -a ${scriptlog}

    fi;
}

fn_migrate_data()
{

    if [[ "${SKIP_BACKUP_OPERATION,,}" == "n" ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the Data migration!!!" | tee -a ${scriptlog}

        ${mysqlbin}/mysqldump --single-transaction --no-create-db --no-create-info --skip-triggers --skip-set-charset --complete-insert --column-statistics=0 --set-gtid-purged=OFF --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --password="${SOURCE_PASSWORD}"  --databases ${final_db_list} > ${BACKUP_DIRECTORY}/only_data_dump.sql  2> ${stderr}

        if [ $? -eq 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source data has been exported.\n\nDump file : ${BACKUP_DIRECTORY}/only_data_dump.sql\n" | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Unable to export data dump of source database!!!" | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          fn_display_message
          exit 1
        fi;
    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source data export has been skipped due to SKIP_BACKUP_OPERATION set to Y." | tee -a ${scriptlog}
    fi;

    if [[ "${SKIP_RESTORE_OPERATION,,}" == "n" ]]; then

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Importing the source data to target database!!!" | tee -a ${scriptlog}

        export MYSQL_PWD="$TARGET_PASSWORD"
        ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT  < ${BACKUP_DIRECTORY}/only_data_dump.sql  1> ${stdout} 2> ${stderr}
        if [ $? -eq 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source Data has been successfully migrated to target database." | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Failed to migrate data dump to target database. Please check the error and try again" | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          export MYSQL_PWD=""
          fn_display_message
          exit 1
        fi;
        export MYSQL_PWD=""
    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Data migration to target database has been skipped due to SKIP_RESTORE_OPERATION set to Y." | tee -a ${scriptlog}
    fi;

    if [[ "${SKIP_VALIDATION_AFTER_MIGRATION,,}" == "n" ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the Data Validation!!!" | tee -a ${scriptlog}

        echo "-----------------------------------------------------------------" > $DATA_COUNT_VALIDATION_FILE
        echo "DB Name | Table Name | Source Count | Target Count | Validation" >> $DATA_COUNT_VALIDATION_FILE
        echo "-----------------------------------------------------------------" >> $DATA_COUNT_VALIDATION_FILE

        read -ra dbs <<< "${final_db_list}"
        db_count=0

        for db in "${dbs[@]}"; do

            db_count=`expr ${db_count} + 1 `

            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Validating table data count for DB \"$db\" between Source and Target database..." | tee -a ${scriptlog}
            fn_validate_data_count $db

        done
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Data Validation Report has been generated.\n\nData Validation Report\t: $DATA_COUNT_VALIDATION_FILE\nData Mismatch Found\t: `grep MISMATCH $DATA_COUNT_VALIDATION_FILE | wc -l`\n" | tee -a ${scriptlog}
    else

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Data validation on target database has been skipped due to SKIP_VALIDATION_AFTER_MIGRATION set to Y." | tee -a ${scriptlog}

    fi;
}



fn_mydumper_only_data()
{

    if [[ "${SKIP_BACKUP_OPERATION,,}" == "n" ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the Data migration!!!" | tee -a ${scriptlog}

        regex_dblist="^($(echo "$final_db_list" | sed 's/ /\\.|/g')\\.)"

        ${mydumper_bin}/mydumper ${CONFIGURE_MYDUMPER} --no-schemas --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --password="${SOURCE_PASSWORD}" --port=$SOURCE_PORT --outputdir=${BACKUP_DIRECTORY}/mydumper  --regex=${regex_dblist} --logfile ${BACKUP_DIRECTORY}/mydumper.log

        if [ $? -eq 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source data has been exported.\n\nDump Location : ${BACKUP_DIRECTORY}/mydumper\n" | tee -a ${scriptlog}
        else
          echo -e "\n\n`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Unable to export data dump of source database!!!" | tee -a ${scriptlog}
          cat ${BACKUP_DIRECTORY}/mydumper.log | grep -i error | tee -a ${scriptlog}
          fn_display_message
          exit 1
        fi;
    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source data export has been skipped due to SKIP_BACKUP_OPERATION set to Y." | tee -a ${scriptlog}
    fi;

    if [[ "${SKIP_RESTORE_OPERATION,,}" == "n" ]]; then

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Importing the source data to target database!!!" | tee -a ${scriptlog}


        ${mydumper_bin}/myloader ${CONFIGURE_MYLOADER}  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --password="${TARGET_PASSWORD}" --port=$TARGET_PORT  --directory=${BACKUP_DIRECTORY}/mydumper  --logfile ${BACKUP_DIRECTORY}/myloader.log
        if [ $? -eq 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source Data has been successfully migrated to target database." | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Failed to migrate data dump to target database. Please check the error and try again" | tee -a ${scriptlog}
          cat ${BACKUP_DIRECTORY}/myloader.log | grep -i error | tee -a ${scriptlog}

          fn_display_message
          exit 1
        fi;

    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Data migration to target database has been skipped due to SKIP_RESTORE_OPERATION set to Y." | tee -a ${scriptlog}
    fi;

    if [[ "${SKIP_VALIDATION_AFTER_MIGRATION,,}" == "n" ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the Data Validation!!!" | tee -a ${scriptlog}

        echo "-----------------------------------------------------------------" > $DATA_COUNT_VALIDATION_FILE
        echo "DB Name | Table Name | Source Count | Target Count | Validation" >> $DATA_COUNT_VALIDATION_FILE
        echo "-----------------------------------------------------------------" >> $DATA_COUNT_VALIDATION_FILE

        read -ra dbs <<< "${final_db_list}"
        db_count=0

        for db in "${dbs[@]}"; do

            db_count=`expr ${db_count} + 1 `

            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Validating table data count for DB \"$db\" between Source and Target database..." | tee -a ${scriptlog}
            fn_validate_data_count $db

        done
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Data Validation Report has been generated.\n\nData Validation Report\t: $DATA_COUNT_VALIDATION_FILE\nData Mismatch Found\t: `grep MISMATCH $DATA_COUNT_VALIDATION_FILE|wc -l`\n" | tee -a ${scriptlog}
    else

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Data validation on target database has been skipped due to SKIP_VALIDATION_AFTER_MIGRATION set to Y." | tee -a ${scriptlog}

    fi;
}


fn_migrate_user()
{
    if [[ "${SKIP_BACKUP_OPERATION,,}" == "n" ]]; then

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the Database User migration!!!" | tee -a ${scriptlog}
        echo -e "\nExcluded Users from source : 'master','rdsadmin','mysql.sys','rep_user','root','mysql.infoschema','mysql.session','mysql.sys','tessell_monitor','rds_superuser_role'\n" | tee -a ${scriptlog}

        ${mysqlbin}/mysqlpump --set-gtid-purged=OFF --exclude_databases=% --exclude-users='master','rdsadmin','mysql.sys','rep_user','root','mysql.infoschema','mysql.session','mysql.sys','tessell_monitor','rds_superuser_role' --users --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --password="${SOURCE_PASSWORD}" > ${BACKUP_DIRECTORY}/only_users_dump.sql  2> ${stderr}

        if [ $? -eq 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source DB User(s) Dump has been exported.\n\nDump file : ${BACKUP_DIRECTORY}/only_users_dump.sql\n" | tee -a ${scriptlog}
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Filtering the unwanted line(s) from dump file!!!" | tee -a ${scriptlog}
          sed -i '/SET @@SESSION.SQL_LOG_BIN/d' ${BACKUP_DIRECTORY}/only_users_dump.sql
          sed -i 's/, LOAD FROM S3//g' ${BACKUP_DIRECTORY}/only_users_dump.sql
          sed -i 's/, SELECT INTO S3//g' ${BACKUP_DIRECTORY}/only_users_dump.sql
          sed -i 's/, INVOKE LAMBDA//g' ${BACKUP_DIRECTORY}/only_users_dump.sql
          sed -i 's/, INVOKE SAGEMAKER//g'  ${BACKUP_DIRECTORY}/only_users_dump.sql
          sed -i 's/, INVOKE COMPREHEND//g'  ${BACKUP_DIRECTORY}/only_users_dump.sql
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: DB user dump file is ready for use." | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Unable to export user dump of source database!!!" | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          fn_display_message
          exit 1
        fi;

    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source users export has been skipped due to SKIP_BACKUP_OPERATION set to Y." | tee -a ${scriptlog}
    fi;

    if [[ "${SKIP_RESTORE_OPERATION,,}" == "n" ]]; then

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Importing the Source db users to target database!!!" | tee -a ${scriptlog}


        MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT  < ${BACKUP_DIRECTORY}/only_users_dump.sql  1> ${stdout} 2> ${stderr}
        if [ $? -eq 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source DB users have been successfully migrated to target database." | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Failed to migrate users dump to target database. Please check the error and try again." | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          export MYSQL_PWD=""
          fn_display_message
          exit 1
        fi;
        export MYSQL_PWD=""

    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: User migration to target database has been skipped due to SKIP_RESTORE_OPERATION set to Y." | tee -a ${scriptlog}
    fi;

    if [[ "${SKIP_VALIDATION_AFTER_MIGRATION,,}" == "n" ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the DB Users Validation!!!" | tee -a ${scriptlog}

        echo "-----------------------------------------------------------------" > $USER_GRANTS_VALIDATION_FILE

        echo "Source User | Target User | Validation" >> $USER_GRANTS_VALIDATION_FILE
        echo "-----------------------------------------------------------------" >> $USER_GRANTS_VALIDATION_FILE

        for username in $(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select concat_ws(\"'\",\" \",user,\"@\",substring_index(host,':',1),\" \") from mysql.user;");
        do

            fn_validate_users $username

        done
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: User Validation Report has been generated.\n\nUser Validation Report\t: $USER_GRANTS_VALIDATION_FILE\nUser Mismatch Found\t: `grep MISMATCH $USER_GRANTS_VALIDATION_FILE|wc -l`\n" | tee -a ${scriptlog}
    else

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: User validation on target database has been skipped due to SKIP_VALIDATION_AFTER_MIGRATION set to Y." | tee -a ${scriptlog}

    fi;

}

fn_migrate_stored_programs()
{

    if [[ "${SKIP_BACKUP_OPERATION,,}" == "n" ]]; then

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the migration of TRIGGERS/ROUTINES/EVENTS!!!" | tee -a ${scriptlog}

        ${mysqlbin}/mysqldump --no-data --routines --events --triggers  --skip-set-charset  --no-create-info --no-create-db --skip-opt --column-statistics=0 --set-gtid-purged=OFF --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --password="${SOURCE_PASSWORD}"  --databases ${final_db_list} > ${BACKUP_DIRECTORY}/only_schema_stored_programs_dump.sql  2> ${stderr}

        if [ $? -eq 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Database stored programs such as trigger/event/routines have been exported to file.\n\nDump file : ${BACKUP_DIRECTORY}/only_schema_stored_programs_dump.sql\n" | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Unable to take dump of source database stored programs!!!" | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          fn_display_message
          exit 1
        fi;

    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source stored program(s) export has been skipped due to SKIP_BACKUP_OPERATION set to Y." | tee -a ${scriptlog}
    fi;

    if [[ "${SKIP_RESTORE_OPERATION,,}" == "n"  ]]; then

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Importing the database stored programs to target database!!!" | tee -a ${scriptlog}

        export MYSQL_PWD="$TARGET_PASSWORD"
        ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT  < ${BACKUP_DIRECTORY}/only_schema_stored_programs_dump.sql  1> ${stdout} 2> ${stderr}
        if [ $? -eq 0 ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source database stored programs have been migrated to target database." | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Failed to migrate source database stored programs to target database. Please check the error and try again" | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          export MYSQL_PWD=""
          fn_display_message
          exit 1
        fi;
        export MYSQL_PWD=""
    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Source DB stored programs migration to target database has been skipped due to SKIP_RESTORE_OPERATION set to Y." | tee -a ${scriptlog}
    fi;

    if [[ "${SKIP_VALIDATION_AFTER_MIGRATION,,}" == "n" ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the Validation for Stored Programs!!!" | tee -a ${scriptlog}
        echo "-----------------------------------------------------------------" > $STORED_PROGRAM_VALIDATION_FILE
        echo "DB Name | Stored Program | Source Count | Target Count | Validation" >> $STORED_PROGRAM_VALIDATION_FILE
        echo "-----------------------------------------------------------------" >> $STORED_PROGRAM_VALIDATION_FILE

        read -ra dbs <<< "${final_db_list}"
        db_count=0

        for db in "${dbs[@]}"; do

            db_count=`expr ${db_count} + 1 `

            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Validating for DB \"$db\" between Source and Target database..." | tee -a ${scriptlog}
            fn_validate_stored_program $db

        done
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Stored Program Validation Report has been generated.\n\nValidation Report\t: $STORED_PROGRAM_VALIDATION_FILE\nMismatch Found\t: `grep MISMATCH $STORED_PROGRAM_VALIDATION_FILE | wc -l`\n" | tee -a ${scriptlog}
    else

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Stored Program validation on target database has been skipped due to SKIP_VALIDATION_AFTER_MIGRATION set to Y." | tee -a ${scriptlog}

    fi;

}




#############################################################

#                   MAIN MODULE                             #

#############################################################

fn_initialize

NUM_OF_ARGUMENTS=$#

if [ ${NUM_OF_ARGUMENTS} = 0 ];then
	echo "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Please provide migration configuration file to the automation."
	echo -e "\nUsage:\n\tbash $0 migration_config_file.json\n"
	exit 1
else
  variable_file=$1
fi

if [ ! -f ${variable_file} ]; then
	echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Migration variable profile does not exist.\n"
	echo -e "\tVariable Profile\t: ${variable_file}\n"
	exit 1
fi;

# the option --trx-consistency-only is a required for transactional consistency while we take backup.

echo -e "\n`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Creating log directory!!!"

fn_generate_logdir

echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking the pre-requisites!!!" | tee -a ${scriptlog}

fn_check_package "mysql"

fn_check_package "mysqlpump"

fn_check_package "mydumper"

fn_check_package "jq"


echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Initialing the backup settings!!!\n" | tee -a ${scriptlog}

variables=$(jq -r '.variables | keys[]' "$variable_file")

for var in $variables; do

    value=$(jq -r ".variables.\"$var\".value" "$variable_file")

    # Check if the variable value is empty

    if [[ -z "$value" ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: The value for $var is empty." | tee -a ${scriptlog}
        fn_display_message
        exit 1
    fi

    # Check if variable is a port number and validate it's an integer

    if [[ "$var" == "SOURCE_PORT" || "$var" == "TARGET_PORT" ]]; then
        if ! [[ "$value" =~ ^[0-9]+$ ]]; then
            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Variable $var should be an integer. Provided value: $value" | tee -a ${scriptlog}
            fn_display_message
            exit 1
        fi
    fi

    # Check if variable is a boolean flag and validate it's Y or N

    if [[ "$var" == "SKIP_SCHEMA_COMPATIBILITY_CHECKS" || \
          "$var" == "SKIP_BACKUP_OPERATION" || \
          "$var" == "SKIP_RESTORE_OPERATION" || \
          "$var" == "SKIP_USER_MIGRATION" || \
          "$var" == "SKIP_VALIDATION_AFTER_MIGRATION" || \
          "$var" == "SKIP_SLACK_NOTIFICATION" ]]; then
        if ! [[ "$value" == "Y" || "$value" == "N" ]]; then
            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Variable $var should be 'Y' or 'N'. Provided value: $value" | tee -a ${scriptlog}
            fn_display_message
            exit 1
        fi
    fi

    # Check if SELECT_MIGRATION_TYPE has an allowed value

    if [[ "$var" == "SELECT_MIGRATION_TYPE" ]]; then
        allowed_values=("only_tables" "only_data" "only_stored_programs" "tables_with_data" "only_user_migration" "all")
        if [[ ! " ${allowed_values[@]} " =~ " $value " ]]; then
            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Variable $var has an invalid value. \nAllowed values are: ${allowed_values[*]}. \nProvided value: $value" | tee -a ${scriptlog}
            fn_display_message
            exit 1
        fi
    fi

    export "$var"="$value"
    echo "$var=$value" >> ${scriptlog}
done

echo "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking the Backup directory existence!!!" | tee -a ${scriptlog}
if [ ! -d ${BACKUP_DIRECTORY} ]; then
     echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Backup directory does not exist, creating the provided backup directory!!!" | tee -a ${scriptlog}
     mkdir -p ${BACKUP_DIRECTORY}/mydumper 1> ${stdout} 2> ${stderr}
      if [ $? -ne 0 ]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Unable to create the backup directory. Please check and try again." | tee -a ${scriptlog}
        cat ${stderr} | tee -a ${scriptlog}
        fn_display_message
        exit 1
      else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Backup directory has been created." | tee -a ${scriptlog}
      fi;
else

    echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Backup directory already exists.\n" | tee -a ${scriptlog}

    # Check if the mydumper backup directory is empty
    if [[ "$(ls -A "${BACKUP_DIRECTORY}/mydumper" 2>/dev/null)" && "${BACKUP_TOOL}" == "mydumper" && "${SKIP_BACKUP_OPERATION,,}" == "n"  ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Backup directory is not empty. Please clear the directory ${BACKUP_DIRECTORY}/mydumper" | tee -a ${scriptlog}
        fn_display_message
        exit 1
    fi;

fi

# Check database connectivity
echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking Source database connection!!!" | tee -a ${scriptlog}

fn_check_db_connectivity $SOURCE_ENDPOINT $SOURCE_USERNAME $SOURCE_PASSWORD $SOURCE_PORT

echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking Target database connection!!!" | tee -a ${scriptlog}

fn_check_db_connectivity $TARGET_ENDPOINT $TARGET_USERNAME $TARGET_PASSWORD $TARGET_PORT

if [[ "${SKIP_SCHEMA_COMPATIBILITY_CHECKS,,}" == "n" ]]; then
    echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking Primary Key or equivalent unique key existence on source tables!!!" | tee -a ${scriptlog}

    MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nr -e "SELECT concat_ws('.',t.table_schema,t.table_name)
FROM information_schema.tables t INNER JOIN information_schema .columns c
on t.table_schema=c.table_schema and t.table_name=c.table_name
where t.table_schema not in ('mysql', 'sys', 'information_schema', 'performance_schema','mysql_innodb_cluster_metadata')
GROUP BY t.table_schema,t.table_name
HAVING sum(if(column_key in ('PRI','UNI'), 1,0)) =0;" > ${script_folder}/pk_check.txt 2> ${stderr}

    if [ $? -eq 0 ]; then
      if [ ! -s ${script_folder}/pk_check.txt ]; then
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Success.\n" | tee -a ${scriptlog}
      else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Primary Key or equivalent unique key existence check on source tables has been failed.\n\nTable(s) without PK/UK:" | tee -a ${scriptlog}
          cat ${script_folder}/pk_check.txt | tee -a ${scriptlog}
          export MYSQL_PWD=""
          fn_display_message
          exit 1
      fi;
    else
      echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Unable to check Primary Key or equivalent unique key existence on source tables.!!!" | tee -a ${scriptlog}
      cat ${stderr} | tee -a ${scriptlog}
      export MYSQL_PWD=""
      fn_display_message
      exit 1
    fi;

    export MYSQL_PWD=""

fi;

if [[ "${SKIP_SCHEMA_COMPATIBILITY_CHECKS,,}" == "n" ]]; then
    echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking value of system variable log_bin_trust_function_creators on Target database!!!" | tee -a ${scriptlog}

    check_trigger_count=$(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select count(1) from information_schema.TRIGGERS where TRIGGER_SCHEMA not in ('mysql', 'sys', 'information_schema', 'performance_schema','mysql_innodb_cluster_metadata')")
    check_routine_count=$(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select count(1) from information_schema.ROUTINES where ROUTINE_SCHEMA not in ('mysql', 'sys', 'information_schema', 'performance_schema','mysql_innodb_cluster_metadata')")
    check_event_count=$(MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A  --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -Nrs -e "select count(1) from information_schema.EVENTS where EVENT_SCHEMA not in ('mysql', 'sys', 'information_schema', 'performance_schema','mysql_innodb_cluster_metadata')")

    if [[ $check_event_count -ne 0 || \
          $check_routine_count -ne 0 || \
          $check_routine_count -ne 0 ]]; then

          check_log_bin_trust=$(MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e  "show global variables like 'log_bin_trust_function_creators'" | grep ON | wc -l)
          if [[ $check_log_bin_trust -eq 1 ]]; then
              echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Success.\n" | tee -a ${scriptlog}
          else
              echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: System variable log_bin_trust_function_creators on Target database is not set to 1." | tee -a ${scriptlog}
              export MYSQL_PWD=""
              fn_display_message
              exit 1
          fi;
    else

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Success.\n" | tee -a ${scriptlog}

    fi

fi;


fn_check_privileges $TARGET_ENDPOINT $TARGET_USERNAME $TARGET_PASSWORD $TARGET_PORT 'target' 'SET_USER_ID'

if [[ "${BACKUP_TOOL}" == "mydumper" ]]; then

    #fn_check_privileges $SOURCE_ENDPOINT $SOURCE_USERNAME $SOURCE_PASSWORD $SOURCE_PORT 'source' 'BACKUP_ADMIN'
    fn_check_privileges $SOURCE_ENDPOINT $SOURCE_USERNAME $SOURCE_PASSWORD $SOURCE_PORT 'source' 'SESSION_VARIABLES_ADMIN'
    fn_check_privileges $TARGET_ENDPOINT $TARGET_USERNAME $TARGET_PASSWORD $TARGET_PORT 'target' 'SESSION_VARIABLES_ADMIN'
    fn_check_privileges $TARGET_ENDPOINT $TARGET_USERNAME $TARGET_PASSWORD $TARGET_PORT 'target' 'INNODB_REDO_LOG_ENABLE'
fi;

if [[ "${ENABLE_REPLICATION_AFTER_MIGRATION,,}" == "y" ]]; then

    if [[ "${BACKUP_TOOL,,}" != "mydumper" ]]; then
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Data replication is not supported for tool 'mysqldump'. You must specify 'mydumper' for 'BACKUP_TOOL' setting." | tee -a ${scriptlog}
        export MYSQL_PWD=""
        fn_display_message
        exit 1
    fi;

    echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking if replication channel '$repl_channel' does not exist on target database!!!" | tee -a ${scriptlog}
    repl_check=`MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "select count(1) from performance_schema.replication_connection_status where CHANNEL_NAME='$repl_channel';" 2> ${stderr}`

    if [[ $repl_check -eq 1 ]]; then

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Replication channel '$repl_channel' already exists on target database. Please cleanup the existing replication channel before you proceed!!!" | tee -a ${scriptlog}
        cat ${stderr} | tee -a ${scriptlog}
        export MYSQL_PWD=""
        fn_display_message
        exit 1
    else
        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Success.\n" | tee -a ${scriptlog}
    fi;


    fn_check_privileges $SOURCE_ENDPOINT $SOURCE_USERNAME $SOURCE_PASSWORD $SOURCE_PORT 'source' 'REPLICATION SLAVE'
    fn_check_privileges $SOURCE_ENDPOINT $SOURCE_USERNAME $SOURCE_PASSWORD $SOURCE_PORT 'source' 'REPLICATION CLIENT'
    fn_check_privileges $TARGET_ENDPOINT $TARGET_USERNAME $TARGET_PASSWORD $TARGET_PORT 'target' 'REPLICATION_SLAVE_ADMIN'

    fn_check_binlog_settings $SOURCE_ENDPOINT $SOURCE_USERNAME $SOURCE_PASSWORD $SOURCE_PORT


fi;


echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the migration process for the selected type: ${SELECT_MIGRATION_TYPE}" | tee -a ${scriptlog}

echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Fetching the database list!!!" | tee -a ${scriptlog}

if [[ "$SELECT_DATABASES" == "ALL" || "$SELECT_DATABASES" == "all" ]]; then


    MYSQL_PWD="$SOURCE_PASSWORD" ${mysqlbin}/mysql -A -Nrs --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT  -e "show databases;" 1> ${stdout} 2> ${stderr}

    if [ $? -eq 0 ]; then

      final_db_list=`cat ${stdout} | egrep -v '^information_schema$|^mysql$|^sys$|^innodb$|^performance_schema$' | xargs `
      echo -e "\nDB List : ${final_db_list} \n" | tee -a ${scriptlog}
    else
      echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Unable to fetch list of database(s) from given db instance!!!" | tee -a ${scriptlog}
      cat ${stderr} | tee -a ${scriptlog}
      export MYSQL_PWD=""
      fn_display_message
      exit 1
    fi;
    repl_ignore_db="information_schema, mysql, performance_schema, sys, mysql_innodb_cluster_metadata"
    export MYSQL_PWD=""
else
    echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Validating the provided database list!!!\nDB List : $SELECT_DATABASES" | tee -a ${scriptlog}
    fn_validate_database_list $SOURCE_ENDPOINT $SOURCE_USERNAME $SOURCE_PASSWORD $SOURCE_PORT $SELECT_DATABASES
    final_db_list=`echo ${SELECT_DATABASES} | sed 's/,/ /g'`
    repl_do_db=$SELECT_DATABASES
fi;

if [[ "${SELECT_MIGRATION_TYPE,,}" == "only_tables" ]]; then

  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  fn_migrate_schema
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}

elif [[ "${SELECT_MIGRATION_TYPE,,}" == "only_data" ]]; then

  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  if [[ "${BACKUP_TOOL}" == "mysqldump" ]]; then
      fn_migrate_data
  else
      fn_mydumper_only_data
  fi;
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}

elif [[ "${SELECT_MIGRATION_TYPE,,}" == "tables_with_data" ]]; then

  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  fn_migrate_schema
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  if [[ "${BACKUP_TOOL}" == "mysqldump" ]]; then
      fn_migrate_data
  else
      fn_mydumper_only_data
  fi;
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  #fn_migrate_stored_programs
  #echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}

elif [[ "${SELECT_MIGRATION_TYPE,,}" == "only_stored_programs" ]]; then

  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  fn_migrate_stored_programs
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}

elif [[ "${SELECT_MIGRATION_TYPE,,}" == "only_user_migration" ]]; then

  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  fn_migrate_user
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}

elif [[ "${SELECT_MIGRATION_TYPE,,}" == "all" ]]; then
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  fn_migrate_schema
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  fn_migrate_user
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  if [[ "${BACKUP_TOOL}" == "mysqldump" ]]; then
      fn_migrate_data
  else
      fn_mydumper_only_data
  fi;
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}
  fn_migrate_stored_programs
  echo -e "------------------------------------------------------------------------\n" | tee -a ${scriptlog}

fi;

if [[ "${ENABLE_REPLICATION_AFTER_MIGRATION,,}" == "y" ]]; then

    echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Setting up data replication on target database!!!" | tee -a ${scriptlog}

    if [ -f ${BACKUP_DIRECTORY}/mydumper/metadata ]; then
        SOURCE_LOG_FILE=`grep "^File" ${BACKUP_DIRECTORY}/mydumper/metadata | cut -d"=" -f2 | sed 's/^[ \t]*//'`
        SOURCE_LOG_POS=`grep "^Position" ${BACKUP_DIRECTORY}/mydumper/metadata | cut -d"=" -f2 | sed 's/^[ \t]*//'`

        # Run the CHANGE REPLICATION SOURCE TO command on the target db
        MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "CHANGE REPLICATION SOURCE TO \
        SOURCE_HOST='$SOURCE_ENDPOINT', \
        SOURCE_USER='$SOURCE_USERNAME', \
        SOURCE_PASSWORD='$SOURCE_PASSWORD', \
        SOURCE_LOG_FILE = '$SOURCE_LOG_FILE', \
        SOURCE_LOG_POS=$SOURCE_LOG_POS, \
        SOURCE_AUTO_POSITION = 0 \
        FOR CHANNEL '$repl_channel';" 1> ${stdout} 2> ${stderr}

        if [ $? -eq 0 ]; then

          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Successfully configured replication channel in target database for data replication." | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Failed to configure replication channel in the target database!!!" | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          export MYSQL_PWD=""
          fn_display_message
          exit 1
        fi;

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Applying schema replication filter(s) in target database!!!" | tee -a ${scriptlog}

        if [[ "$SELECT_DATABASES" == "ALL" || "$SELECT_DATABASES" == "all" ]]; then

            replication_filter="REPLICATE_IGNORE_DB = (${repl_ignore_db})"

        else

            replication_filter="REPLICATE_DO_DB = (${repl_do_db})"

        fi;

        # Run the CHANGE REPLICATION FILTER command on the target db
        MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "CHANGE REPLICATION FILTER ${replication_filter} FOR CHANNEL '$repl_channel';" 1> ${stdout} 2> ${stderr}

        if [ $? -eq 0 ]; then

          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Successfully applied schema replication filter(s) in target database for data replication." | tee -a ${scriptlog}
          echo -e "\n${replication_filter}\n" | tee -a ${scriptlog}
        else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Failed to configure schema replication filter(s) in the target database!!!" | tee -a ${scriptlog}
          cat ${stderr} | tee -a ${scriptlog}
          export MYSQL_PWD=""
          fn_display_message
          exit 1
        fi;

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Checking GTID Mode setting on source database!!!" | tee -a ${scriptlog}
        MYSQL_PWD=${SOURCE_PASSWORD} ${mysqlbin}/mysql -A -Nrs --host=$SOURCE_ENDPOINT --user=$SOURCE_USERNAME --port=$SOURCE_PORT -e "show global variables like 'gtid_mode';" | grep 'ON' 1> ${stdout} 2> ${stderr}
        if [ $? -ne 0 ]; then

            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: GTID is disabled on source database." | tee -a ${scriptlog}
            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Configuring replication channel to use ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS=LOCAL parameter that allows target db to assign new GTID for every non-GTID transaction received from the source db." | tee -a ${scriptlog}

            MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "CHANGE REPLICATION SOURCE TO ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS=LOCAL FOR CHANNEL '$repl_channel';" 1> ${stdout} 2> ${stderr}

            if [ $? -eq 0 ]; then

              echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Successfully configured replication channel to assign new GTID for every non-GTID transaction received from the source db." | tee -a ${scriptlog}

            else
              echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Failed to enable replication channel with ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS=LOCAL!!!" | tee -a ${scriptlog}
              cat ${stderr} | tee -a ${scriptlog}
              export MYSQL_PWD=""
              fn_display_message
              exit 1
            fi;

        fi;

        echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Starting the data replication on target database!!!" | tee -a ${scriptlog}

        # Start replication on the target db
        MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -Nrs -e "START REPLICA FOR CHANNEL '$repl_channel';" 1> ${stdout} 2> ${stderr}

        if [ $? -eq 0 ]; then
            sleep 15
            MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -e "SHOW REPLICA STATUS FOR CHANNEL '$repl_channel'\G" | egrep -i 'Replica_IO_Running:|Replica_SQL_Running:' | grep -v Yes | wc -l 1> ${stdout} 2> ${stderr}
            if [ `cat ${stdout}` -eq 0 ]; then

                echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Data replication is running smoothly and both replication IO and SQL threads are active." | tee -a ${scriptlog}

            else
                echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Data replication is not running on target database. Please check the issue!!!\n" | tee -a ${scriptlog}

                MYSQL_PWD="$TARGET_PASSWORD" ${mysqlbin}/mysql -A  --host=$TARGET_ENDPOINT --user=$TARGET_USERNAME --port=$TARGET_PORT -e "SHOW REPLICA STATUS FOR CHANNEL '$repl_channel'\G" | egrep -i 'Last_IO_Error:|Last_SQL_Error:' | tee -a ${scriptlog}
                echo -e "\n" | tee -a ${scriptlog}
                cat ${stderr} | tee -a ${scriptlog}
                export MYSQL_PWD=""
                fn_display_message
                exit 1
            fi;


        else
            echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Failed to start data replication on target database.!!!" | tee -a ${scriptlog}
            cat ${stderr} | tee -a ${scriptlog}
            export MYSQL_PWD=""
            fn_display_message
            exit 1
        fi;

    else
          echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:ERROR: Could not find mydumper metadata file to enable data replication!!!" | tee -a ${scriptlog}
          fn_display_message
          exit 1
    fi


fi;

echo -e "`date +"%Y-%m-%dT%H:%M:%S"`:INFO: Migration Automation has been completed!!!" | tee -a ${scriptlog}
fn_display_message

echo -e "\n\n###########################################################\n###                        End                          ###\n###########################################################\n" | tee -a ${scriptlog}




