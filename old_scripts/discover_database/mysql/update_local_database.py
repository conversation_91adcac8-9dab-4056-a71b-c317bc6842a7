from tessell.plugin.database.mysql.tasks.utils.MySQLTaskUtils import MySQLTaskUtils
from tessell.plugin.database.mysql.mysql_queries import MySQLQueries
import base64, json, requests, pprint

LOG_SWEEP_CONFIG_FILE = "/opt/tessell-base/.tessell/log_sweep/logSweepConfig.json"
tenant_id = service_id = service_instance_id = ""
rpc_url = "http://localhost:8081/rpc"
rpc_port = 8080
page_size = 100
engine_type = "MYSQL"

def read_log_sweep_config():
    f = open(LOG_SWEEP_CONFIG_FILE, )
    logSweepConfig = json.load(f)
    logSweepConfig = logSweepConfig["log_sweep_config"]
    if 'serviceId' in logSweepConfig:
        service_id = logSweepConfig["serviceId"]
    else:
        print("Failed to get services from logSweepConfig")
        exit(1)
    tenant_id = logSweepConfig['tenantId']
    return service_id, tenant_id


class RPCRequest:

    def __init__(self, service_id,tenant_id ):
        self.service_id = service_id
        self.tenant_id = tenant_id


    def get_rpc_data_payload(self,service , port, get_endpoint, timeout=900):
        print("End Point Triggered: {}".format(get_endpoint))
        modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}
        rpc_payload = {
            "rpcName": "get_data",
            "service": service,
            "port": port,
            "method": "GET",
            "endpoint": get_endpoint,
            "headers": modified_headers,
            "timeout": timeout,
        }
        response_from_request = requests.post(
            "http://localhost:8081/rpc",
            data=json.dumps(rpc_payload),
            headers={"Content-type": "application/json"},
            verify=False,
            timeout=900,
        )
        if response_from_request.status_code:
            print("get metadata response status code: " + str(response_from_request.status_code))

        response_json = json.loads(response_from_request.content.decode())
        response_json = base64.b64decode(response_json['payload']).decode('utf-8')
        response_json = json.loads(response_json)
        print(f"response_json is:")
        pprint.pprint(response_json)
        return response_json

    def post_rpc_data_payload(self, service, data, port, endpoint,request="POST", timeout=900):
        print("End Point Triggered: {}".format(endpoint))

        data = json.dumps(data, ensure_ascii=False)
        data = data.encode()
        modified_headers = {"Content-Type": "application/json", "tenant-id": tenant_id}

        rpc_payload = {
            "rpcName": "post_or_update_tessell_service",
            "service": service,
            "port": port,
            "method": request,
            "endpoint": endpoint,
            "payload": base64.b64encode(data).decode("utf-8"),
            "headers": modified_headers,
            "timeout": timeout,
        }
        response_from_request = requests.post(
            "http://localhost:8081/rpc",
            data=json.dumps(rpc_payload),
            headers={"Content-type": "application/json"},
            verify=False,
            timeout=900,
        )
        if response_from_request.status_code:
            print(f"{request } metadata response status code: " + str(response_from_request.status_code))

        response_json = json.loads(response_from_request.content.decode())
        response_json = base64.b64decode(response_json['payload']).decode('utf-8')
        response_json = json.loads(response_json)
        print(f"response_json is:")
        pprint.pprint(response_json)
        return response_json


class MySQLGatherInfo:
    def __init__(self):
        self.taskUtils = MySQLTaskUtils()


    def get_all_databases(self):
        database_list = set()
        # query = "SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'mysql_innodb_cluster_metadata');"
        query = MySQLQueries.GET_USER_CREATED_DATABASE
        query_output = self.taskUtils.query_db_using_mysqlsh(query)
        for database in query_output:
            database_list.add(database["SCHEMA_NAME"])
        print("Database List: {}".format(database_list))
        return database_list


class OpsOperations:
    def __init__(self):
        self.mysql_gather_info = MySQLGatherInfo()
        self.service_id, self.tenant_id = read_log_sweep_config()
        self.rpc = RPCRequest(service_id=service_id, tenant_id=tenant_id)
        self.already_present_list = []
        self.recently_added_list = []
        self.database_not_added_to_tessell_metadata = []


    def get_tessell_databases(self):

        service = "tessell-database-system"
        get_endpoint = f"/tessell-ops/services/{self.service_id}/databases?page_size={page_size}"
        response_json = self.rpc.get_rpc_data_payload(service=service, port=rpc_port, get_endpoint=get_endpoint)
        tessell_database_list_info = response_json['response']
        return tessell_database_list_info

    def post_tessell_metadata(self, db_name):
        service = "tessell-database-system"
        post_endpoint = f"/tessell-ops/services/{self.service_id}/databases"
        data = {
            "name": db_name,
            "description": "Added by ops framework",
            "tessellServiceId": self.service_id,
            "engineType": engine_type,
            "status": "READY",
            "databaseConfiguration": {
                "userView": {
                    "oracleConfig": None,
                    "postgresqlConfig": None,
                    "mySqlConfig": {
                        "parameterProfileId": None
                    },
                    "sqlServerConfig": None,
                    "mongodbConfig": None,
                    "milvusConfig": None
                },
                "internalInfo": {
                    "optionsProfileId": None,
                    "parameterProfileId": None,
                    "secretId": None
                }
            }
        }
        try:
            response_json = self.rpc.post_rpc_data_payload(service=service, data=data, port=rpc_port,
                                                           endpoint=post_endpoint)
            return True
        except Exception as e:
            print(f"Failed to add database {db_name} to tessell metadata. Error: {e}")
            return False

    def get_all_databases(self):
        db_database_list = self.mysql_gather_info.get_all_databases()
        return db_database_list

    def add_database_to_tessell_metadata(self, db_name):
        response = input(f"Do you want to add {db_name} to tessell metadata? (yes/no)")
        if response.lower() == 'yes':
            # Run the code you want to execute here
            print("This will add data to tessell-repo")
            if self.post_tessell_metadata(db_name):
                print(f"Database {db_name} added to tessell metadata")
                return True
            else:
                print(f"Database {db_name} not added to tessell metadata")
                return False
        elif response.lower() == 'no':
            print("Answer was no so not adding ro tessell metadata")
            return False
        else:
            print("Answer was not yes or no so not adding to tessell metadata")
            return False

    def validata_and_update_local_database_in_tessell_metadata(self, db_name, tessell_database_list_info):
        db_present_in_tessell = False
        for db_info in tessell_database_list_info:
            if db_name == db_info['name'] and db_info['status'].lower() in "READY".lower():
                db_present_in_tessell = True
                break

        if not db_present_in_tessell:
            print(f"Database {db_name} is not present in tessell metadata. Adding it now")
            if self.add_database_to_tessell_metadata(db_name):
                self.recently_added_list.append(db_name)
            else:
                self.database_not_added_to_tessell_metadata.append(db_name)
        else:
            print(f"Database {db_name} is present in tessell metadata. Status: {db_info['status']}")
            self.already_present_list.append(db_name)


    def verify_databases(self):
        db_database_list = self.get_all_databases()
        tessell_database_list_info = self.get_tessell_databases()
        for local_db_name in db_database_list:
            self.validata_and_update_local_database_in_tessell_metadata(local_db_name, tessell_database_list_info)

        print(f"Databases already present in tessell metadata. Status: {self.already_present_list}\n")
        print(f"Databases recently added to tessell metadata. Status: {self.recently_added_list}\n")
        print(f"Databases is not added to tessell metadata. Status: {self.database_not_added_to_tessell_metadata}")






if __name__ == "__main__":
    # mysql_gather_info = MySQLGatherInfo()
    # mysql_gather_info.get_all_databases()
    ops_operations = OpsOperations()
    ops_operations.verify_databases()
