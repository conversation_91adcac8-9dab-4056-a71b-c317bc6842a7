#Requirements:
#1. rep_user user should be created with replication privileges
#2. run below queries on migrated database
# CREATE USER 'rep_user'@'%' IDENTIFIED WITH mysql_native_password BY '$replication_password';
# GRANT SELECT, RELOAD, SHUTDOWN, PROCESS, FILE, EXECUTE, R<PERSON><PERSON><PERSON><PERSON><PERSON> SLAVE, REPLICATION CLIENT, CREATE USER ON *.* TO 'rep_user'@'%' WITH GRANT OPTION;
# GRANT CLONE_ADMIN,CONNECTION_ADMIN,GROUP_REPLICATION_ADMIN,PERSIST_RO_VARIABLES_ADMIN,REPL<PERSON>ATION_APPLIER,REPL<PERSON>ATION_SLAVE_ADMIN,ROLE_ADMIN,SYSTEM_VARIABLES_ADMIN ON *.* TO 'rep_user'@'%' WITH GRANT OPTION;
# GRANT INSERT, UPDATE, DELETE ON mysql.* TO 'rep_user'@'%' WITH GRANT OPTION;
# GRANT INSERT, UPDATE, <PERSON>LETE, CREATE, DROP, R<PERSON>ERENCES, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES, EXECUTE, CREATE VIEW, SHOW VIEW, CREATE ROUTINE, ALTER ROUTINE, EVENT, TRIGGER ON mysql_innodb_cluster_metadata.* TO 'rep_user'@'%' WITH GRANT OPTION;
# GRANT INSERT, UPDATE, DELETE, CREATE, DROP, REFERENCES, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES, EXECUTE, CREATE VIEW, SHOW VIEW, CREATE ROUTINE, ALTER ROUTINE, EVENT, TRIGGER ON mysql_innodb_cluster_metadata_bkp.* TO 'rep_user'@'%' WITH GRANT OPTION;
# GRANT INSERT, UPDATE, DELETE, CREATE, DROP, REFERENCES, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES, EXECUTE, CREATE VIEW, SHOW VIEW, CREATE ROUTINE, ALTER ROUTINE, EVENT, TRIGGER ON mysql_innodb_cluster_metadata_previous.* TO 'rep_user'@'%' WITH GRANT OPTION;
# FLUSH PRIVILEGES;

# How to run this script
# ./configure_mysql_login_cred.sh <replication_password> <root_password>
source ~/.bash_profile
replication_password=$1
root_password=$2
#added this part to reset the .login-cnf incase it exists
#This case is added bec image might have .login-cnf with some data and also while clone source creates might be there
#in .login-cnf
/mysqlbin/bin/mysql_config_editor reset

#Root password
#added root password to .login-cnf using mysqlsh
#we can also use mysql_config_editor directly but there is an interactive session is required which we want to avoid or
#handle separately

mysqlsh root@localhost:3306 --password=$root_password  --py --credential-store-helper="default" -- shell store_credential "root@localhost:3306" "$root_password"

rc=$?
if [ $rc != 0 ]; then
echo "Failed to update MYSQL to login-path for root user! Return Code : $rc"
exit $rc
fi

#Replica Password

mysqlsh rep_user@localhost:3306 --password=$replication_password  --py --credential-store-helper="default" -- shell store_credential "rep_user@localhost:3306" "$replication_password"

rc=$?
if [ $rc != 0 ]; then
echo "Failed to update MYSQL to login-path for rep_user user! Return Code : $rc"
exit $rc
fi