SET SESSION sql_mode = '';

-- MySQL version info
SET @version_info = (
  SELECT JSON_OBJECTAGG(variable_name, variable_value)
  FROM performance_schema.global_variables
  WHERE variable_name LIKE 'version%'
);

-- Read-only status
SET @read_only = (
  SELECT JSON_OBJECTAGG(variable_name, variable_value)
  FROM performance_schema.global_variables
  WHERE variable_name = 'read_only'
);

-- Replication status
SET @replication_status = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT(
      'channel', IFNULL(NULLIF(CHANNEL_NAME, ''), 'Default'),
      'state', SERVICE_STATE
    )
  )
  FROM performance_schema.replication_applier_status
);

-- Database list
SET @database_list = (
  SELECT JSON_OBJECTAGG(SCHEMA_NAME, SCHEMA_NAME)
  FROM information_schema.SCHEMATA
  WHERE SCHEMA_NAME NOT IN ('mysql', 'sys', 'information_schema', 'performance_schema', 'mysql_innodb_cluster_metadata')
);

-- Database sizes
SET @database_sizes = (
  SELECT JSON_OBJECTAGG(schema_name, size_mb)
  FROM (
    SELECT
      s.SCHEMA_NAME AS schema_name,
      ROUND(COALESCE(SUM(t.data_length + t.index_length + t.data_free), 0) / 1024 / 1024, 2) AS size_mb
    FROM information_schema.SCHEMATA s
    LEFT JOIN information_schema.TABLES t ON s.SCHEMA_NAME = t.TABLE_SCHEMA
    WHERE s.SCHEMA_NAME NOT IN ('mysql', 'sys', 'information_schema', 'performance_schema', 'mysql_innodb_cluster_metadata')
    GROUP BY s.SCHEMA_NAME
  ) AS derived
);


-- Non-InnoDB tables
SET @non_innodb_tables = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT('schema', table_schema, 'table', table_name, 'engine', engine)
  )
  FROM information_schema.tables
  WHERE table_schema NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys')
    AND engine != 'InnoDB'
    AND table_type = 'BASE TABLE'
);

-- Tables without primary/unique keys
SET @tables_without_keys = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT('schema', sub.table_schema, 'table', sub.table_name)
  )
  FROM (
    SELECT t.table_schema, t.table_name
    FROM information_schema.tables t
    INNER JOIN information_schema.columns c
      ON t.table_schema = c.table_schema AND t.table_name = c.table_name
    WHERE t.table_schema NOT IN ('mysql', 'sys', 'information_schema', 'performance_schema', 'mysql_innodb_cluster_metadata')
      AND t.table_type != 'VIEW'
    GROUP BY t.table_schema, t.table_name
    HAVING SUM(IF(c.column_key IN ('PRI', 'UNI'), 1, 0)) = 0
  ) AS sub
);

-- LOB tables without primary keys
SET @lob_tables_without_pk = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT('schema', table_schema, 'table', table_name)
  )
  FROM information_schema.columns
  WHERE table_schema NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'innodb') 
    AND data_type IN ('tinyblob', 'blob', 'mediumblob', 'longblob', 'tinytext', 'text', 'mediumtext', 'longtext', 'json', 'binary')
    AND table_name NOT IN (
      SELECT DISTINCT table_name
      FROM information_schema.table_constraints
      WHERE constraint_type IN ('PRIMARY KEY', 'UNIQUE')
    )
    AND table_name NOT IN (
      SELECT DISTINCT table_name
      FROM information_schema.tables
      WHERE table_type = 'VIEW'
    )
);

-- NOT NULL LOB columns
SET @not_null_lob_columns = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT('schema', TABLE_SCHEMA, 'table', TABLE_NAME, 'column', COLUMN_NAME, 'data_type', DATA_TYPE)
  )
  FROM information_schema.COLUMNS
  WHERE DATA_TYPE IN ('tinyblob', 'blob', 'mediumblob', 'longblob', 'tinytext', 'text', 'mediumtext', 'longtext', 'json', 'binary')
    AND IS_NULLABLE = 'NO'
    AND TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'innodb')
);

-- Foreign keys with cascade
SET @cascade_constraints = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT(
      'schema', kcu.TABLE_SCHEMA,
      'table', kcu.TABLE_NAME,
      'column', kcu.COLUMN_NAME,
      'ref_table', kcu.REFERENCED_TABLE_NAME,
      'ref_column', kcu.REFERENCED_COLUMN_NAME,
      'on_update', rc.UPDATE_RULE,
      'on_delete', rc.DELETE_RULE
    )
  )
  FROM information_schema.REFERENTIAL_CONSTRAINTS rc
  JOIN information_schema.KEY_COLUMN_USAGE kcu
    ON rc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
    AND rc.CONSTRAINT_SCHEMA = kcu.CONSTRAINT_SCHEMA
  WHERE (rc.UPDATE_RULE = 'CASCADE' OR rc.DELETE_RULE = 'CASCADE')
    AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
);

-- Table counts
SET @table_counts = (
  SELECT JSON_OBJECTAGG(schema_name, table_count)
  FROM (
    SELECT
      s.SCHEMA_NAME AS schema_name,
      COUNT(t.table_name) AS table_count
    FROM information_schema.SCHEMATA s
    LEFT JOIN information_schema.TABLES t
      ON s.SCHEMA_NAME = t.TABLE_SCHEMA
    WHERE s.SCHEMA_NAME NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'innodb')
    GROUP BY s.SCHEMA_NAME
  ) AS sub
);


-- Table sizes
SET @table_sizes = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT(
      'schema', schema_name,
      'table', table_name,
      'rows', table_rows,
      'size_mb', size_mb
    )
  )
  FROM (
    SELECT
      s.SCHEMA_NAME AS schema_name,
      t.TABLE_NAME AS table_name,
      t.TABLE_ROWS AS table_rows,
      ROUND(COALESCE(t.DATA_LENGTH + t.INDEX_LENGTH + t.DATA_FREE, 0) / 1024 / 1024, 2) AS size_mb
    FROM information_schema.SCHEMATA s
    JOIN information_schema.TABLES t ON s.SCHEMA_NAME = t.TABLE_SCHEMA
    WHERE s.SCHEMA_NAME NOT IN ('information_schema', 'sys', 'innodb', 'mysql', 'performance_schema')
  ) AS sub
);


-- Triggers
SET @triggers = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT('schema', TRIGGER_SCHEMA, 'trigger', TRIGGER_NAME)
  )
  FROM information_schema.triggers
  WHERE TRIGGER_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'innodb')
);

-- Routines
SET @routines = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT('schema', ROUTINE_SCHEMA, 'routine', ROUTINE_NAME)
  )
  FROM information_schema.ROUTINES
  WHERE ROUTINE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'innodb')
);

-- Views
SET @views = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT('schema', TABLE_SCHEMA, 'view', TABLE_NAME)
  )
  FROM information_schema.VIEWS
  WHERE TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'innodb')
);

-- Events
SET @events = (
  SELECT JSON_ARRAYAGG(
    JSON_OBJECT('schema', EVENT_SCHEMA, 'event', EVENT_NAME)
  )
  FROM information_schema.EVENTS
  WHERE EVENT_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'innodb')
);

-- Global parameters
SET @all_globals = (
  SELECT JSON_OBJECTAGG(variable_name, variable_value)
  FROM performance_schema.global_variables where variable_name not in ('ft_boolean_syntax')
);


-- Final summary
SELECT JSON_OBJECT(
  'VersionInfo',             CAST(@version_info AS JSON),
  'ReadOnlyStatus',          CAST(@read_only AS JSON),
  'ReplicationStatus',       CAST(@replication_status AS JSON),
  'Databases',               CAST(@database_list AS JSON),
  'DatabaseSizeMB',          CAST(@database_sizes AS JSON),
  'NonInnoDBTables',         CAST(@non_innodb_tables AS JSON),
  'TablesWithoutPK',         CAST(@tables_without_keys AS JSON),
  'LobTablesWithoutPK',      CAST(@lob_tables_without_pk AS JSON),
  'NotNullLOBColumns',       CAST(@not_null_lob_columns AS JSON),
  'CascadeConstraints',      CAST(@cascade_constraints AS JSON),
  'TableCounts',             CAST(@table_counts AS JSON),
  'TableSizesMB',            CAST(@table_sizes AS JSON),
  'Triggers',                CAST(@triggers AS JSON),
  'Routines',                CAST(@routines AS JSON),
  'Views',                   CAST(@views AS JSON),
  'Events',                  CAST(@events AS JSON),
  'GlobalParameters',        CAST(@all_globals AS JSON)
) AS full_summary;

