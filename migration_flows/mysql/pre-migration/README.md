# MySQL Premigration Assessment Automation

A comprehensive automation tool for performing premigration assessment checks on MySQL databases prior to migration.

## Features

This tool performs the following assessment checks:

### Database Configuration Checks
- ✅ Database connectivity verification
- ✅ Database version check
- ✅ Database status verification
- ✅ Read replica identification
- ✅ Replication status check
- ✅ Binary log configuration analysis
- ✅ Time zone configuration verification
- ✅ Server initialization parameter review
- ✅ Database parameter settings audit

### Schema Assessment
- ✅ Storage engine analysis (non-InnoDB tables)
- ✅ Table without Primary/Unique key validation
- ✅ Foreign key with cascade constraints identification
- ✅ LOB Table without PK/UK validation
- ✅ NOT NULL constraint on LOB fields check
- ✅ Table count per database
- ✅ Table size analysis
- ✅ Trigger identification
- ✅ Routine (stored procedures/functions) detection
- ✅ View identification
- ✅ Event scheduler identification

## Prerequisites

- MySQL Server must be 5.7 or higher 

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/mysql-premigration-assessment.git
   cd mysql-premigration-assessment

2. Set up your configuration file:

   Edit the configuration file with your database credentials and settings
   ```bash
   vi config.json

## Usage

   ```bash
    bash mysql_premigration_assessment.sh config.json 
   ```

## Output Files

The tool generates output files in the /tmp/premigration_assessment_timestamp output directory.

1. Assessment Log (premigration_assessment_timestamp.log)
- Details of all checks performed

2. JSON Report (premigration_assessment_report.json)
- Details of all checks performed in JSON format


## Sample Output Report

    "DatabaseSizeMB": {
        "db1": 0.03,
        "db2": 0.08,
        "test_db": 0.03
    },
    "ReadOnlyStatus": {
        "read_only": "OFF"
    },
    "NonInnoDBTables": [
        {
            "table": "tt1",
            "engine": "MyISAM",
            "schema": "db1"
        },
        {
            "table": "tt2",
            "engine": "MyISAM",
            "schema": "db1"
        },
        {
            "table": "t3",
            "engine": "MyISAM",
            "schema": "db2"
        }
    ]
