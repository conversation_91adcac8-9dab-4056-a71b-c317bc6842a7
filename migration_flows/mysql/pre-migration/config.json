{"variables": {"DB_ENDPOINT": {"description": "Provide database endpoint", "value": "127.0.0.1"}, "DB_USERNAME": {"description": "Provide database username", "value": "master"}, "DB_PASSWORD": {"description": "Provide database password", "value": "master"}, "DB_PORT": {"description": "Provide database port number", "value": "3306"}, "DB_BINARY_PATH": {"description": "Provide database binary path (ex: /usr/bin). You can find the MySQL binary path by running 'which mysql' in the terminal.", "value": "/mysqlbin/bin"}, "OUTPUT_DIRECTORY": {"description": "Provide the directory where script stores the automation log and reports.", "value": "/tmp"}}}