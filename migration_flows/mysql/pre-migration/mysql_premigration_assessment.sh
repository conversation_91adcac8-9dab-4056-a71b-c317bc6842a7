#!/bin/bash

fn_initialize()
{
    script_folder="${OUTPUT_DIRECTORY}/premigration_assessment_`date +%Y%m%d_%H%M%S`"
    stdout="${script_folder}/automation_stdout.log"
    stderr="${script_folder}/automation_stderr.err"
    scriptlog="${script_folder}/premigration_assessment_`date +%Y%m%d_%H%M%S`.log"
    json_report_file="${script_folder}/premigration_assessment_report.json"
    SQL_FILE="./mysql_assessment_queries.sql"
}

# Color codes for output
if [[ -t 1 ]]; then
  RED='\033[0;31m'
  GREEN='\033[0;32m'
  YELLOW='\033[1;33m'
  BLUE='\033[0;34m'
  NC='\033[0m' # No Color
else
  RED=''
  GREEN=''
  YELLOW=''
  BLUE=''
  NC=''
fi


# Function to get timestamp with both local and UTC time
get_timestamp() {
    local local_time=$(date +"%Y-%m-%d %H:%M:%S")
    local utc_time=$(date -u +"%Y-%m-%d %H:%M:%S")
    echo "${local_time} (UTC: ${utc_time})"
}

log_msg() {
    echo -e "$@"
}


# Function to print colored output with timestamps
log_info() {
    echo -e "${BLUE}[$(get_timestamp)] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(get_timestamp)] [SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(get_timestamp)] [WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(get_timestamp)] [ERROR]${NC} $1"
}

fn_display_message()
{
    log_msg "\nAssessment Log\t: ${scriptlog}"
    log_msg "\nAssessment JSON Report\t: ${json_report_file}"

    log_msg  "\n"
    export MYSQL_PWD=""

}

# Function to create script log directory
fn_generate_logdir()
{
    result=0
    if [ ! -d ${script_folder} ]; then
        mkdir -p ${script_folder}
        result=$?
        if [ $result -ne 0 ]; then
            log_error "Failed to create log directory ${script_folder}, please check the error.\n"
            exit 1
        else
            log_info "Successfully created log directory ${script_folder}\n"
            log_msg " " > ${scriptlog}
            log_msg "\n\n###########################################################\n###             Tessell Automation                     ###\n###########################################################\n" | tee -a ${scriptlog}
            log_msg "\n\tScript Name\t: $0\n\tStart Time\t: `date +"%Y-%m-%dT%H:%M:%S"`\n" | tee -a ${scriptlog}
        fi;
    fi;
}

#############################################################

##########           Main Module                   ##########

#############################################################

NUM_OF_ARGUMENTS=$#

if [ ${NUM_OF_ARGUMENTS} = 0 ];then
	log_error "Please provide premigration assessment configuration file to the automation."
	log_msg "\nUsage:\n\tbash $0 premigration_assessment_config.json\n"
	exit 1
else
  variable_file=$1
fi

if [ ! -f ${variable_file} ]; then
	log_error "Premigration assessment configuration file does not exist.\n"
	log_msg "\tConfiguration Profile\t: ${variable_file}\n"
	exit 1
fi;

log_info "Initialing the automation environmental variables!!!\n"

variables=$(jq -r '.variables | keys[]' "$variable_file")

for var in $variables; do

    value=$(jq -r ".variables.\"$var\".value" "$variable_file")

    # Check if the variable value is empty

    if [[ -z "$value" ]]; then
        log_error "The value for $var is empty."
        fn_display_message
        exit 1
    fi

    # Check if variable is a port number and validate if it's an integer

    if [[ "$var" == "DB_PORT" ]]; then
        if ! [[ "$value" =~ ^[0-9]+$ ]]; then
            log_error "Variable $var should be an integer. Provided value: $value"
            fn_display_message
            exit 1
        fi
    fi

    # Check if variable is a directory and validate if it's exist

    if [[ "$var" == "DB_BINARY_PATH" || "$var" == "OUTPUT_DIRECTORY" ]]; then
        if [ ! -d "$value" ]; then
            log_error "Provided directory path for Variable $var does not exist. Provided value: $value"
            fn_display_message
            exit 1
        fi
    fi


    export "$var"="$value"
    
done

fn_initialize

log_info "Check whether assessment SQL file exists or not!!!" 
if [ ! -f ${SQL_FILE} ]; then
	log_error "Premigration assessment SQL file does not exist.\n"
	log_msg "\tSQL File\t: ${SQL_FILE}\n"
	exit 1
else
    log_info "SQL file exists!!!"
fi;

log_info "Creating log directory!!!"

fn_generate_logdir

log_info "Writing the configuration settings to log!!!\n"  | tee -a ${scriptlog}

jq -r '.variables | to_entries[] | "\(.key)=\(.value.value)"' config.json | grep -v DB_PASSWORD >> ${scriptlog}
log_msg "SQL_FILE=${SQL_FILE}\n" >> ${scriptlog}





log_info "Starting the Premigration Assessment for the following database:\n\n\tDB Host\t: $DB_ENDPOINT\n" | tee -a ${scriptlog}


log_info "Executing the Assessment SQL file!!!\n" | tee -a ${scriptlog}
# Run SQL file and capture only JSON results
MYSQL_PWD="$DB_PASSWORD" "${DB_BINARY_PATH}/mysql" -A --host="$DB_ENDPOINT" --user="$DB_USERNAME" --port="$DB_PORT" --skip-column-names  < ${SQL_FILE}  1> ${stdout} 2> ${stderr}

if [ $? -eq 0 ]; then
   
    log_info "Successfully executed the assessment SQL script.\n" | tee -a ${scriptlog}
    cat ${stdout} > ${json_report_file}
else
    log_error "Failed to execute the assessment SQL script. Please check the error.\n" | tee -a ${scriptlog}
    cat ${stderr} | tee -a ${scriptlog}
   
    fn_display_message
    exit 1
fi


log_info "Premigration assessment has been completed!!!" | tee -a ${scriptlog}
fn_display_message

log_msg "\n\n###########################################################\n###                        End                          ###\n###########################################################\n" | tee -a ${scriptlog}
