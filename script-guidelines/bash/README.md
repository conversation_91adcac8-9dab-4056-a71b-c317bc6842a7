
# Bash Scripting Best Practices

This document outlines best practices, common patterns, and reusable functions for writing production-ready Bash scripts for engineering use at Tessell.

---

## 📁 Project Organization

- Store all Bash scripts in `bash/scripts/`
- Use `.sh` extension for clarity
- Name files and functions using `snake_case`
- Separate reusable logic and config where appropriate

---

## ✅ Best Practices Summary

| Category           | Best Practice                                                                 |
|--------------------|------------------------------------------------------------------------------|
| **Shebang**        | Start scripts with `#!/usr/bin/env bash`                                     |
| **Permissions**    | Make scripts executable using `chmod +x script.sh`                           |
| **Quoting**        | Always quote variables: `"$var"`                                             |
| **Error Handling** | Use `set -euo pipefail` and `trap`                                           |
| **Logging**        | Use standardized `log_info`, `log_error`, etc.                               |
| **Modularity**     | Write reusable and single-purpose functions                                  |
| **Validation**     | Check all arguments and inputs                                               |
| **Temp Files**     | Use `mktemp` for safe temporary file creation                                |
| **Linting**        | Use `shellcheck` for script validation                                       |

---

## 🔧 Logging & Error Functions (Reusable Snippet)

```bash
log_info()  { echo -e "\033[1;32m[INFO]\033[0m $*"; }   # Green for general information
log_warn()  { echo -e "\033[1;33m[WARN]\033[0m $*"; }   # Yellow for warnings
log_error() { echo -e "\033[1;31m[ERROR]\033[0m $*"; }  # Red for errors

exit_on_error() {
  log_error "$1"
  exit 1
}
```

These are standard logging functions you should include in all scripts.

---

## 🚀 Starter Template Block

All new scripts should start with this:

```bash
#!/usr/bin/env bash
set -euo pipefail
trap 'log_error "Script failed at line $LINENO"; exit 1' ERR

log_info()  { echo -e "\033[1;32m[INFO]\033[0m $*"; }
log_warn()  { echo -e "\033[1;33m[WARN]\033[0m $*"; }
log_error() { echo -e "\033[1;31m[ERROR]\033[0m $*"; }
exit_on_error() { log_error "$1"; exit 1; }

main() {
  [[ $# -lt 1 ]] && exit_on_error "Missing required argument"
  log_info "Script started with argument: $1"
  # Add script logic here
}

main "$@"
```

---


## 🆘 Usage / Help Function Template

```bash
usage() {
  cat <<EOF

Usage: deploy_app.sh [OPTIONS] <ENVIRONMENT> <APP_NAME>

Description:
  This script deploys the specified application to the chosen environment using preconfigured deployment pipelines.

Arguments:
  ENVIRONMENT     Target environment to deploy to (e.g., dev, staging, prod)
  APP_NAME        Name of the application (e.g., inventory-service)

Options:
  -r, --rollback        Trigger a rollback instead of a deployment
  -c, --config <file>   Specify a custom configuration file
  -h, --help            Show this help message and exit

Examples:
  # Deploy inventory-service to staging
  deploy_app.sh staging inventory-service

  # Deploy with a specific config
  deploy_app.sh -c ./custom.yml prod inventory-service

  # Rollback the last deployment in dev
  deploy_app.sh --rollback dev inventory-service

EOF
  exit 1
}
```

---

## 🛡 Safe File Handling

### ❌ Don't do this:

```bash
rm -rf "$user_input"
```

If `$user_input` is empty or misformatted, it could delete the current directory or worse.

### ✅ Safer approach:

```bash
if [[ -n "${dir:-}" && -d "$dir" && "$dir" == /tmp/* ]]; then
  log_info "Deleting directory: $dir"
  rm -rf "$dir"
else
  exit_on_error "Invalid or unsafe directory path: $dir"
fi
```

This ensures deletion only happens for known-safe paths.

---

## 🔁 Loop Safety

Use `read -r` and always quote variables:

```bash
find . -type f -name "*.log" | while read -r file; do
  log_info "Processing $file"
done
```

---

## 🧪 Temporary Files

```bash
tmpfile=$(mktemp)
echo "Hello" > "$tmpfile"
```

`mktemp` avoids name collisions and insecure file naming.

---

## 📜 Example Script: `backup_file.sh`

```bash
#!/usr/bin/env bash
set -euo pipefail
trap 'log_error "Script failed at line $LINENO"; exit 1' ERR

log_info()  { echo -e "\033[1;32m[INFO]\033[0m $*"; }
log_warn()  { echo -e "\033[1;33m[WARN]\033[0m $*"; }
log_error() { echo -e "\033[1;31m[ERROR]\033[0m $*"; }
exit_on_error() { log_error "$1"; exit 1; }

backup_file() {
  local file="$1"
  [[ ! -f "$file" ]] && exit_on_error "File not found: $file"
  local timestamp
  timestamp=$(date +"%Y%m%d_%H%M%S")
  cp "$file" "${file}.${timestamp}.bak"
  log_info "Backup created for $file"
}

main() {
  [[ $# -lt 1 ]] && exit_on_error "Usage: $0 <file_to_backup>"
  local file="$1"
  log_info "Starting backup for $file"
  backup_file "$file"
  log_info "Backup completed."
}

main "$@"
```

---

## 📌 Tips

- Keep scripts short and purpose-driven
- Use `.env` or external config for environment-dependent logic
- Avoid `eval`, unvalidated input, or wildcard expansion without quoting

---

## 🔍 Tools & References

- [ShellCheck – Lint your scripts](https://www.shellcheck.net)
- [Advanced Bash Guide](https://tldp.org/LDP/abs/html/)

---

Happy scripting! 🐚
