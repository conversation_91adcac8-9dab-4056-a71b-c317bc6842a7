# 🧠 Automation Framework for DBaaS Company

## 1. Overview
- DBaaS-focused, modular, scalable automation
- Engines: Oracle, PostgreSQL, MySQL, SQL Server
- Clouds: AWS, Azure, GCP
- Tools: Python, Bash, Terraform, Ansible
- CI/CD ready, secure, reusable

## 2. 🔍 Data Collection
- Utilization Analysis
  - CPU
  - Memory
  - IOPS
  - Throughput
  - Connections
- Performance Analysis
  - Query stats
  - TPS
  - Index usage
  - Wait events
- Sources
  - Cloud-managed (RDS, EC2, Azure SQL)
  - Self-managed (OEM, AWR, CLI tools)
  - Third-party (Datadog, Prometheus, PMM)
  - DB Metadata
    - Version
    - Storage
    - Params
    - Users/Roles

## 3. 📏 Sizing Engine
- Purpose: Recommend VM shapes & capacity
- Process:
  - Normalize input
  - Aggregate historical usage
  - Map to profiles
- Modes:
  - Single-service
  - Multi-service
- Output:
  - YAML/CSV
  - Cost estimates
  - License needs

## 4. 🧰 DBA Daily Tasks
- Monitoring
  - Replication checks
  - HA sync validation
- Cleanup
  - FRA cleanup
  - Disk cleanup
- Operational Control
  - Unquiesce DB
  - Pause dbrolemonitor
- Config & Extensions
  - Enable alert/logs
  - Install extensions (PG/MySQL)
- Structure:
  - `<engine>/<major>/<minor>/<script>`
  - Supports `.sh`, `.ps1`

## 5. ☁️ Cloud SRE Tasks
- Categories:
  - Network: SG, DNS, NAT
  - Compute: VM state, burst credit
  - Storage: IOPS, volume state
  - Security: IAM, drift detection
  - Cost: Idle VMs, RI detection
- Structure:
  - `task_name/aws|azure|gcp/script.py`

## 6. 🛠 Product SRE Tasks
- Checks:
  - Metadata drift
  - K8s pod/service health
  - Terraform state drift
  - Secret & inventory validation
- Structure:
  - `major_task/minor_task/cloud_or_engine/script`
  - Example:
    - `governance/iam/aws/validate.py`
    - `governance/db-governance/oracle/audit_users.sh`

## 7. 🔄 Migrations (Upcoming)
- Support for Oracle, PG, MySQL, MSSQL
- Covers:
  - Precheck
  - Data movement
  - Validation
  - Rollback plan

## 8. ✅ Best Practices
- Cross-platform scripts (Linux + Windows)
- README in each:
  - Major task folder
  - Minor task folder
- Python scripts:
  - Use Poetry
  - Use `pyproject.toml`
  - Store dependencies in `vendor_envs/python/`
- Use `.ini` or `.env` inputs
- Logs: Structured + JSON
- Scripts must be:
  - Retry-safe
  - Idempotent
  - CI/CD compatible